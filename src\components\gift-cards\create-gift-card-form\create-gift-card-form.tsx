import { CustomCopyButton } from '@/components/common/custom-copy-button';
import SubmitButton from '@/components/common/submit-button';
import { SelectCurrencyCard } from '@/components/currency/select-currency-card';
import { getCurrenciesQuery } from '@/store/currencies';
import { Prism } from '@mantine/prism';

import {
  CreateGiftCardApiRequest,
  createGiftCardMutation,
  GiftCardsApiResponse,
} from '@/store/gift-card';
import { createGiftCardApiRequestSchema } from '@/store/gift-card/request-transformer';
import { UserApiResponse } from '@/store/user';
import { numberInputFormatter } from '@/utils';
import {
  Alert,
  Box,
  Card,
  Group,
  Image,
  NumberInput,
  Paper,
  Stack,
  Text,
} from '@mantine/core';
import { useForm, zodResolver } from '@mantine/form';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import useTranslation from 'next-translate/useTranslation';
import { eqBy, prop, unionWith } from 'ramda';
import React, { useState } from 'react';
import CreateGiftCardsSkeleton from './create-gift-card-form-skeleton';
import { IconAlertCircle } from '@tabler/icons-react';
import { useCaptcha } from '@/hooks';
import { Captcha } from '@/components/common/captcha';
import MinMaxAmountTransfer from '@/components/transfer/transfer-form/min-max-anount';
import currency from 'currency.js';

interface CreateGiftCardFormProps {
  userData: UserApiResponse | undefined;
}
// eslint-disable-next-line sonarjs/cognitive-complexity
function CreateGiftCardForm(props: CreateGiftCardFormProps) {
  const { userData } = props;
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [selectedItem, setSelectedItem] = useState<UserApiResponse['currencies'][0]>();
  const [newCode, setNewCode] = useState<GiftCardsApiResponse['data'][0]>();
  const [isLoading, setLoading] = useState(false);
  const allCurrencies = useQuery(getCurrenciesQuery({}));
  const { execute, reCaptchaRef, reset } = useCaptcha();
  const mergedCurrencies = unionWith(
    eqBy(prop('id')),
    userData?.currencies ?? [],
    allCurrencies?.data?.data ?? [],
  );
  const form = useForm<CreateGiftCardApiRequest>({
    initialValues: {
      amount: '',
      currencyCode: '',
    },
    validate: zodResolver(createGiftCardApiRequestSchema),
  });

  const { mutate } = useMutation({
    ...createGiftCardMutation(),
    onSuccess: (res) => {
      setSelectedItem(undefined);
      form.reset();
      if (res) setNewCode(res);
      queryClient.invalidateQueries({ queryKey: ['gifts'] });
      reset();
      setLoading(false);
    },
    onError: () => {
      reset();
      setLoading(false);
    },
  });

  const onSubmit = (values: CreateGiftCardApiRequest) => {
    setLoading(true);

    if (reCaptchaRef.current) {
      execute((token) => mutate({
        body: values,
        chaKey: token,
      }));
    } else {
      mutate({ body: values });
    }
  };

  // return boolean if the amount was more than max amount or less than min amount or more than the user balance.
  const minMaxError = () => {
    const transferMin = selectedItem?.transferMin;
    const transferMax = selectedItem?.transferMax;
    const amount = selectedItem?.amount ?? 0;

    let isError = false;
    if (typeof form.values.amount === 'number' && form.values.amount > 0) {
      if (transferMin && form?.values?.amount < transferMin) isError = true;
      else if (
        (transferMax && form?.values?.amount > transferMax)
        || form?.values?.amount > amount
      ) {
        isError = true;
      } else isError = false;
    }
    return isError;
  };
  return allCurrencies.isLoading ? (
    <CreateGiftCardsSkeleton />
  ) : (
    <>
      <Paper withBorder py="xl" px="md" radius="lg">
        <Text mb="lg">{t('common:createNewGiftCard')}</Text>
        <form onSubmit={form.onSubmit(onSubmit)}>
          <Stack>
            <Paper px="sm" py={5} withBorder radius={50}>
              <SelectCurrencyCard
                type="select"
                data={mergedCurrencies ?? []}
                setSelectedItem={(v) => {
                  setSelectedItem(v);
                  form.setFieldValue('currencyCode', v?.code);
                  form.setFieldValue('amount', 0);
                }}
                selectedItem={selectedItem}
                value={selectedItem?.value as string}
              >
                {!selectedItem ? (
                  <Group align="center" h="100%">
                    <Text color="dimmed" weight={500} tt="capitalize">
                      {t('common:selectCurrency')}
                    </Text>
                  </Group>
                ) : (
                  <Group h="100%" noWrap>
                    <Image
                      src={selectedItem?.image}
                      height={35}
                      width={35}
                      radius={50}
                      alt="currency"
                      w="100%"
                    />

                    <Text size="xl" color="dimmed">
                      {currency(selectedItem?.amount, {
                        precision: selectedItem?.precision ?? 0,
                        symbol: '',
                      }).format()}
                      {` ${selectedItem?.symbol}`}
                    </Text>
                  </Group>
                )}
              </SelectCurrencyCard>
            </Paper>
            <MinMaxAmountTransfer currencyItem={selectedItem} />
            <NumberInput
              radius="xl"
              hideControls
              precision={selectedItem?.precision ?? 2}
              formatter={numberInputFormatter}
              min={0}
              step={0}
              label={<Text tt="capitalize">{t('common:amount')}</Text>}
              {...form.getInputProps('amount')}
            />
            {minMaxError() && (
              <Text mx={2} color="red" size="sm">
                {t('common:minMaxErrorGift')}
              </Text>
            )}
            <SubmitButton
              radius="xl"
              loading={isLoading}
              fullWidth
              disabled={
                !form.values.amount
                || !form.values.currencyCode
                || minMaxError()
              }
              type="submit"
            >
              {t('common:createGiftCard')}
            </SubmitButton>
          </Stack>
        </form>

        {newCode && (
          <Box mt="xl">
            <Text>{t('common:newGiftCardCode')}</Text>
            <Card withBorder py="xs" px="xl" radius="xl" mb="lg">
              <Group position="apart">
                <Text size="md">{newCode?.code}</Text>
                <CustomCopyButton value={newCode?.code} />
              </Group>
            </Card>
            <Prism
              sx={{
                '& .prism-code': {
                  marginTop: '-10px',
                },
              }}
              language="bash"
              copyLabel={t('common:copy')}
              copiedLabel={t('common:copied')}
            >
              {`code:${newCode?.code}
currency:${newCode?.currency?.label}
amount:${currency(newCode?.amount, {
                symbol: '',
                precision: newCode?.currency?.precision ?? 0,
              }).format()}
uid:${newCode?.uid}
            `}
            </Prism>

            <Alert
              variant="light"
              mt="md"
              icon={<IconAlertCircle size="3rem" />}
              title={t('common:giftCardNoteLabel')}
              color="red"
            >
              {t('common:giftCardCodeNote')}
            </Alert>
          </Box>
        )}
      </Paper>
      <Captcha reCaptchaRef={reCaptchaRef} />
    </>
  );
}

export default CreateGiftCardForm;
