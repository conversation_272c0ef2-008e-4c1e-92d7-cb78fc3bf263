/**
 * currencies response schema
 */
import { globalPaginationBackendSchema } from '@/utils';
import { z } from 'zod';
// image attributes backend schema
export const imageBackedSchema = z.object({
  url: z.string(),
  formats: z
    .object({
      thumbnail: z.object({
        url: z.string(),
      }),
    })
    .nullable(),
});

export const currencyBackendSchema = z.object({
  id: z.number().optional(),
  uid: z.string(),
  name: z.string(),
  name_ar: z.string().nullable(),
  symbol: z.string().nullable(),
  code: z.string(),
  type: z.enum(['fiat', 'crypto', 'crypto_stable', 'fiat_stable']),
  active: z.boolean(),
  precision: z.number().nullable(),
  allow_exchange_from: z.boolean(),
  allow_exchange_to: z.boolean(),
  transfer_fees_percent: z.string(),
  transfer_fees_fixed: z.string(),
  exchange_fees_percent: z.string(),
  exchange_fees_fixed: z.string(),
  transfer_fees_min: z.string(),
  transfer_fees_max: z.string().nullable(),
  exchange_fees_min: z.string(),
  exchange_fees_max: z.string().nullable(),
  transfer_min: z.string(),
  transfer_max: z.string().nullable(),
  exchange_min: z.string(),
  exchange_max: z.string().nullable(),
  createdAt: z.string().nullable(),
  show_by_default: z.boolean().nullable(),
  order: z.number().nullable(),
  icon: z
    .object({
      url: z.string().optional(),
      data: z
        .object({
          attributes: imageBackedSchema,
        })
        .nullable()
        .optional(),
    })
    .nullable()
    .optional(),
});

export const currenciesBackendResponseSchema = z.array(
  z.object({
    id: z.number(),
    attributes: currencyBackendSchema,
  }),
);

export const currencyApiResponseSchema = (
  i: z.infer<typeof currencyBackendSchema>,
) => ({
  uid: i.uid,
  label: i.name,
  labelAr: i?.name_ar,
  symbol: i?.symbol,
  code: i.code,
  type: i.type,
  active: i.active,
  precision: i?.precision,
  allowExchangeFrom: i.allow_exchange_from,
  allowExchangeTo: i.allow_exchange_to,
  transferFeesPercent: +i.transfer_fees_percent,
  transferFeesFixed: +i.transfer_fees_fixed,
  exchangeFeesPercent: +i.exchange_fees_percent,
  exchangeFeesFixed: +i.exchange_fees_fixed,
  transferFeesMin: +i.transfer_fees_min,
  transferFeesMax: i?.transfer_fees_max ? +i.transfer_fees_max : null,
  exchangeFeesMin: +i.exchange_fees_min,
  exchangeFeesMax: i?.exchange_fees_max ? +i.exchange_fees_max : null,
  transferMin: +i.transfer_min,
  transferMax: i?.transfer_max ? +i.transfer_max : null,
  exchangeMin: +i.exchange_min,
  exchangeMax: i?.exchange_max ? +i.exchange_max : null,
  order: i?.order,
  image:
    i.icon?.data?.attributes.formats?.thumbnail.url
    ?? i.icon?.data?.attributes.url,
});

export const currenciesApiResponseSchema = z
  .object({
    data: currenciesBackendResponseSchema,
    meta: globalPaginationBackendSchema,
  })
  .transform(({ data, meta }) => ({
    data: data.map((item) => ({
      id: item.id,
      value: `${item.id}`,
      amount: 0,
      ...currencyApiResponseSchema(item.attributes),
    })),
    pagination: {
      page: meta.pagination?.page,
      pageSize: meta.pagination?.pageSize,
      pageCount: meta.pagination?.pageCount,
      total: meta.pagination.total,
    },
  }));

export const currencyTransfersPaymentsApiResponseSchema = (
  i: z.infer<typeof currencyBackendSchema>,
) => ({
  uid: i.uid,
  label: i.name,
  labelAr: i.name_ar,
  symbol: i?.symbol,
  code: i.code,
  precision: i?.precision,
  image:
    i.icon?.data?.attributes.formats?.thumbnail.url
    ?? i.icon?.data?.attributes.url,
  type: i.type,
});
