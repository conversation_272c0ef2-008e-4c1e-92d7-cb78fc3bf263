import { useState } from 'react';
import { ROUTES } from '@/data';
import { shareQRCode } from '../qr-helpers';

export const useQRGenerator = (accountId: string | undefined) => {
  const [generatedQR, setGeneratedQR] = useState('');

  const generateQR = () => {
    const params = new URLSearchParams();
    if (accountId) {
      params.append('accountId', accountId);
    }
    const url = `${window.location.origin}${ROUTES.transfer.path}?${params.toString()}`;
    setGeneratedQR(url);
  };

  const handleDownload = (qrWrapperRef: React.RefObject<HTMLDivElement>) => {
    const wrapper = qrWrapperRef.current;
    if (!wrapper) return;

    const qrCanvas = wrapper.querySelector('canvas');
    if (!(qrCanvas instanceof HTMLCanvasElement)) return;

    // Create a new canvas to combine QR code and account ID text
    const combinedCanvas = document.createElement('canvas');
    const ctx = combinedCanvas.getContext('2d');
    if (!ctx) return;

    // Set canvas dimensions with extra space for text
    const padding = 10;
    const textHeight = accountId ? 60 : 0;
    combinedCanvas.width = qrCanvas.width + (padding * 2);
    combinedCanvas.height = qrCanvas.height + (padding * 2) + textHeight;

    // Fill background
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, combinedCanvas.width, combinedCanvas.height);

    // Draw QR code
    ctx.drawImage(qrCanvas, padding, padding);

    // Add account ID text if available
    if (accountId) {
      ctx.fillStyle = '#79ca53';
      ctx.font = 'bold 30px monospace';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      const textY = qrCanvas.height + padding + (textHeight / 2);
      ctx.fillText(accountId, combinedCanvas.width / 2, textY);
    }

    // Download the combined image
    const pngUrl = combinedCanvas.toDataURL('image/png');
    const link = document.createElement('a');
    link.href = pngUrl;
    link.download = `qr-${accountId ?? 'code'}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleShare = async () => {
    if (accountId && generatedQR) {
      await shareQRCode(accountId, generatedQR);
    }
  };

  const resetQR = () => {
    setGeneratedQR('');
  };

  return {
    generatedQR,
    generateQR,
    handleDownload,
    handleShare,
    resetQR,
  };
};
