export const ROUTES = {
  logout: '/auth/logout',
  register: '/auth/register',
  root: {
    key: 'home',
    path: '/',
  },
  wallets: {
    key: 'wallets',
    path: '/wallets',
  },
  walletHistory: {
    key: 'walletHistory',
    path: '/wallets',
  },
  deposit: {
    key: 'deposit',
    path: '/deposit',
  },
  withdraw: {
    key: 'withdraw',
    path: '/withdraw',
  },
  exchange: {
    key: 'exchange',
    path: '/exchange',
  },
  myAccount: {
    key: 'settings',
    path: '/account',
  },
  transfer: {
    key: 'transfer',
    path: '/transfer',
  },
  massPayout: {
    key: 'massPayout',
    path: '/mass-payout',
  },
  rates: {
    key: 'rates',
    path: '/rates',
  },
  history: {
    key: 'history',
    path: '/history',
  },
  paymentLink: {
    key: 'paymentLink',
    path: '/payment-link',
  },
  privacyPolicy: {
    key: 'privacyPolicy',
    path: '/privacy-policy',
  },
  termsOfService: {
    key: 'termsOfService',
    path: '/terms-of-service',
  },
  paymentLinkApiUsage: {
    key: 'paymentLinkApiUsage',
    path: '/payment-link-api-usage',
  },
  merchant: {
    key: 'merchant',
    path: '/merchant',
  },
  iframeDeposit: {
    key: 'deposit',
    path: '/iframe/deposit',
  },
  iframeHistory: {
    key: 'history',
    path: '/iframe/history',
  },
  iframeLogin: {
    key: 'login',
    path: '/iframe/login',
  },
  iframeNotifications: {
    key: 'notifications',
    path: '/iframe/notifications',
  },
  help: {
    key: 'helpCenter',
    path: '/help',
  },
  merchants: {
    key: 'merchants',
    path: '/merchants',
  },
  accountBlocked: {
    key: 'accountBlocked',
    path: '/account-blocked',
  },
  accountBlockedChangIp: {
    key: 'accountBlockedChangeIp',
    path: '/account-blocked/change-ip',
  },
  redeemTerms: {
    key: 'redeemTerms',
    path: '/redeem-terms',
  },
  agentDeposit: {
    key: 'agentDeposit',
    path: '/agent-deposit',
  },
  agent: {
    key: 'agent',
    path: '/agents',
  },
  flexiCard: {
    key: 'flexiCard',
    path: '/flexi-card',
  },
};

export const authPages = [
  ROUTES.wallets.path,
  `${ROUTES.wallets.path}/[slug]`,
  ROUTES.walletHistory.path,
  ROUTES.deposit.path,
  ROUTES.withdraw.path,
  ROUTES.exchange.path,
  ROUTES.myAccount.path,
  ROUTES.transfer.path,
  ROUTES.massPayout.path,
  ROUTES.history.path,
  ROUTES.agentDeposit.path,
  ROUTES.flexiCard.path,
  ROUTES.logout,
];

export const iframePages = [
  ROUTES.iframeDeposit.path,
  ROUTES.iframeHistory.path,
  ROUTES.iframeNotifications.path,
];
