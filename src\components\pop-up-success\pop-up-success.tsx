/* eslint-disable max-lines */
import { PaymentsApiResponse } from '@/store/payment';
import {
  Badge,
  Button,
  Group,
  Image,
  Modal,
  ScrollArea,
  Skeleton,
  Stack,
  Text,
  useMantineTheme,
} from '@mantine/core';
import { useDisclosure, useMediaQuery } from '@mantine/hooks';
import dayjs from 'dayjs';
import useTranslation from 'next-translate/useTranslation';

import currency from 'currency.js';

import { statusCases } from '@/utils/operations-utils/operation-status';
import { operationIcon } from '@/utils/operations-utils/operation-icons';

import TransferDetails from './transfer-details';
import ExchangeDetails from './exchange-details';
import { CustomCopyButton } from '../common/custom-copy-button';
import { Icon } from '../common/icon';
import { Item } from './pop-up-item';
import { RenderCustomFields } from '../custom-fields/render-custom-fields';
import { CryptoPaymentDetails } from '../crypto-payment-details';
import { TextRenderHtml } from '../common/text-with-html-value';
import classes from './style.module.css';
import Link from 'next/link';
import { ROUTES } from '@/data';
import { AdminMessageAlert } from '../admin-message-alert';
import { TranslatedTextValue } from '../common/translated-text-value';

interface Props {
  operationType: 'transfer' | 'exchange' | 'deposit' | 'withdraw';
  operationId: null | string;
  amount: number;
  actualAmount: number;
  fees: number | null;
  status: PaymentsApiResponse['data'][0]['status'] | undefined;
  note: string | undefined | null;
  date: string;
  sender: string | undefined;
  receiver: string | undefined;
  currencyCode: string | undefined;
  currencySymbol: string | undefined | null;
  currencyToSymbol: string | undefined | null;
  currencyToCode: string | undefined;
  paymentMethod: PaymentsApiResponse['data'][0]['paymentMethod'] | undefined;
  setResponseData: (v: undefined) => void;
  fields: PaymentsApiResponse['data'][0]['fields'] | undefined;
  rate: number;
  precision: number | null;
  currencyToPrecision: number | null;
  gif: null | string;
  cryptoGetaway: PaymentsApiResponse['data'][0]['cryptoGetaway'] | undefined;
  originPaymentAmount:
    | PaymentsApiResponse['data'][0]['originPaymentAmount']
    | undefined;
  isLoading: boolean;
  adminMessage: string | undefined | null;
  // eslint-disable-next-line  react/require-default-props
  onClose?: () => void;
}
// eslint-disable-next-line complexity
export default function PopUpSuccess({
  actualAmount,
  amount,
  currencyCode,
  currencyToCode,
  date,
  fees,
  note,
  paymentMethod,
  receiver,
  sender,
  status,
  operationId,
  currencySymbol,
  currencyToSymbol,
  setResponseData,
  operationType,
  fields,
  rate,
  precision,
  currencyToPrecision,
  gif,
  cryptoGetaway,
  originPaymentAmount,
  isLoading,
  adminMessage,
  onClose = () => {},
}: Props) {
  const [opened, { close }] = useDisclosure(true);
  const { t } = useTranslation();
  const theme = useMantineTheme();
  const matches = useMediaQuery('(max-width: 34em)');
  const isDepositOperation = operationType === 'deposit';
  const isWithdrawOperation = operationType === 'withdraw';
  const isTransferOperation = operationType === 'transfer';
  // function ti return operation name translation key by type pf operation
  const handleTranslation = () => {
    let translation = '';
    if (isTransferOperation) translation = 'transferSuccess';
    else if (operationType === 'exchange') translation = 'exchangeSuccess';
    else if (isDepositOperation) translation = 'depositSuccess';
    else translation = 'withdrawSuccess';
    return translation;
  };
  // return precision if founded or 2 as default
  const newPrecision = precision ?? 2;
  // return fee if founded or set 0 as default
  const newFees = fees ?? 0;
  // return actual amount after format number with precision use "currency js"
  const actualAmountValue = currency(actualAmount, {
    symbol: '',
    precision: newPrecision,
  }).format();
  // return fees after format number with precision use "currency js"
  const feesValue = currency(newFees, {
    symbol: '',
    precision: newPrecision,
  }).format();
  // return amount after format number with precision use "currency js"
  const amountValue = currency(amount, {
    symbol: '',
    precision: newPrecision,
  }).format();
  // handle modal closing
  const onModalClose = () => {
    close();
    setResponseData(undefined);
    onClose();
  };
  // return origin amount returned when currency type is "crypto" after formate the number with precision use "currency js".
  const originPaymentAmountValue = currency(originPaymentAmount ?? 0, {
    symbol: '',
    precision: newPrecision,
  }).format();
  // boolean to return true if the currency is "crypto" to display crypto details
  const isCryptoPayment = paymentMethod?.tag === 'cryptocurrency'
    && isDepositOperation
    && cryptoGetaway;
  // return color by operation type
  const returnColor = isWithdrawOperation ? 'red' : 'green';
  const isPaymentOperationType = isDepositOperation || isWithdrawOperation;
  return (
    <div>
      <Modal
        fullScreen={matches}
        radius={30}
        opened={opened}
        scrollAreaComponent={ScrollArea.Autosize}
        onClose={onModalClose}
        title={
          isLoading ? (
            <Group spacing={2}>
              <Skeleton w={10} h={10} radius={10} />
              <Skeleton w={10} h={10} radius={10} />
              <Skeleton w={10} h={10} radius={10} />
            </Group>
          ) : (
            ''
          )
        }
        centered
        classNames={{
          content: matches ? classes.content : '',
        }}
        sx={{
          '& .mantine-ltr-Modal-close ,& .mantine-rtl-Modal-close': {
            border: '1px solid',
            borderRadius: 8,
            width: 25,
            height: 25,
            color: theme.colorScheme === 'dark' ? '#FFF' : '#000',
          },
        }}
      >
        <Stack pl={matches ? 'xl' : '0'}>
          {!isPaymentOperationType && (
            <>
              <Text size="lg" weight={500} mx="auto">
                {t(`common:${handleTranslation()}`)}
              </Text>
              <Badge
                mx="auto"
                p={0}
                w={50}
                h={50}
                sx={{ borderWidth: '0.2rem' }}
                variant="outline"
                radius={50}
                color="blue"
              >
                <Group>
                  <Icon icon="check" size={36} color="dark" />
                </Group>
              </Badge>
            </>
          )}
          {isCryptoPayment && (
            <CryptoPaymentDetails
              amount={cryptoGetaway?.amount}
              currencyCode={cryptoGetaway?.currency}
              address={cryptoGetaway?.address}
              network={cryptoGetaway?.network}
              memo={cryptoGetaway?.memo}
              validityDate={cryptoGetaway?.validity}
              qrCode={cryptoGetaway?.qrCode}
              status={status}
              createdAt={date}
            />
          )}

          {isTransferOperation && (
            <TransferDetails
              currencyCode={currencyCode}
              currencySymbol={currencySymbol}
              amount={amount}
              actualAmount={actualAmount}
              precision={newPrecision}
              fees={newFees}
              sender={sender}
              receiver={receiver}
            />
          )}
          {operationType === 'exchange' && (
            <ExchangeDetails
              currencyCode={currencyCode}
              currencyToCode={currencyToCode}
              currencySymbol={currencySymbol}
              currencyToSymbol={currencyToSymbol}
              amount={amount}
              actualAmount={actualAmount}
              precision={newPrecision}
              currencyToPrecision={currencyToPrecision ?? 2}
              fees={newFees}
              rate={rate}
            />
          )}

          {(isWithdrawOperation || isDepositOperation) && (
            <>
              <Item
                align="center"
                name={isWithdrawOperation ? 'toBePaid' : 'thePrice'}
                content={(
                  <div style={{ display: 'flex', gap: 5 }}>
                    <Text weight={500} color={returnColor} span>
                      {amountValue}
                    </Text>
                    <Text weight={500} color={returnColor} span>
                      {currencySymbol}
                    </Text>
                  </div>
                )}
              />
              {originPaymentAmount && (
                <Item
                  align="center"
                  name="originAmount"
                  content={(
                    <div style={{ display: 'flex', gap: 5 }}>
                      <Text weight={500} color={returnColor} span>
                        {originPaymentAmountValue}
                      </Text>
                      <Text weight={500} color={returnColor} span>
                        {currencySymbol}
                      </Text>
                    </div>
                  )}
                />
              )}
              <Item
                align="center"
                name="fees"
                content={<Text weight={500}>{feesValue}</Text>}
              />
              <Item
                align="center"
                name={isDepositOperation ? 'toBeDeposit' : 'toBeReceived'}
                content={(
                  <div style={{ display: 'flex', gap: 5 }}>
                    <Text weight={500} span>
                      {actualAmountValue}
                    </Text>
                    <Text weight={500} span>
                      {currencySymbol}
                    </Text>
                  </div>
                )}
              />
              {paymentMethod && (
                <Item
                  align="center"
                  name="paymentMethod"
                  content={(
                    <Text>
                      <TranslatedTextValue keyEn={paymentMethod?.label} keyAr={paymentMethod?.labelAr} />
                    </Text>
                  )}
                />
              )}
            </>
          )}
          <Group>
            <Item
              align="center"
              name="operationNo"
              content={<Text tt="uppercase">{operationId}</Text>}
            />
            <CustomCopyButton value={`${operationId}`} />
          </Group>

          <Item
            align="center"
            name="operationType"
            content={(
              <>
                <Icon
                  icon={operationIcon({ operationType })}
                  size={24}
                  color="gray"
                />
                <Text weight={500} tt="uppercase">
                  {t(`common:${operationType}`)}
                </Text>
              </>
            )}
          />
          <Item
            align="center"
            name="status"
            content={(
              <>
                <Badge
                  p={0}
                  w={25}
                  h={25}
                  sx={{ borderWidth: '0.15rem' }}
                  variant="outline"
                  radius={50}
                  color={statusCases({ status })?.color}
                >
                  <Group>
                    <Icon
                      icon={statusCases({ status })?.icon}
                      size={16}
                      color="dark"
                    />
                  </Group>
                </Badge>
                <Badge color={statusCases({ status })?.color}>
                  {t(`common:${status}`)}
                </Badge>
              </>
            )}
          />
          <Item
            align="center"
            name="date"
            content={
              <Text size="sm">{dayjs(date).format('YY-MM-DD hh:mm:ss A')}</Text>
            }
          />
          {adminMessage && <AdminMessageAlert adminMessage={adminMessage} />}
          {note && (
            <Item
              align="start"
              name="note"
              content={<TextRenderHtml text={note} />}
            />
          )}
          <RenderCustomFields fields={fields} />
          {gif && (
            <Group spacing={5} align="start">
              <Text weight={500} color="dimmed">
                Gif
              </Text>
              <Image src={gif} width={200} mx="auto" alt="gif" />
            </Group>
          )}
          <Button radius="lg" component={Link} href={ROUTES.wallets.path}>
            {t('common:gotoWallets')}
          </Button>
        </Stack>
      </Modal>
    </div>
  );
}
