import { apiEndpoints } from '@/data';
import { ApiClient } from '@/lib';
import { handleApiError } from '@/utils/api-utils/handle-backend-error';
import { CreateTransferApiRequest, getTransfersQueryProps } from './types';
import { QueryFunctionContext } from '@tanstack/react-query';

enum queryKeys {
  create = 'create',
  transfers = 'transfers',
}
/** ****************************************************** */
/**
 * @description function calls handler in "/transfers" api route with "post" method to create transfer.
 * If user enabled 2FA will get "qrCode" param if type of the transfer is "transfer" ,
 *  and will check code validity before create the transfer.
 * @param "body,qrCode and chaKey is a captcha token"
 * @returns created transfer details or error if failed
 */
const createTransferRequest = ({
  body,
  qrCode,
  chaKey,
}: {
  body: CreateTransferApiRequest;
  qrCode?: string;
  chaKey?: string;
}) => ApiClient.post(apiEndpoints.transfers(), body, {
  params: {
    qrCode,
    chaKey,
  },
})
  .then((res) => res.data)
  .catch((err) => {
    handleApiError(err);
    throw err;
  });

export const createTransferMutation = () => ({
  mutationKey: [queryKeys.create],
  mutationFn: ({
    body,
    qrCode,
    chaKey,
  }: {
    body: CreateTransferApiRequest;
    qrCode?: string;
    chaKey?: string;
  }) => createTransferRequest({ body, qrCode, chaKey }),
});
/** ****************************************************** */
/**
 * @description function call handler in "/transfers" api route with "get" method to fetch user transfers.
 * @param props
 * @returns list of transfer or error if failed
 */

const getTransfersRequest = (props: getTransfersQueryProps) => {
  const { pagination, filters, params } = props;
  return ApiClient.get(apiEndpoints.transfers(), {
    params: {
      page: params ? params.pageParam : pagination?.page,
      pageSize: pagination?.pageSize,
      start: pagination?.start,
      limit: pagination?.limit,
      eqType: filters?.eqType,
      nEqType: filters?.nEqType,
      currencyId: filters?.currencyId,
      search: filters?.search,
      received: filters?.received,
      sent: filters?.sent,
      isPayment: filters?.isPayment,
      isNotPayment: filters?.isNotPayment,
      createdFrom: filters?.createdFrom,
      createdTo: filters?.createdTo,
      status: filters?.status,
      paymentMethod: filters?.paymentMethod,
    },
  })
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e, true);
      throw e.response?.data;
    });
};
export const getTransfersQuery = (props: getTransfersQueryProps) => ({
  queryKey: [
    queryKeys.transfers,
    props?.filters,
    props?.pagination?.page,
    props?.pagination?.pageSize,
  ],
  queryFn: (params: QueryFunctionContext) => getTransfersRequest({ ...props, params }),
  refetchOnWindowFocus: false,
  // if the last loaded page from pagination pages is less than the pagination page count set next page is last page +1
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  getNextPageParam: (lastPage: any) => lastPage?.pagination?.page < lastPage?.pagination?.pageCount
    && lastPage.pagination.page + 1,
});
