import { MerchantsApiResponse } from '@/store/merchant/types';
import {
  Badge,
  Box,
  Button,
  Card,
  Group,
  Image,
  Stack,
  Text,
} from '@mantine/core';

import React from 'react';
import MerchantDetailsPopup from './merchant-details-popup';
import useTranslation from 'next-translate/useTranslation';

interface MerchantCardProps {
  merchant: MerchantsApiResponse['data'][0];
}
function MerchantCard({ merchant }: MerchantCardProps) {
  const {
    image, name, shortDescription, tag,
  } = merchant;
  const { t } = useTranslation();

  return (
    <Card
      shadow="sm"
      padding="lg"
      radius="lg"
      withBorder
      h={330}
      w={320}
      mx="auto"
    >
      <Stack h="100%" justify="space-between">
        <Box>
          <Card.Section h={150}>
            <Group position="center" w="100%" px="md">
              <Image src={image} height={150} alt="Norway" radius="lg" />
            </Group>
          </Card.Section>
          <Group position="apart" mt="md" mb="xs">
            <Text weight={500} tt="capitalize">
              {name}
            </Text>
            {tag.name && (
              <Badge color={tag.color} variant="light">
                {tag.name}
              </Badge>
            )}
          </Group>

          <Text h={45} size="sm" color="dimmed" lineClamp={2}>
            {shortDescription}
          </Text>
        </Box>

        <MerchantDetailsPopup
          merchant={merchant}
          modalAction={(
            <Button
              variant="filled"
              color="primary"
              fullWidth
              radius="md"
            >
              {t('common:viewMore')}
            </Button>
          )}
        />
      </Stack>
    </Card>
  );
}

export default MerchantCard;
