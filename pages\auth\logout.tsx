/**
 * This component renders a logout page.
 * @description
 * then click to logout button will delete the user session and clear react query cash memory
 * after click to logout button will navigate to landing page (root)
 * then click to cancel button will back to previous page
 */
import {
  Button, Container, Paper, Stack, Title,
} from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';
import { useState } from 'react';
import { useRouter } from 'next/router';
import MetaTags from '@/components/common/meta-tags';
import LogoBox from '@/components/logo-box';
import { useQueryClient } from '@tanstack/react-query';
import { useStyles } from '../../src/components/auth-pages-style/style';
import styles from './style.module.scss';
import { signOut } from 'next-auth/react';
import { ROUTES } from '@/data';

export default function Logout() {
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const { t } = useTranslation();
  const { classes } = useStyles();
  const queryClient = useQueryClient();
  const onLogoutButtonClick = async () => {
    queryClient.clear();
    setLoading(true);
    await signOut({ redirect: true, callbackUrl: ROUTES.root.path });
  };
  return (
    <div className={styles.root}>
      <MetaTags title={t('common:logout')} />
      <Container size={420} h="100vh">
        <Stack h="100vh" justify="center" spacing={0}>
          <Paper
            withBorder
            shadow="md"
            p={30}
            mt={30}
            radius="md"
            sx={{ textAlign: 'center' }}
          >
            <LogoBox h={100} w={200} />
            <Title align="center" fw={400} order={2}>
              {t('common:areYouSureYouWantToLogout')}
            </Title>
            <Button
              type="submit"
              fullWidth
              mt="xl"
              mb="md"
              loading={loading}
              onClick={onLogoutButtonClick}
            >
              {t('common:logout')}
            </Button>
            <Button
              variant="outline"
              fullWidth
              mt="xl"
              mb="md"
              disabled={loading}
              onClick={() => {
                router.back();
              }}
              className={classes.submitButton}
            >
              {t('common:cancel')}
            </Button>
          </Paper>
        </Stack>
      </Container>
    </div>
  );
}
