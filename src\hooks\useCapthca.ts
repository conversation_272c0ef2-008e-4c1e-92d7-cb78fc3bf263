import { useRef } from 'react';

export const useCaptcha = () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const reCaptchaRef = useRef<any>();

  const reset = () => reCaptchaRef.current?.reset();

  const execute = (fn: (tok: string) => void) => {
    reCaptchaRef.current.executeAsync().then((token: string) => {
      fn(token);
    });
  };

  return {
    reCaptchaRef,
    reset,
    execute,
  };
};
