import { Carousel } from '@mantine/carousel';
import { Image, Stack } from '@mantine/core';
import Autoplay from 'embla-carousel-autoplay';
import React, { useRef } from 'react';
import SlideSkeleton from './slideSkeleton';
import MerchantDetailsPopup from '../merchants-list/merchant-details-popup';
import { MerchantsApiResponse } from '@/store/merchant/types';
import { assetBaseUrl } from '@/data';

interface ImagesSliderProps {
  merchants: MerchantsApiResponse['data'];
}
function ImagesSlider({ merchants }: ImagesSliderProps) {
  const autoplay = useRef(Autoplay({ delay: 5000 }));
  const slides = merchants
    ?.filter((item) => item.featured)
    .map((filteredItem) => filteredItem);

  const staticBanners = [
    `${assetBaseUrl}/assets/merchants/banner1.webp`,
    `${assetBaseUrl}/assets/merchants/banner2.webp`,
  ];
  return (
    <Carousel
      slideSize="100%"
      slideGap="md"
      loop
      align="start"
      plugins={[autoplay.current]}
      onMouseEnter={autoplay.current.stop}
      onMouseLeave={autoplay.current.reset}
    >
      {!slides && (
        <Carousel.Slide>
          <SlideSkeleton />
        </Carousel.Slide>
      )}
      {slides?.map((slide) => (
        <Carousel.Slide key={slide.id}>
          <Stack h="100%" justify="center">
            <MerchantDetailsPopup
              merchant={slide}
              modalAction={(
                <Image
                  mx="auto"
                  mah={200}
                  maw={1000}
                  radius="lg"
                  src={slide?.banner}
                />
              )}
            />
          </Stack>
        </Carousel.Slide>
      ))}
      {staticBanners.map((banner) => (
        <Carousel.Slide key={banner}>
          <Stack h="100%" justify="center">
            <Image mx="auto" mah={200} maw={1000} radius="lg" src={banner} />
          </Stack>
        </Carousel.Slide>
      ))}
    </Carousel>
  );
}

export default ImagesSlider;
