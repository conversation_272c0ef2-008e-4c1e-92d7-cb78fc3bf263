/* eslint-disable @typescript-eslint/no-explicit-any */

import { Box, em, useMantineTheme } from '@mantine/core';
import { useViewportSize } from '@mantine/hooks';
import React from 'react';
import { ResponsiveComponentProps } from './types';
import { mergeDeepRight } from 'ramda';

function ResponsiveComponent<T extends(...args: any) => any>(
  props: ResponsiveComponentProps<T>) {
  const {
    props: childProps = {},
    component: Component,
    sharedProps = {},
    children = null,
  } = props;
  const theme = useMantineTheme();
  const { width } = useViewportSize();
  //* ***************************************************************** */
  const deepMerge = (styles: any[]) => styles.reduce(
    (pre, curr) => mergeDeepRight(pre || {}, curr || {}),
    {},
  );
  const renderStyles = (
    currentViewPort: number,
    style: ResponsiveComponentProps<any>['props'],
  ) => {
    const view = em(currentViewPort).replace('em', '');
    if (!style) return {};

    const {
      xl = {},
      lg = {},
      md = {},
      sm = {},
      xs = {},
      lowerThanXl = {},
      lowerThanLg = {},
      lowerThanMd = {},
      lowerThanSm = {},
    } = style;

    if (+view >= +theme.breakpoints.xl.replace('em', '')) {
      return deepMerge([lowerThanXl, xl]);
    }
    if (+view >= +theme.breakpoints.lg.replace('em', '')) {
      return deepMerge([lowerThanXl, lowerThanLg, lg]);
    }
    if (+view >= +theme.breakpoints.md.replace('em', '')) {
      return deepMerge([lowerThanXl, lowerThanLg, lowerThanMd, md]);
    }
    if (+view >= +theme.breakpoints.sm.replace('em', '')) {
      return deepMerge([
        lowerThanXl,
        lowerThanLg,
        lowerThanMd,
        lowerThanSm,
        sm,
      ]);
    }

    return deepMerge([lowerThanXl, lowerThanLg, lowerThanMd, lowerThanSm, xs]);
  };
  //* ***************************************************************** */
  const finalProps = mergeDeepRight(
    sharedProps || {},
    renderStyles(width, childProps) || {},
  );
  return (
    <Box>
      <Component {...{ ...finalProps, children }} />
    </Box>
  );
}
export default ResponsiveComponent;
