import { InferComponentProps } from '@/types/infer-component-props';
import { ReactNode } from 'react';

export type SchemeProps<T> = {
  dark?: T;
  light?: T;
};
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export interface ColorSchemeComponentProps<T extends (...args: any) => any> {
  props?: SchemeProps<Omit<InferComponentProps<T>, 'children'>>;
  sharedProps?: Omit<InferComponentProps<T>, 'children'>;
  children?: ReactNode;
  component: T;
}
