import {
  Tabs, Text, Group, Box,
} from '@mantine/core';

import { UseInfiniteQueryResult } from '@tanstack/react-query';
import TabDataContents from '../tab-data-contents';
import { ExportAsCsv } from '@/components/common/export-as-csv';
import { HistoryFilters } from '../history-filters';

export default function TapContent({
  accountId,
  title,
  dataQueryProps,
  dataType,
  value,
  transactionsType,
  filtersProps,
}: {
  accountId: string | undefined;
  title: string;
  dataQueryProps: UseInfiniteQueryResult;
  dataType: 'payment' | 'transfer' | 'all';
  value: string;
  transactionsType:
    | 'deposit'
    | 'withdraw'
    | 'transfer'
    | 'exchange'
    | 'received'
    | 'all';
  filtersProps: {
    currency: string | null;
    setCurrency: (v: string | null) => void;
    paymentMethod: string | null;
    setPaymentMethod: (v: string | null) => void;
    status: string | null;
    setStatus: (v: string | null) => void;
    createdFrom: string | null;
    setCreatedFrom: (v: string | null) => void;
    createdTo: string | null;
    setCreatedTo: (v: string | null) => void;
    schedule: string | null;
    setSchedule: (v: string | null) => void;
    filteredDataType: 'transfers' | 'payments' | 'all';
  };
}) {
  const { data, isLoading } = dataQueryProps;

  const returnDataToExportCsv = () => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const listData: any[] = [];
    if (data?.pages && data?.pages?.length > 0) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      data?.pages?.map((i: any) => listData.push(...i.data));
      return listData;
    }
    return listData;
  };

  return (
    <Tabs.Panel value={value} pt="xs">
      <Group position="apart">
        <Text tt="capitalize">{title}</Text>
        <Box display="flex" sx={{ gap: '10px' }}>
          <HistoryFilters {...filtersProps} />
          <ExportAsCsv
            data={returnDataToExportCsv()}
            transactionsType={transactionsType}
            isLoading={isLoading}
          />
        </Box>
      </Group>
      <TabDataContents
        userAccountId={accountId ?? ''}
        dataType={dataType}
        dataQueryProps={dataQueryProps}
        mixData={null}
      />
    </Tabs.Panel>
  );
}
