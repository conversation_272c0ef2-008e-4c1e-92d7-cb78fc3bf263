import {
  ActionIcon, Image, Popover, Text, Stack, Box,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { IconAlertCircle } from '@tabler/icons-react';

import React from 'react';

interface UploadNoteModalProps {
  image: string;
  message: string;
  showAsModal?: boolean;
}
function UploadNoteModal(props: UploadNoteModalProps) {
  const { image, message, showAsModal = true } = props;

  const [opened, { open, close }] = useDisclosure(false);

  // render as inline content below the field
  if (!showAsModal) {
    return (
      <Box
        mt="xs"
        p="xs"
        sx={(theme) => ({
          backgroundColor: theme.colorScheme === 'dark' ? theme.colors.dark[8] : theme.colors.gray[1],
          borderRadius: theme.radius.sm,
          border: `1px solid ${theme.colorScheme === 'dark' ? theme.colors.dark[6] : theme.colors.gray[2]}`,
        })}
      >
        <Text
          size="xs"
          color="dimmed"
          sx={{ lineHeight: 1.3, flex: 1 }}
        >
          {message}
        </Text>
      </Box>
    );
  }

  return (
    <div>
      <Popover
        width={400}
        position="bottom"
        withArrow
        shadow="md"
        radius="lg"
        opened={opened}
      >
        <Popover.Target>
          <ActionIcon
            size="sm"
            onClick={open}
            onMouseEnter={open}
            onMouseLeave={close}
          >
            <IconAlertCircle color="red" />
          </ActionIcon>
        </Popover.Target>
        <Popover.Dropdown sx={{ pointerEvents: 'none' }}>
          <Stack spacing="md" align="center">
            <Text align="center" sx={{ lineHeight: 1.4 }}>
              {message}
            </Text>
            <Box
              sx={(theme) => ({
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                padding: theme.spacing.sm,
                backgroundColor: theme.colorScheme === 'dark' ? theme.colors.dark[7] : theme.colors.gray[0],
                borderRadius: theme.radius.md,
                border: `1px solid ${theme.colorScheme === 'dark' ? theme.colors.dark[4] : theme.colors.gray[3]}`,
              })}
            >
              <Image
                src={image}
                alt="Requirement example"
                width={300}
                height={250}
                fit="contain"
                radius="sm"
              />
            </Box>
          </Stack>
        </Popover.Dropdown>
      </Popover>
    </div>
  );
}

export default UploadNoteModal;

UploadNoteModal.defaultProps = {
  showAsModal: true,
};
