import {
  Alert,
  Box,
  Button,
  Center,
  Collapse,
  Group,
  Text,
  Tooltip,
} from '@mantine/core';
import React from 'react';
import { Item } from '../pop-up-success/pop-up-item';
import { CustomCopyButton } from '../common/custom-copy-button';
import { CountDown } from '../payment-link/payment-link-details/payment-link-details-components';
import useTranslation from 'next-translate/useTranslation';
import QRCode from 'react-qr-code';
import { PaymentsApiResponse } from '@/store/payment';
import { CountdownRenderProps } from 'react-countdown';
import { useDisclosure } from '@mantine/hooks';
import MarkdownView from 'react-showdown';

interface Props {
  amount: number;
  currencyCode: string;
  address: string;
  network: string;
  memo: string | undefined;
  validityDate: number;
  qrCode: string;
  status: PaymentsApiResponse['data'][0]['status'] | undefined;
  createdAt: PaymentsApiResponse['data'][0]['createdAt'] | undefined;
}
function CryptoPaymentDetails({
  amount,
  currencyCode,
  address,
  network,
  memo,
  validityDate,
  qrCode,
  status,
  createdAt,
}: Props) {
  const { t } = useTranslation();
  const [opened, { toggle }] = useDisclosure(false);

  // Qr code uri contain address,amount and currency code
  const paymentURI = `${(
    currencyCode ?? ''
  ).toLowerCase()}:${address}?amount=${amount}`;
  // elapse address
  const elapsesAddress = () => {
    const firstSection = address.slice(0, 10);
    const lastSection = address.slice(address.length - 5, address.length);
    return `${firstSection}.....${lastSection}`;
  };
  // boolean variable return true if payment type is "approved" or "rejected"
  // if isCompleted is true then the qr code and address will not be displayed
  const isCompleted = status === 'approved';
  const isRejected = status === 'rejected';
  // render count down timer formate
  const renderTimer = (p: CountdownRenderProps) => (
    <Text size="lg" weight={500} color="dimmed">
      {p.formatted.days}
      d :
      {p.formatted.hours}
      h :
      {p.formatted.minutes}
      m :
      {p.formatted.seconds}
      s
    </Text>
  );
  // return validity payment date
  const returnValidityDate = () => {
    // max validity time = 12 hours
    const maxValidityTime = 12 * 60 * 60;
    // get created at date seconds
    const createdAtDateSeconds = createdAt ? new Date(createdAt).getTime() : 0;
    // get payment details validity seconds
    const paymentDetailsSeconds = validityDate
      ? (validityDate > maxValidityTime ? maxValidityTime : validityDate) * 1000
      : 0;
    // calculate number of validity seconds
    const validitySeconds = createdAtDateSeconds + paymentDetailsSeconds;

    // return validity data
    return new Date(validitySeconds);
  };
  return (
    <>
      {!isCompleted && (
        <>
          <Alert color="red">
            <MarkdownView
              style={{ fontSize: 18, fontWeight: 600, margin: '-20px 0' }}
              className="markdown"
              markdown={t('common:alertText')}
              options={{ tables: true, emoji: true }}
            />
          </Alert>
          <Group position="center" mb={5}>
            <Button color="red" onClick={toggle}>
              {t('common:showPaymentAddress')}
            </Button>
          </Group>

          <Collapse
            in={opened}
            transitionDuration={300}
            transitionTimingFunction="linear"
          >
            <Center>
              <Box pos="relative" mb={15} h={180} w={180}>
                <QRCode
                  size={180}
                  style={{
                    maxWidth: '100%',
                    width: '180px',
                    height: '180px',
                    margin: 'auto',
                  }}
                  value={qrCode !== '' ? qrCode : paymentURI}
                  viewBox="0 0 180 180"
                />

                <Box
                  opacity={0}
                  left={-20}
                  top={-20}
                  pos="absolute"
                  mb={15}
                  h={220}
                  w={220}
                  bg="red"
                />
              </Box>
            </Center>

            <Item
              align="center"
              name="address"
              content={(
                <Group spacing={0}>
                  <Tooltip label={address}>
                    <Text maw={150}>{elapsesAddress()}</Text>
                  </Tooltip>
                  <CustomCopyButton value={address} />
                </Group>
              )}
            />
          </Collapse>
        </>
      )}
      <Item
        align="center"
        name="network"
        content={(
          <Group spacing={0}>
            <Text>{network}</Text>
            <CustomCopyButton value={network} />
          </Group>
        )}
      />
      {memo && (
        <Item
          align="center"
          name="memo"
          content={(
            <Group spacing={0}>
              <Text>{memo}</Text>
              <CustomCopyButton value={memo} />
            </Group>
          )}
        />
      )}
      {!isCompleted && (
        <Item
          align="center"
          name="payIn"
          content={
            isRejected ? (
              <Text color="red">{t('common:expired')}</Text>
            ) : (
              <CountDown
                date={returnValidityDate()}
                autoStart
                renderer={renderTimer}
              />
            )
          }
        />
      )}
    </>
  );
}

export default CryptoPaymentDetails;
