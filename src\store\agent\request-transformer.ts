/**
 * create agent payment request schema
 */
import { Translate } from 'next-translate';
import { z } from 'zod';

export const createAgentPaymentApiRequestSchema = z.object({
  transactionId: z.string(),
  note: z.string().optional(),
  type: z.enum(['deposit']).or(z.string()),
  fields: z
    .array(
      z.object({
        type: z.string().nullable().optional(),
        value: z
          .string()
          .or(z.date())
          .or(z.number())
          .or(z.array(z.string()))
          .or(z.object({ id: z.number(), url: z.string(), name: z.string() }))
          .nullable(),
        name: z.string().nullable().optional(),
      }),
    )
    .optional(),
});

export const createAgentPaymentBackendRequestSchema = createAgentPaymentApiRequestSchema.transform((data) => ({
  data: {
    related_withdraw_id: data.transactionId,
    note: data?.note?.trim(),
    type: data.type,
    custom_fields: data?.fields?.map((i) => {
      if (typeof i?.value === 'string') {
        return {
          ...i,
          value: i.value.trim(),
        };
      }
      if (Array.isArray(i?.value)) {
        return {
          ...i,
          value: i.value.map((item) => item.trim()),
        };
      }
      return i;
    }),
  },
}));

const required = 'required';
export const createAgentRequestFormSchema = (t: Translate) => z.object({
  name: z.string().min(1, { message: t(`errors:${required}`) }),
  firstName: z.string().min(1, { message: t(`errors:${required}`) }),
  lastName: z.string().min(1, { message: t(`errors:${required}`) }),
  email: z
    .string()
    .min(1, { message: t(`errors:${required}`) })
    .email({ message: t('errors:invalidEmail') }),
  code: z.string().min(1, { message: t(`errors:${required}`) }),
  phone: z
    .string()
    .min(6, { message: t(`errors:${required}`) })
    .regex(/^\d*$/, t('errors:pleaseEnterValidNumbers')),
  jobType: z.string().min(1, { message: t(`errors:${required}`) }),
  address: z.string().min(1, { message: t(`errors:${required}`) }),
  location: z
    .string()
    .url({ message: t('errors:invalidUrl') })
    .min(1, { message: t(`errors:${required}`) }),
  currencies: z.array(z.string()).optional(),
  liquidity: z
    .number()
    .min(1, { message: t(`errors:${required}`) })
    .or(z.string().min(1, { message: t(`errors:${required}`) })),
  telegramOrWhatsapp: z
    .string()
    .min(1, { message: t(`errors:${required}`) })
    .nullable(),
});

export const createAgentRequestApiRequestSchema = z.object({
  name: z.string(),
  firstName: z.string(),
  lastName: z.string(),
  email: z.string(),
  code: z.string(),
  phone: z.string().regex(/^\d*$/, 'Please enter valid numbers'),
  jobType: z.string(),
  address: z.string(),
  location: z.string(),
  currencies: z.array(z.string()).optional(),
  liquidity: z.number().or(z.string()),
  telegramOrWhatsapp: z.string(),
  token: z.string().optional(),
});

export const createAgentRequestBackendRequestSchema = createAgentRequestApiRequestSchema.transform((data) => ({
  currencies: {
    multi_select: data.currencies?.map((c) => ({ name: c })),
  },
  location: {
    url: data.location,
  },
  phone: {
    rich_text: [
      {
        text: { content: data.phone },
      },
    ],
  },
  email: {
    email: data.email,
  },
  address: {
    rich_text: [
      {
        text: { content: data.address },
      },
    ],
  },
  first_name: {
    rich_text: [
      {
        text: { content: data.firstName },
      },
    ],
  },
  last_name: {
    rich_text: [
      {
        text: { content: data.lastName },
      },
    ],
  },
  job_type: {
    rich_text: [
      {
        text: { content: data.jobType },
      },
    ],
  },
  liquidity: {
    number: data.liquidity,
  },

  telegram_whatsapp: {
    rich_text: [
      {
        text: { content: data.telegramOrWhatsapp },
      },
    ],
  },
  name: {
    title: [
      {
        text: { content: data.name },
      },
    ],
  },
}));
