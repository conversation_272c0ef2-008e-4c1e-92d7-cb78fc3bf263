import {
  UpdateUserApiRequest,
  updateUserApiRequestSchema,
  updateUserMutation,
  UserApiResponse,
} from '@/store/user';
import {
  Button, Paper, Select, Stack, Text,
} from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useForm, zodResolver } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { Code2faSettings } from '@/components/2fa/2fa-code-settings';
import { preserveUserData } from '@/utils/profile-utils';

interface SecurityTabProps {
  data: UserApiResponse | undefined;
}
function SecurityTab({ data }: SecurityTabProps) {
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  const accountBlockedOptions = [
    { value: 'never', label: t('common:never') },
    { value: 'ip_change', label: t('common:ipChange') },
    { value: 'subnet_change', label: t('common:subnetChange') },
    { value: 'browser_change', label: t('common:browserChange') },
    { value: 'always', label: t('common:always') },
  ];
  const form = useForm<UpdateUserApiRequest>({
    initialValues: {
      sendVerificationConditions: data?.sendVerificationConditions,
    },
    validate: zodResolver(updateUserApiRequestSchema),
  });

  const { mutate, isLoading } = useMutation(updateUserMutation().mutationFn, {
    onSuccess() {
      queryClient.invalidateQueries(['user']);
      notifications.show({
        message: t('common:securitySettingsUpdateSuccess'),
        color: 'blue',
      });
      form.resetDirty();
    },
  });

  const submit = (values: UpdateUserApiRequest) => {
    mutate(preserveUserData(values, data));
  };
  return (
    <Stack spacing="lg" align="stretch" m="auto" w="100%" maw={650}>
      <Paper p="md" withBorder radius="lg">
        <form onSubmit={form.onSubmit(submit)}>
          <Select
            radius="lg"
            label={
              <Text tt="capitalize">{t('common:sendVerificationCode')}</Text>
            }
            data={accountBlockedOptions}
            {...form.getInputProps('sendVerificationConditions')}
          />

          <Button
            loading={isLoading}
            disabled={!form.isDirty()}
            mt={20}
            type="submit"
            fullWidth
            radius="lg"
          >
            {t('common:save')}
          </Button>
        </form>
      </Paper>
      <Paper p="md" withBorder radius="lg">
        <Text mb="sm">{t('common:googleAuthenticatorSettings')}</Text>
        <Code2faSettings data={data} />
      </Paper>
    </Stack>
  );
}

export default SecurityTab;
