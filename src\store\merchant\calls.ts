import { apiEndpoints } from '@/data';
import { ApiClient } from '@/lib';
import { CreateMerchantRequestApiRequest } from './types';
import { handleApiError } from '@/utils/api-utils/handle-backend-error';

enum queryKeys {
  create = 'create',
}
/**
 * @description function call handler in "/merchant" api route to create merchant request.
 * "token" param using to captcha validation.
 * @param body
 * @returns success of error if failed
 */
const createMerchantRequestRequest = (body: CreateMerchantRequestApiRequest) => ApiClient.post(apiEndpoints.merchant(), body)
  .then((res) => res.data)
  .catch((err) => {
    handleApiError(err);
    throw err;
  });

export const createMerchantRequestMutation = () => ({
  mutationKey: [queryKeys.create],
  mutationFn: createMerchantRequestRequest,
});
