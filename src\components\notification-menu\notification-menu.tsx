import React from 'react';
import { Menu } from '@mantine/core';

import NotificationsList from './notifications-list';

interface NotificationsMenuProps {
  target: React.ReactNode;
  handleOpen: () => void;
}
function NotificationsMenu(props: NotificationsMenuProps) {
  const { target, handleOpen } = props;

  return (
    <Menu shadow="md" width={320} radius="md" onOpen={handleOpen}>
      <Menu.Target>{target}</Menu.Target>
      <Menu.Dropdown pt="md" sx={{ paddingInlineStart: 0 }}>
        <NotificationsList height={300} />
      </Menu.Dropdown>
    </Menu>
  );
}

export default NotificationsMenu;
