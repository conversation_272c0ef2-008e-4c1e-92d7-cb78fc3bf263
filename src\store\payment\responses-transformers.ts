/**
 * payment response schema
 */
import { globalPaginationBackendSchema } from '@/utils';
import { z } from 'zod';
import {
  currencyBackendSchema,
  currencyTransfersPaymentsApiResponseSchema,
} from '../currencies';
import { imageBackedSchema } from '../currencies/responses-transformers';
/** ******************************************** */
// custom fields schema
const customFieldsSchema = z
  .object({
    type: z.string().nullable().optional(),
    value: z
      .string()
      .or(z.date())
      .or(z.number())
      .or(z.object({ id: z.number(), url: z.string(), name: z.string() }))
      .or(z.any())
      .nullable(),
    name: z.string().nullable().optional(),
    nameAr: z.string().nullable().optional(),
  })
  .optional();
const customFieldsApiSchema = (i: z.infer<typeof customFieldsSchema>) => ({
  name: i?.name,
  nameAr: i?.nameAr,
  type: i?.type,
  value: i?.value,
});
/** ******************************************** */
// payment backend schema
export const paymentBackendResponseSchema = z.object({
  id: z.number(),
  attributes: z.object({
    amount: z.string(),
    status: z.enum(['pending', 'processing', 'approved', 'rejected']),
    note: z.string().nullable(),
    meta: z.any().nullable(),
    admin_message: z.string().nullable(),
    type: z.enum(['withdraw', 'deposit']),
    createdAt: z.string(),
    updatedAt: z.string(),
    transaction_id: z.string(),
    total_fees: z.string().nullable(),
    actual_amount: z.string(),
    custom_fields: z.array(customFieldsSchema).nullable(),
    currency: z.object({
      data: z.object({
        id: z.number(),
        attributes: currencyBackendSchema,
      }),
    }),
    payment_method: z.object({
      data: z.object({
        id: z.number(),
        attributes: z.object({
          title: z.string(),
          title_ar: z.string().nullable(),
          tag: z.enum([
            'cryptocurrency',
            'ccpayment',
            'bank',
            'e-payment',
            'payeer',
            'perfect_money',
            'syriatel_credit',
            'syriatel_cash',
            'syriatel_invoice',
            'mtn_credit',
            'mtn_cash',
            'mtn_invoice',
            'syriatel_two_credit',
            'syriatel_two_cash',
            'syriatel_two_invoice',
            'mtn_two_credit',
            'mtn_two_cash',
            'mtn_two_invoice',
            'haram_glue',
            'haram',
            'haram_usd_cash',
            'haram_usd_cash_hama',
            'haram_usd_cash_homs',
            'haram_cash_damascus_zahera',
            'coinex',
            'cwallet',
            'binance',
            'bemo',
            'agent',
            'internal_agent',
          ]),
          icon: z
            .object({
              data: z
                .object({
                  id: z.number(),
                  attributes: imageBackedSchema,
                })
                .nullable(),
            })
            .optional(),
        }),
      }),
    }),
    user: z
      .object({
        data: z.object({
          id: z.number(),
          attributes: z.object({
            account_id: z.string(),
            email: z.string(),
            firstName: z.string().optional(),
            lastName: z.string().optional(),
          }),
        }),
      })
      .optional(),
    getway_response: z
      .object({
        cryptoResult: z
          .object({
            amount: z.string(),
            qrCode: z.string(),
            payAddress: z.string(),
            payNetwork: z.string(),
            orderValidPeriod: z.number(),
            paidCryptoCurrency: z.string(),
            memo: z.string().optional(),
          })
          .nullable()
          .optional(),
      })
      .nullable()
      .optional(),
    getway_webhook_response: z
      .array(
        z.object({
          webhookResult: z.object({
            originAmount: z.string(),
          }),
        }),
      )
      .nullable(),
  }),
});
// list payments
export const paymentsBackendResponseSchema = z.array(
  paymentBackendResponseSchema,
);
// payment front end schema
export const paymentApiResponseSchema = (
  item: z.infer<typeof paymentBackendResponseSchema>,
) => ({
  id: item.id,
  amount: +item.attributes.amount,
  status: item.attributes.status,
  note: item.attributes?.note,
  meta: item.attributes?.meta,
  adminMessage: item.attributes?.admin_message,
  type: item.attributes.type,
  transactionId: item.attributes.transaction_id,
  createdAt: item.attributes.createdAt,
  totalFees: item.attributes?.total_fees ? +item.attributes.total_fees : null,
  actualAmount: +item.attributes.actual_amount,
  fields: item.attributes?.custom_fields?.map((i) => customFieldsApiSchema(i)),
  currency: {
    ...currencyTransfersPaymentsApiResponseSchema(
      item.attributes.currency?.data.attributes,
    ),
  },
  paymentMethod: {
    label: item.attributes.payment_method.data.attributes.title,
    labelAr: item.attributes.payment_method.data.attributes?.title_ar,
    image:
      item.attributes.payment_method.data.attributes?.icon?.data?.attributes
        ?.formats?.thumbnail.url
      ?? item.attributes.payment_method.data.attributes?.icon?.data?.attributes
        .url,
    tag: item.attributes.payment_method.data.attributes.tag,
  },
  user: {
    accountId: item.attributes?.user?.data?.attributes?.account_id,
    email: item.attributes?.user?.data?.attributes?.email,
    fullName: `${item.attributes?.user?.data?.attributes?.firstName ?? ''} ${
      item.attributes?.user?.data?.attributes?.lastName ?? ''
    }`,
  },
  cryptoGetaway: item.attributes?.getway_response?.cryptoResult
    ? {
      amount: +item.attributes.getway_response.cryptoResult.amount,
      qrCode: item.attributes.getway_response.cryptoResult.qrCode,
      address: item.attributes.getway_response.cryptoResult.payAddress,
      network: item.attributes.getway_response.cryptoResult.payNetwork,
      currency:
          item.attributes.getway_response.cryptoResult.paidCryptoCurrency,
      validity: item.attributes.getway_response.cryptoResult.orderValidPeriod,
      memo: item.attributes.getway_response.cryptoResult?.memo,
    }
    : null,
  originPaymentAmount: item.attributes.getway_webhook_response
    ? +item.attributes.getway_webhook_response[0].webhookResult.originAmount
    : null,
});
// list payments with pagination
export const paymentsApiResponseSchema = z
  .object({
    data: paymentsBackendResponseSchema,
    meta: globalPaginationBackendSchema,
  })
  .transform(({ data, meta }) => ({
    data: data.map(paymentApiResponseSchema),
    pagination: {
      page: meta.pagination?.page,
      pageSize: meta.pagination?.pageSize,
      pageCount: meta.pagination?.pageCount,
      total: meta.pagination.total,
    },
  }));
