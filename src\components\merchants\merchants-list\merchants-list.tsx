import { Box, SimpleGrid, Text } from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';
import React from 'react';
import MerchantCard from './merchant-card';
import { MerchantsApiResponse } from '@/store/merchant/types';
import { EmptyData } from '@/components/common/empty-data';

interface MerchantsListProps {
  data: MerchantsApiResponse['data'];
}
function MerchantsList({ data }: MerchantsListProps) {
  const { t } = useTranslation();
  if (data?.length === 0) {
    return (
      <Box mt={20}>
        <Text mb={20}>{t('common:merchantsList')}</Text>
        <EmptyData message="" />
      </Box>
    );
  }
  return (
    <Box>
      <SimpleGrid
        mt={20}
        cols={3}
        breakpoints={[
          { maxWidth: 'lg', cols: 3, spacing: 'sm' },
          { maxWidth: 'md', cols: 2, spacing: 'sm' },
          { maxWidth: 'sm', cols: 1, spacing: 'sm' },
        ]}
      >
        {data?.map((merchant) => (
          <MerchantCard key={merchant.id} merchant={merchant} />
        ))}
      </SimpleGrid>
    </Box>
  );
}

export default MerchantsList;
