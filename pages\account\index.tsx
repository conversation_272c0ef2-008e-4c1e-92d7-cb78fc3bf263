/**
 * This component renders a user profile page and form to update user data.
 *
 */
import MetaTags from '@/components/common/meta-tags';
import { Layout } from '@/components/layout/layout';
import {
  Group, Loader, Stack, Tabs,
} from '@mantine/core';

import useTranslation from 'next-translate/useTranslation';

import { useQuery } from '@tanstack/react-query';
import { getUserQuery } from '@/store/user';
import ProfileSkeleton from '@/components/profile/profile-tab/profile-skeleton';
import { useState } from 'react';
import ProfileTab from '@/components/profile/profile-tab';
import SecurityTab from '@/components/profile/security-tab';
import MerchantTab from '@/components/profile/merchant-tab';
import { useMediaQuery } from '@mantine/hooks';
import { useRouter } from 'next/router';
import SecuritySkeleton from '@/components/profile/security-tab/security-skeleton';
import MerchantSkeleton from '@/components/profile/merchant-tab/merchant-skeleton';
import {
  IconBattery3,
  IconGift,
  IconLock,
  IconUser,
  IconUser<PERSON><PERSON><PERSON>,
  IconWebhook,
} from '@tabler/icons-react';
import { LimitsTab } from '@/components/profile/limits-tab';
import { GiftCardsSettingsTab } from '@/components/profile/gift-cards-settings-tab';
import { KycTab } from '@/components/profile/kyc-tab';
import { ReturnKYCBadge } from '@/components/profile/kyc-tab/return-kyc-tag';
// eslint-disable-next-line complexity
export default function Account() {
  const { t } = useTranslation();
  const { query, push } = useRouter();
  const { page } = query;
  const { data, isLoading } = useQuery({ ...getUserQuery({}) });
  const [activeTab, setActiveTab] = useState<string>(
    (page as string) ?? 'profile',
  );
  const handleTabChange = (tab: string | null) => {
    setActiveTab(tab ?? 'profile');
    push({
      query: {
        page: tab,
      },
    });
  };
  const isMerchant = data?.role === 'merchant';
  const hasBadges = data?.badges && data.badges?.length > 0;
  const matchesSmall = useMediaQuery('(max-width: 61.95em)');
  const matchesXSmall = useMediaQuery('(max-width: 27em)');

  return (
    <div>
      <MetaTags title={t('common:myAccount')} />
      <Layout>
        <Tabs
          mt="lg"
          value={activeTab}
          onTabChange={handleTabChange}
          defaultValue="gallery"
          orientation={matchesSmall ? 'horizontal' : 'vertical'}
          variant={matchesSmall ? 'default' : 'pills'}
          styles={{
            tabIcon: {
              display: matchesXSmall ? 'none' : '',
            },
          }}
        >
          <Tabs.List miw={200}>
            <Tabs.Tab icon={<IconUser />} value="profile">
              {t('common:profile')}
            </Tabs.Tab>
            <Tabs.Tab icon={<IconLock />} value="security">
              {t('common:security')}
            </Tabs.Tab>
            {isMerchant && (
              <Tabs.Tab icon={<IconWebhook />} value="merchant">
                {t('common:merchant')}
              </Tabs.Tab>
            )}
            {hasBadges && (
            <Tabs.Tab icon={<IconBattery3 />} value="limits">
              {t('common:limits')}
            </Tabs.Tab>
            )}
            <Tabs.Tab icon={<IconUserCheck />} value="kyc">
              <Group spacing="md" align="center">
                {t('common:KYC')}
                <ReturnKYCBadge kyc={data?.kyc} />
              </Group>
            </Tabs.Tab>
            <Tabs.Tab icon={<IconGift />} value="gifts">
              {t('common:giftCards')}
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel
            px={{ sm: 'xl' }}
            pt={{ base: 'md', md: 0 }}
            value="profile"
          >
            {isLoading ? <ProfileSkeleton /> : <ProfileTab data={data} />}
          </Tabs.Panel>
          <Tabs.Panel
            px={{ sm: 'xl' }}
            pt={{ base: 'md', md: 0 }}
            value="security"
          >
            {isLoading ? <SecuritySkeleton /> : <SecurityTab data={data} />}
          </Tabs.Panel>
          {isMerchant && (
            <Tabs.Panel
              px={{ sm: 'xl' }}
              pt={{ base: 'md', md: 0 }}
              value="merchant"
            >
              {isLoading ? <MerchantSkeleton /> : <MerchantTab data={data} />}
            </Tabs.Panel>
          )}
          {hasBadges && (
          <Tabs.Panel
            px={{ sm: 'xl' }}
            pt={{ base: 'md', md: 0 }}
            value="limits"
          >
            <LimitsTab badges={data?.badges} />
          </Tabs.Panel>
          )}
          <Tabs.Panel
            px={{ sm: 'xl' }}
            pt={{ base: 'md', md: 0 }}
            value="kyc"
          >
            {isLoading ? (
              <Stack mih={500} justify="center" align="center" w="100%">
                <Loader />
              </Stack>
            ) : (
              <KycTab data={data} />
            )}
          </Tabs.Panel>
          <Tabs.Panel
            px={{ sm: 'xl' }}
            pt={{ base: 'md', md: 0 }}
            value="gifts"
          >
            <GiftCardsSettingsTab isMerchant={isMerchant} />
          </Tabs.Panel>
        </Tabs>
      </Layout>
    </div>
  );
}

export async function getServerSideProps() {
  return { props: {} };
}
