//  function to calculate fees for transfer,withdraw and deposit operations

export function FeesTransferCalculate(
  feesFixed: number | null | undefined,
  feesPercentage: number | null | undefined,
  feesMin: number | null | undefined,
  feesMax: number | null | undefined,
  amount: number,
) {
  let fees = amount * ((feesPercentage ?? 0) / 100) + (feesFixed ?? 0);
  if (amount > 0) {
    if (feesMin && fees < feesMin) {
      fees = feesMin;
    } else if (feesMax && fees > feesMax) {
      fees = feesMax;
    }
  } else fees = 0;

  return fees;
}
