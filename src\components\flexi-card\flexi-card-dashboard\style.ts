/* eslint-disable @typescript-eslint/no-explicit-any */
import { createStyles, rem } from '@mantine/core';

export const useStyles = createStyles((theme) => ({
  carouselContainer: {
    width: '100%',
    overflow: 'hidden', // Prevent any overflow

    '& .mantine-Carousel-root': {
      width: '100%',
    },

    '& .mantine-Carousel-viewport': {
      overflow: 'hidden',
      width: '100%',
    },

    '& .mantine-Carousel-container': {
      display: 'flex',
      width: '100%',
    },

    '& .mantine-Carousel-slide': {
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      padding: '0 10px',
    },

    '& .mantine-Carousel-indicators': {
      gap: 8,
      bottom: -24,
    },
  },

  fixedCardShell: {
    width: '100%',
    maxWidth: rem(380),
    margin: '0 auto',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },

  balanceContainer: {
    borderRadius: rem(20),
    padding: theme.spacing.lg,
    backgroundColor: theme.colorScheme === 'dark' ? theme.colors.dark[6] : theme.white,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
    height: '100%',
    gap: theme.spacing.lg,

    [theme.fn.smallerThan('sm')]: {
      padding: theme.spacing.md,
      backgroundColor: 'transparent',
    },
  },

  menuItem: {
    padding: '12px 16px',
  },

  balanceAmount: {
    fontWeight: 800,
    lineHeight: 1,
    fontSize: rem(48),

    [theme.fn.smallerThan('md')]: {
      fontSize: rem(36),
    },

    [theme.fn.smallerThan('sm')]: {
      fontSize: rem(28),
    },
  },

  actions: {
    display: 'flex',
    gap: 12,
    width: '100%',
    justifyContent: 'space-between',

    [theme.fn.smallerThan('sm')]: {
      display: 'grid',
      gridTemplateColumns: 'repeat(4, 1fr)',
      gap: 10,
    },
  },

  actionCol: {
    flex: '0 0 30%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',

    [theme.fn.smallerThan('sm')]: {
      flex: 'unset',
      flexDirection: 'column',
      gap: 6,
      fontSize: rem(12),
    },
  },

  mobileOnly: {
    display: 'none',

    [theme.fn.smallerThan('sm')]: {
      display: 'block',
      textAlign: 'center',
    },
  },

  actionButton: {
    width: '100%',
    transition: 'all 0.2s ease',
    borderRadius: theme.radius.md,

    '&:hover': {
      transform: 'translateY(-2px)',
    },
  },
}));
export const quarterCell = (theme: any) => ({
  flex: `1 1 calc(25% - ${rem(12)})`,
  minWidth: rem(110),
  [theme.fn.smallerThan('sm')]: {
    flex: '0 0 auto',
    width: rem(44),
    height: rem(44),
    borderRadius: '50%',
    padding: 0,
  },
});

export const neutralIconStyles = (theme: any) => ({
  root: {
    ...quarterCell(theme),
    backgroundColor:
      theme.colorScheme === 'dark'
        ? theme.colors.dark[2]
        : theme.colors.gray[1],
    color: theme.colorScheme === 'dark' ? theme.white : theme.black,
    '&:hover': {
      backgroundColor:
        theme.colorScheme === 'dark'
          ? theme.colors.dark[6]
          : theme.colors.gray[1],
    },
  },
});

export const topUpStyles = (theme: any) => ({
  root: {
    ...quarterCell(theme),

    [theme.fn.smallerThan('sm')]: {
      flex: '0 0 auto',
      width: rem(44),
      height: rem(44),
      minWidth: rem(44),
      borderRadius: '50%',
      padding: 0,
    },
  },
  label: {
    [theme.fn.smallerThan('sm')]: { display: 'none' },
  },
  leftIcon: {
    [theme.fn.smallerThan('sm')]: { margin: 0 },
  },
});
