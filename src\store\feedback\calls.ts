import { apiEndpoints } from '@/data';
import { ApiClient } from '@/lib';
import { CreateFeedbackApiRequest } from './types';

enum queryKeys {
  create = 'create',
}
/**
 * @description function calls handler in "/feedback" api route to create feedback.
 * @param body
 * @returns success message if success and error if failed
 */
const createFeedbackRequest = (body: CreateFeedbackApiRequest) => ApiClient.post(apiEndpoints.feedback(), body)
  .then((res) => res.data)
  .catch((err) => {
    // handleApiError(err);
    throw err;
  });

export const createFeedbackMutation = () => ({
  mutationKey: [queryKeys.create],
  mutationFn: createFeedbackRequest,
});
