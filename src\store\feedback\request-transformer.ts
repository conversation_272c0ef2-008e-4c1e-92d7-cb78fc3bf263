/**
 * create feedback request schema
 */
import { Translate } from 'next-translate';
import { z } from 'zod';

export const createFeedbackFormSchema = (t: Translate) => z.object({
  name: z.string(),
  email: z.string(),
  url: z.string(),
  feedback: z.string().max(500, { message: t('errors:maxFeedbackLength') }),
  rate: z.number(),
});

export const createFeedbackApiRequestSchema = z.object({
  name: z.string(),
  email: z.string(),
  url: z.string(),
  feedback: z.string(),
  rate: z.number(),
});

export const createFeedbackBackendRequestSchema = createFeedbackApiRequestSchema.transform((data) => ({
  url: {
    url: data.url,
  },
  email: {
    email: data.email,
  },
  feedback: {
    rich_text: [
      {
        text: { content: data.feedback },
      },
    ],
  },
  rate: {
    number: data.rate,
  },
  name: {
    title: [
      {
        text: { content: data.name },
      },
    ],
  },
}));
