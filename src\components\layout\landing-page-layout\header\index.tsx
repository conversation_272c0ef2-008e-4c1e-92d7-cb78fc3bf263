import { ROUTES, assetBaseUrl, googlePlayAppLink } from '@/data';
import styles from './styles.module.scss';
import {
  Box,
  Image,
  Group,
  UnstyledButton,
  Button,
  Burger,
  Container,
  Header,
  Divider,
  Skeleton,
  useMantineTheme,
  MediaQuery,
} from '@mantine/core';
import Link from 'next/link';
import useTranslation from 'next-translate/useTranslation';
import { useRouter } from 'next/router';

import { useQuery } from '@tanstack/react-query';
import { getUserQuery } from '@/store/user';
import { useEffect, useState } from 'react';

import CreatePaymentLinkModal from '@/components/payment-link/create-payment-link-modal/create-payment-link-modal';
import { signIn, useSession } from 'next-auth/react';
import { LanguagesMenu } from '@/components/common/languages-menu';
import { IconWorld } from '@tabler/icons-react';
import { FeedbackPopup } from '@/components/feedback-popup';
import { useDisclosure } from '@mantine/hooks';
import SpotlightOpenButton from '@/components/spotlight/spotlight-open-button';
import UserDataAndNotificationsSection from '../../top-menu/user-data-notifications-section';

interface HeaderSearchProps {
  open: () => void;
  close: () => void;
  opened: boolean;
  isAuth: boolean;
}
export default function HeaderHome({
  open,
  opened,
  close,
  isAuth,
}: HeaderSearchProps) {
  const [openedFeedback, { open: openFeedback, close: closeFeedback }] = useDisclosure(false);
  const [openCreatePaymentLink, setOpenCreatePaymentLink] = useState(false);

  const { t } = useTranslation();
  const router = useRouter();
  const { pathname } = router;
  const theme = useMantineTheme();
  const { status } = useSession();
  const [loading, setLoading] = useState(false);
  const { data, isInitialLoading } = useQuery({
    ...getUserQuery({
      loggedIn: isAuth === true,
    }),
    retry: 1,
  });
  useEffect(
    () => () => {
      setLoading(false);
    },
    [],
  );
  const returnLink = (sectionId: string) => {
    let link = sectionId;
    if (
      pathname === ROUTES.merchant.path
      || pathname === ROUTES.agent.path
    ) {
      link = `${ROUTES.root.path}/${sectionId}`;
    }
    return link;
  };
  return (
    <Header height="" sx={{ position: 'fixed' }}>
      <div style={{ backgroundColor: 'rgb(0, 13, 35)' }}>
        <Container
          px={{
            xs: 20,
            sm: 30,
            lg: 40,
            base: 20,
          }}
          size={1200}
        >
          <Group py="xs" position="apart">
            <Box className={styles.image}>
              <Group>
                <Burger
                  color="white"
                  sx={() => ({
                    display: 'none',
                    [theme.fn.smallerThan('lg')]: {
                      display: 'block',
                    },
                  })}
                  opened={opened}
                  onClick={opened ? close : open}
                  size="sm"
                  aria-label="open navbar"
                />
                <Link
                  style={{ textDecoration: 'none', color: 'black' }}
                  href={isAuth ? ROUTES.wallets.path : ROUTES.root.path}
                >
                  <Image
                    alt="kazawallet-logo"
                    src={`${assetBaseUrl}/assets/logo/logo-landing.png`}
                    width={50}
                    height={50}
                  />
                </Link>
              </Group>
            </Box>
            <Box>
              <Group
                spacing="xs"
                sx={() => ({
                  [theme.fn.smallerThan('lg')]: {
                    display: 'none',
                  },
                })}
                className={styles.nav}
              >
                <Link
                  style={{ textDecoration: 'none' }}
                  href={ROUTES.wallets.path}
                >
                  {t('common:wallets')}
                </Link>
                <Link
                  scroll
                  style={{ textDecoration: 'none' }}
                  href={returnLink('#rates-section')}
                >
                  {t('common:rates')}
                </Link>
                <Link
                  scroll
                  style={{ textDecoration: 'none' }}
                  href={returnLink('#agents-program')}
                >
                  {t('common:agents')}
                </Link>
                <Link
                  scroll
                  style={{ textDecoration: 'none' }}
                  href={returnLink('#merchants-section')}
                >
                  {t('common:merchants')}
                </Link>
                <Link
                  scroll
                  style={{ textDecoration: 'none' }}
                  href={ROUTES.help.path}
                >
                  {t('common:helpCenter')}
                </Link>
                <Link
                  scroll
                  style={{ textDecoration: 'none' }}
                  href={googlePlayAppLink}
                  target="_blank"
                >
                  {t('common:installApp')}
                </Link>

                <SpotlightOpenButton isAuth={isAuth} />
              </Group>
            </Box>
            <Group align="center">
              <LanguagesMenu
                setMenuOpened={undefined}
                button={(
                  <UnstyledButton mt={5} aria-label="switch-lang">
                    <IconWorld color="gray" />
                  </UnstyledButton>
                )}
              />

              {(isInitialLoading || status === 'loading') && (
                <UnstyledButton aria-label="skeleton">
                  <MediaQuery smallerThan="xs" styles={{ width: '8rem' }}>
                    <Skeleton radius={50} height="2rem" width="8rem" />
                  </MediaQuery>
                </UnstyledButton>
              )}
              {isAuth && !isInitialLoading && (
                <UserDataAndNotificationsSection
                  isLandingHeader
                  data={data}
                  openFeedback={openFeedback}
                  setOpenCreatePaymentLink={setOpenCreatePaymentLink}
                />
              )}
              {isAuth === false
                && !isInitialLoading
                && status !== 'loading' && (
                  <Group>
                    <Button
                      loading={loading}
                      radius="lg"
                      onClick={() => {
                        setLoading(true);
                        signIn('keycloak', {
                          callbackUrl: ROUTES.wallets.path,
                        });
                      }}
                    >
                      {t('common:loginOrRegister')}
                    </Button>
                  </Group>
              )}
            </Group>
          </Group>
        </Container>
        <Divider color="gray" size={1} w="100%" />
      </div>
      {data && (
        <CreatePaymentLinkModal
          userData={data}
          openCreatePaymentLink={openCreatePaymentLink}
          setOpenCreatePaymentLink={setOpenCreatePaymentLink}
        />
      )}
      {data && (
        <FeedbackPopup
          close={closeFeedback}
          opened={openedFeedback}
          userEmail={data?.email ?? ''}
          userFullName={`${data?.firstName ?? ''} ${data?.lastName ?? ''}`}
        />
      )}
    </Header>
  );
}
