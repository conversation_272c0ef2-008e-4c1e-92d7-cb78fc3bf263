import { ENV_MODE } from '@/types';
import axios from 'axios';

export const captchaValidator = async (props: {
  token: string;
  secret: string;
  active?: boolean;
}) => {
  const { token, secret, active = true } = props;
  if (process.env.ENV !== ENV_MODE.Production || !active) {
    return { success: true };
  }

  const response = await axios.post(
    `https://www.google.com/recaptcha/api/siteverify?secret=${secret}&response=${token}`,
  );
  if (!response.data.success) throw new Error('captcha error');
  return response.data;
};
