import { Captcha } from '@/components/common/captcha';
import { ErrorPopup } from '@/components/common/errorr-poup';
import SubmitButton from '@/components/common/submit-button';
import { ROUTES } from '@/data';
import { useCaptcha } from '@/hooks';
import {
  getGiftCardMutation,
  RedeemGiftCardApiRequest,
  redeemGiftCardMutation,
} from '@/store/gift-card';
import { redeemGiftCardFormRequestSchema } from '@/store/gift-card/request-transformer';
import {
  Alert,
  Box,
  Button,
  Group,
  Paper,
  Text,
  TextInput,
} from '@mantine/core';
import { useForm, zodResolver } from '@mantine/form';
import { useMutation } from '@tanstack/react-query';
import useTranslation from 'next-translate/useTranslation';
import Link from 'next/link';
import React, { useState } from 'react';
import RedeemConfirmPopup from './redeem-confirm-popup';
import RedeemSuccessPopup from './redeem-success-popup';

function RedeemGiftCardForm() {
  const { t } = useTranslation();
  const { execute, reCaptchaRef, reset } = useCaptcha();
  const [isLoading, setLoading] = useState(false);
  const [errorPopupOpen, setErrorPopupOpen] = useState(false);
  const [confirmPopupOpen, setConfirmPopupOpen] = useState(false);
  const [successPopupOpen, setSuccessPopupOpen] = useState(false);
  const form = useForm({
    initialValues: {
      code: '',
    },
    validate: zodResolver(redeemGiftCardFormRequestSchema(t)),
  });

  const {
    mutate: getGiftData,
    data,
  } = useMutation({
    ...getGiftCardMutation(),
    onSuccess: () => {
      setConfirmPopupOpen(true);
      setLoading(false);
      reset();
    },
    onError: () => {
      setErrorPopupOpen(true);
      setLoading(false);
      reset();
    },
  });

  const { mutate } = useMutation({
    ...redeemGiftCardMutation(),
    onSuccess: (res) => {
      if (res?.success) {
        setSuccessPopupOpen(true);
        form.reset();
      } else {
        setErrorPopupOpen(true);
      }
      reset();
      setLoading(false);
      setConfirmPopupOpen(false);
    },
    onError: () => {
      reset();
      setLoading(false);
    },
  });
  const onSubmit = (values: RedeemGiftCardApiRequest) => {
    setLoading(true);

    if (reCaptchaRef.current) {
      execute((token) => mutate({
        body: values,
        chaKey: token,
      }));
    } else {
      mutate({ body: values });
    }
  };

  const handleCloseErrorPopup = () => {
    form.reset();
    setErrorPopupOpen(false);
  };

  const handleOpenConfirmPopup = () => {
    form.validate();
    if (form.isValid()) {
      setLoading(true);
      if (reCaptchaRef.current) {
        execute((token) => getGiftData({
          code: form.values?.code,
          chaKey: token,
        }));
      } else {
        getGiftData({
          code: form.values?.code,
        });
      }
    }
  };
  return (
    <>
      <Paper withBorder py="xl" px="md" radius="lg">
        <Text mb="lg">{t('common:redeemYourGiftCard')}</Text>
        <form onSubmit={form.onSubmit(onSubmit)}>
          <Group position="apart" w="100%" align="start">
            <TextInput
              miw={280}
              w={{ base: '100%', xs: '70%' }}
              radius="xl"
              withAsterisk
              label={t('common:code')}
              placeholder={t('common:enterGiftCardCode')}
              {...form.getInputProps('code')}
            />

            <SubmitButton
              mt="xl"
              radius="xl"
              w={{ base: '100%', xs: '25%' }}
              disabled={!form.values.code || isLoading}
              onClick={handleOpenConfirmPopup}
            >
              {t('common:redeem')}
            </SubmitButton>
          </Group>

          <Alert title={t('common:note')} variant="light" color="red" mt="lg">
            <Text
              span
              size="sm"
              sx={{
                whiteSpace: 'pre-line',
              }}
            >
              {t('common:giftCodeNote')}
            </Text>
            <Text span size="sm" color="green">
              A1BC23D4EFG78H56
            </Text>
          </Alert>
          <Box mt={30}>
            <Text size="sm">
              {t('common:note')}
              :
            </Text>
            <Text mt={2}>
              {t('common:redeemNote')}
              {' '}
              <Link
                style={{ textDecoration: 'none' }}
                href={ROUTES.redeemTerms.path}
              >
                {t('common:viewMore')}
              </Link>
            </Text>
          </Box>
          <RedeemConfirmPopup
            loading={isLoading}
            opened={confirmPopupOpen}
            setOpened={setConfirmPopupOpen}
            onClick={form.onSubmit(onSubmit)}
            giftCard={data}
          />
        </form>
      </Paper>
      <RedeemSuccessPopup
        opened={successPopupOpen}
        setOpened={setSuccessPopupOpen}
        giftCard={data}
      />
      <ErrorPopup
        open={errorPopupOpen}
        setOpen={setErrorPopupOpen}
        message={t('errors:redeemCodeError')}
        actionButton={(
          <Button fullWidth radius="lg" onClick={handleCloseErrorPopup}>
            {t('common:ok')}
          </Button>
        )}
      />
      <Captcha reCaptchaRef={reCaptchaRef} />
    </>
  );
}

export default RedeemGiftCardForm;
