/**
 * This component renders page display if happened any error in keyckoak auth.
 */
import { NextApiRequest, NextApiResponse } from 'next';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  return res.status(400).send(`
  <html>
  <head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ka<PERSON><PERSON><PERSON> - Login Error</title>
    <style>
      * {
        box-sizing: border-box;
      }
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 1rem;
        min-height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #1a1a1a;
        background-image: url('/assets/pattern-black.png');
        background-size: 260px;
      }
      .container {
        text-align: center;
        padding: 1.5rem;
        max-width: 100%;
        width: 100%;
      }
      .logo {
        width: 100%;
        max-width: 300px;
        height: auto;
        margin-bottom: 1rem;
      }
      .message {
        color: white;
        font-size: 1.25rem;
        margin: 0 0 1.5rem 0;
        line-height: 1.4;
      }
      .button {
        display: inline-block;
        text-decoration: none;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        background-color: #79ca53;
        color: white;
        font-size: 1rem;
        font-weight: 600;
        transition: background-color 0.2s;
        border: none;
        cursor: pointer;
      }
      .button:hover {
        background-color: #8ad664;
      }

      /* Mobile */
      @media (max-width: 480px) {
        body {
          padding: 0.7rem;
        }
        .container {
          padding: 1rem;
        }
        .logo {
          max-width: 250px;
          margin-bottom: 1rem;
        }
        .message {
          font-size: 0.9rem;
          margin: 1rem 0;
        }
        .button {
          padding: 0.5rem 1rem;
          font-size: 0.9rem;
        }
      }

      /* Tablet */
      @media (min-width: 481px) and (max-width: 768px) {
        .container {
          max-width: 500px;
          padding: 2rem;
        }
        .logo {
          max-width: 350px;
        }
        .message {
          font-size: 1.375rem;
        }
        .button {
          font-size: 1.125rem;
          padding: 0.875rem 2rem;
        }
      }

      /* Desktop */
      @media (min-width: 769px) {
        .container {
          max-width: 600px;
          padding: 2.5rem;
        }
        .logo {
          max-width: 400px;
        }
        .message {
          font-size: 1.5rem;
        }
        .button {
          font-size: 1.125rem;
          padding: 1rem 2.5rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <img
        src="/assets/logo/logo-full-en-dark.png"
        alt="Kazawallet Logo"
        class="logo"
      />
      <p class="message">
        Something went wrong when logging in
      </p>
      <a class="button" href="https://www.kazawallet.com">
        Go To Home
      </a>
    </div>
  </body>
  </html>
  `);
}

export default handler;
