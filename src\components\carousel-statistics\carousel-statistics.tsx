import { Carousel } from '@mantine/carousel';
import { Box } from '@mantine/core';
import { MainStat } from './main-stat/main-stat';
import { useRef } from 'react';
import Autoplay from 'embla-carousel-autoplay';
import { useStyles } from './style';
import Link from 'next/link';
import { getSettingQuery, SettingApiResponse } from '@/store/setting';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/router';

export default function StatisticsCarousel() {
  const { classes } = useStyles();
  const autoplay = useRef(Autoplay({ delay: 4000 }));
  const { locale } = useRouter();
  const { data } = useQuery(getSettingQuery());
  const slidesList: SettingApiResponse['homePageSlides'] = data?.homePageSlides;
  return (
    <Carousel
      w={{
        lg: '100%',
        md: 830,
        sm: 550,
        xs: 270,
        base: 270,
      }}
      mx="auto"
      classNames={classes}
      slideSize="25%"
      slideGap="sm"
      loop
      align="start"
      plugins={[autoplay.current]}
      slidesToScroll={1}
      breakpoints={[
        { maxWidth: 'lg', slideSize: '25%', slideGap: 'sm' },
        { maxWidth: 'md', slideSize: '33.33%', slideGap: 'sm' },
        { maxWidth: 'sm', slideSize: '50%', slideGap: 'sm' },
        { maxWidth: 'xs', slideSize: '100%', slideGap: 'sm' },
      ]}
    >
      {slidesList?.map((i, index) => {
        const imageUrl = locale === 'ar' && i.imageUrlAr ? i.imageUrlAr : i.imageUrl;
        const key = i.link || `slide-${imageUrl}-${index}`;
        return (
          <Carousel.Slide key={key}>
            <Box
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              {...((() => (i.link ? { component: Link, href: i.link } : {}))() as any)}
            >
              <MainStat image={imageUrl || ''} />
            </Box>
          </Carousel.Slide>
        );
      })}
    </Carousel>
  );
}
