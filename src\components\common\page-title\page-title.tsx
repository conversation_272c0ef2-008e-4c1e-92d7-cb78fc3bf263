import { Text } from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';
import React from 'react';

interface Props{
    title:string
}
function PageTitle({ title }:Props) {
  const { t } = useTranslation();
  return (
    <Text
      mb="lg"
      ta="center"
      color="dimmed"
      tt="capitalize"
      weight={700}
      size={25}
    >
      {t(`common:${title}`)}
    </Text>
  );
}

export default PageTitle;
