import {
  Title,
  Text,
  Button,
  Container,
  Group,
  useMantineTheme,
} from '@mantine/core';
import Link from 'next/link';

import classes from './style.module.scss';
import MetaTags from '@/components/common/meta-tags';

export default function NotFoundTitle() {
  const theme = useMantineTheme();
  return (
    <div>
      <MetaTags />
      <Container className={classes.root}>
        <div
          className={classes.label}
          style={{
            [theme.fn.smallerThan('sm')]: {
              fontSize: 120,
            },
            color:
              theme.colorScheme === 'dark'
                ? theme.colors.dark[4]
                : theme.colors.gray[2],
          }}
        >
          404
        </div>
        <Title
          className={classes.title}
          sx={{
            [theme.fn.smallerThan('sm')]: {
              fontSize: 32,
            },
            fontFamily: `Greycliff CF, ${theme.fontFamily}`,
          }}
        >
          Nothing To See Here
        </Title>
        <Text
          color="dimmed"
          size="lg"
          align="center"
          className={classes.description}
        >
          Page you are trying to open does not exist. you may have mistyped the
          address, or the page has been moved to another URL. If you think this
          is an error contact support.
        </Text>
        <Group position="center">
          <Link href="/" passHref>
            <Button variant="subtle" size="md">
              Take me back to home page
            </Button>
          </Link>
        </Group>
      </Container>
    </div>
  );
}
