import { numberInputFormatter } from '@/utils';
import {
  Grid, Group, MantineColor, Text,
} from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import { ReactNode } from 'react';

interface RatesItemProps {
  color: MantineColor | undefined;
  rateFrom: string;
  rateTo: string;
  code: string;
  flag: ReactNode;
  forLandingPage?: boolean;
  isCrypto?: boolean;
}
function RatesItem(props: RatesItemProps) {
  const {
    color,
    code,
    flag,
    rateFrom,
    rateTo,
    forLandingPage = false,
    isCrypto = false,
  } = props;

  const isMobileScreen = useMediaQuery('(max-width: 750px)');

  const hasLongDigits = (value: string) => {
    const formatted = numberInputFormatter(value);
    return formatted.length > 8;
  };

  const firstValue = isCrypto ? rateTo : rateFrom;
  const secondValue = isCrypto ? rateFrom : rateTo;

  const shouldUseTwoLines = isMobileScreen && (hasLongDigits(firstValue) || hasLongDigits(secondValue));

  return (
    <Grid>
      <Grid.Col span={6} sm={forLandingPage ? 8 : 6}>
        <Group align="center">
          {flag}
          <Text color={color}>{code}</Text>
        </Group>
      </Grid.Col>
      {shouldUseTwoLines ? (
        <Grid.Col span={6}>
          <Text color={color}>{numberInputFormatter(firstValue)}</Text>
          <Text mt="xs" color={color}>
            {numberInputFormatter(secondValue)}
          </Text>
        </Grid.Col>
      ) : (
        <>
          <Grid.Col span={3} sm={forLandingPage ? 2 : 3}>
            <Text color={color}>{numberInputFormatter(firstValue)}</Text>
          </Grid.Col>
          <Grid.Col span={3} sm={forLandingPage ? 2 : 3}>
            <Text color={color}>{numberInputFormatter(secondValue)}</Text>
          </Grid.Col>
        </>
      )}
    </Grid>
  );
}
RatesItem.defaultProps = {
  forLandingPage: false,
  isCrypto: false,
};
export default RatesItem;
