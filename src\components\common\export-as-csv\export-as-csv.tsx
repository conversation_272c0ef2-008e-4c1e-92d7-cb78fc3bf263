import { Button } from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';
import { CSVLink } from 'react-csv';

import React from 'react';
import dayjs from 'dayjs';
import { PaymentsApiResponse } from '@/store/payment';
import { TransfersApiResponse } from '@/store/transfer/types';

import { IconFileArrowRight } from '@tabler/icons-react';

const OperationNo = 'Operation No';
const OperationType = 'Operation Type';
const currencyLabel = 'currency.label';
const PaymentMMethod = 'Payment Method';
const paymentMethodLabel = 'paymentMethod.label';
const commonHeaders = [
  { label: OperationNo, key: 'transactionId' },
  { label: OperationType, key: 'type' },
  { label: 'Currency', key: currencyLabel },
  { label: 'From', key: 'currencyFrom.code' },
  { label: 'To', key: 'currencyTo.code' },
  { label: PaymentMMethod, key: paymentMethodLabel },
  { label: 'Amount', key: 'amount' },
  { label: 'Actual Amount', key: 'actualAmount' },
  { label: 'Fees', key: 'fees' },
  { label: 'Status', key: 'status' },
  { label: 'Sender', key: 'userFrom.accountId' },
  { label: 'Receiver', key: 'userTo.accountId' },
  { label: 'Date', key: 'createdAt' },
  { label: 'Note', key: 'note' },
];
const depositHeaders = [
  { label: OperationNo, key: 'transactionId' },
  { label: OperationType, key: 'type' },
  { label: 'Currency', key: currencyLabel },
  { label: PaymentMMethod, key: paymentMethodLabel },
  { label: 'Amount', key: 'amount' },
  { label: 'To Be Deposit', key: 'actualAmount' },
  { label: 'Fees', key: 'fees' },
  { label: 'Status', key: 'status' },
  { label: 'Date', key: 'createdAt' },
  { label: 'Note', key: 'note' },
];
const withdrawHeaders = [
  { label: OperationNo, key: 'transactionId' },
  { label: OperationType, key: 'type' },
  { label: 'Currency', key: currencyLabel },
  { label: PaymentMMethod, key: paymentMethodLabel },
  { label: 'To Be Paid', key: 'amount' },
  { label: 'To Be Received', key: 'actualAmount' },
  { label: 'Fees', key: 'fees' },
  { label: 'Status', key: 'status' },
  { label: 'Date', key: 'createdAt' },
  { label: 'Note', key: 'note' },
];
const transferHeaders = [
  { label: OperationNo, key: 'transactionId' },
  { label: OperationType, key: 'type' },
  { label: 'Currency', key: currencyLabel },
  { label: 'Sent Amount', key: 'amount' },
  { label: 'To Be Received', key: 'actualAmount' },
  { label: 'Fees', key: 'fees' },
  { label: 'Receiver', key: 'userTo.accountId' },
  { label: 'Receiver Email', key: 'userTo.email' },
  { label: 'Date', key: 'createdAt' },
  { label: 'Note', key: 'note' },
];
const receiveHeaders = [
  { label: OperationNo, key: 'transactionId' },
  { label: OperationType, key: 'type' },
  { label: 'Currency', key: currencyLabel },
  { label: 'Amount', key: 'amount' },
  { label: 'Sender', key: 'userFrom.accountId' },
  { label: 'Sender Email', key: 'userFrom.email' },
  { label: 'Date', key: 'createdAt' },
  { label: 'Note', key: 'note' },
];
const exchangeHeaders = [
  { label: OperationNo, key: 'transactionId' },
  { label: OperationType, key: 'type' },
  { label: 'From', key: 'currencyFrom.code' },
  { label: 'To', key: 'currencyTo.code' },
  { label: 'From', key: 'amount' },
  { label: 'To', key: 'actualAmount' },
  { label: 'Fees', key: 'fees' },
  { label: 'Date', key: 'createdAt' },
  { label: 'Note', key: 'note' },
];

interface ExportAsCsvProps {
  data: PaymentsApiResponse['data'] | TransfersApiResponse['data'];
  transactionsType:
    | 'deposit'
    | 'withdraw'
    | 'transfer'
    | 'exchange'
    | 'received'
    | 'all';
  isLoading: boolean;
}
function ExportAsCsv({ data, transactionsType, isLoading }: ExportAsCsvProps) {
  const { t } = useTranslation();

  const sortedData = data.sort(
    (b, a) => +dayjs(a.createdAt) - +dayjs(b.createdAt),
  );
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const returnDataAfterFormatting = () => sortedData?.map((item: any) => {
    let formattingItem = {
      ...item,
      fees: item?.totalFees ?? item?.snapshot?.fees,
      currency: item?.currency ?? item?.currencyFrom,
      createdAt: dayjs(item?.createdAt).format('YYYY/MM/DD hh:mm:ss A'),
    };
    if (item?.type === 'transfer' || item?.type === 'exchange') {
      formattingItem = {
        ...formattingItem,
        status: 'approved',
      };
    }
    return formattingItem;
  });

  const returnCsvFileHeader = () => {
    let headers = commonHeaders;
    switch (transactionsType) {
      case 'deposit': {
        headers = depositHeaders;
        break;
      }
      case 'withdraw': {
        headers = withdrawHeaders;
        break;
      }
      case 'transfer': {
        headers = transferHeaders;
        break;
      }
      case 'exchange': {
        headers = exchangeHeaders;
        break;
      }
      case 'received': {
        headers = receiveHeaders;
        break;
      }
      default:
        headers = commonHeaders;
    }
    return headers;
  };

  return (
    <CSVLink
      data={returnDataAfterFormatting()}
      headers={returnCsvFileHeader()}
      filename="Transactions History"
    >
      <Button disabled={isLoading} size="xs" color="primary">
        {t('common:exportToCsv')}
        <IconFileArrowRight style={{ marginInlineStart: '4px' }} size="18px" />
      </Button>
    </CSVLink>
  );
}

export default ExportAsCsv;
