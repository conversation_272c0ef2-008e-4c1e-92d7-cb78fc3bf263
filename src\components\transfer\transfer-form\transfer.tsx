/* eslint-disable max-lines */
/* eslint-disable complexity */
/* eslint-disable react-hooks/exhaustive-deps */
/**
 * This component renders a transfer form.
 *
 * @description
 * There is selected currency to transfer from it.
 * Should fill the amount and account id fields to complete the transfer.
 * Note and gif fields are optional.
 * I user enabled 2FA on click "transfer" button will open pin code popup to enter code, else
 *  will open confirmation popup.
 * When success transfer will display a popup to show transfer details.
 */
import {
  createTransferApiRequestSchema,
  createTransferMutation,
} from '@/store/transfer';
import { TransfersApiResponse } from '@/store/transfer/types';
import { ErrorFrontendType } from '@/types';
import { FeesTransferCalculate } from '@/utils/fees-functions/fees-transfer-withdraw-deposit';
import {
  NumberInput,
  Stack,
  TextInput,
  Text,
  SimpleGrid,
  Textarea,
  Button,
} from '@mantine/core';
// import { TextInputWithQRScanner } from '@/components/common/text-input-with-qr-scanner';
import { useForm, zodResolver } from '@mantine/form';
import { useDisclosure } from '@mantine/hooks';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { getCookie } from 'cookies-next';
import currency from 'currency.js';
import useTranslation from 'next-translate/useTranslation';
import React, { useState } from 'react';
import GifSelector, { GifSelectorApi } from '@/components/gif-selector';
import { numberInputFormatter } from '@/utils';
import { UserApiResponse } from '@/store/user';
import MinMaxAmountTransfer from './min-max-anount';
import { CodeConfirmModal } from '@/components/2fa/code-confirm-modal';
import { ChipGroupPercentage } from '@/components/chip-group-percentage';

import { PopUpSuccess } from '@/components/pop-up-success';
import SubmitButton from '@/components/common/submit-button';
import { TransactionConfirmModal } from '@/components/common/transaction-confirm-modal';
import TransferDetails from '@/components/pop-up-success/transfer-details';
import { Captcha } from '@/components/common/captcha';
import { useCaptcha } from '@/hooks';
import { playCaptcha, ROUTES } from '@/data';
import { LimitsErrorKeys } from '@/types/error.type';
import { ErrorPopup } from '@/components/common/errorr-poup';
import Link from 'next/link';

enum queryKeys {
  user = 'user',
}
interface FormValues {
  amount: number | string;
  userTo: string;
  currencyFrom: string | number;
  currencyTo: string | number;
  type: 'transfer';
  notes: string;
  gif?: string;
}
interface Props {
  balanceAmount: number;
  currencyId: string;
  currencyPrecision: number;
  feesProps: {
    feesFixed: number | null | undefined;
    feesPercentage: number | null | undefined;
    feesMin: number | null | undefined;
    feesMax: number | null | undefined;
  };
  rate: number;
  transferMin: number | undefined | null;
  transferMax: number | undefined | null;
  currencyItem: UserApiResponse['currencies'][0] | undefined;
}

export default function TransferForm({
  balanceAmount,
  currencyId,
  feesProps,
  rate,
  transferMax,
  transferMin,
  currencyPrecision,
  currencyItem,
}: Props) {
  const { t } = useTranslation();
  // const router = useRouter();
  const [responseData, setResponseData] = useState<TransfersApiResponse['data'][0]>();
  const [opened, { open, close }] = useDisclosure(false);
  const [openedCode, setOpenedCode] = useState(false);
  const [code, setCode] = useState('');
  const [codeError, setCodeError] = useState('');
  const [gifSelectorApis, setGifSelectorApis] = useState<GifSelectorApi>();
  const [isLoading, setLoading] = useState(false);
  const [openLimitPopup, setLimitPopup] = useState(false);

  const queryClient = useQueryClient();
  const TRANSFER_TRANSLATE = t('common:transfer');
  const { execute, reCaptchaRef, reset } = useCaptcha();
  // const { accountId } = router.query;

  // Get current user data for QR generation
  // const { data: userData } = useQuery(getUserQuery({}));
  // initial transfer form
  const form = useForm<FormValues>({
    initialValues: {
      amount: '',
      // userTo: accountId ? `${accountId}` : '',
      userTo: '',
      currencyFrom: currencyId,
      currencyTo: currencyId,
      type: 'transfer',
      notes: '',
    },

    validate: zodResolver(createTransferApiRequestSchema),
  });

  // update both form field and URL parameter
  // const updateUserToField = async (value: string) => {
  //   form.setFieldValue('userTo', value);

  //   // Update URL to keep it in sync
  //   const newQuery = { ...router.query };
  //   if (value) {
  //     newQuery.accountId = value;
  //   } else {
  //     delete newQuery.accountId;
  //   }

  //   await router.replace(
  //     { pathname: router.pathname, query: newQuery },
  //     undefined,
  //     { shallow: true },
  //   );
  // };

  // Use form value as the source of truth for the input field
  // const currentUserToValue = form.values.userTo;

  // mutation transfer api call
  const { mutate } = useMutation(
    createTransferMutation().mutationFn,
    {
      onSuccess: (data) => {
        setResponseData(data);
        queryClient.invalidateQueries({ queryKey: [queryKeys.user] });
        close();
        setCodeError('');
        setOpenedCode(false);
        form.reset();
        // updateUserToField('');
        form.setFieldValue('userTo', '');
        setCode('');
        gifSelectorApis?.clear();
        reset();
        setLoading(false);
      },
      onError(error: ErrorFrontendType) {
        setCode('');
        close();
        if (error?.response?.data?.message?.key === 'invalidCode') {
          setCodeError('invalidCode');
        } else setCodeError('');
        if (
          error?.response?.data?.message?.key
        === LimitsErrorKeys.ERROR_PASS_THE_LIMIT
        ) {
          setLimitPopup(true);
        }
        reset();
        setLoading(false);
      },
    },
  );
  // handle percentage button checked
  // on check percent will calculate the amount use this way
  // amount= user balance * percent /100
  // v is percent value
  const handlePercentChange = (v: string) => {
    const newAmount = (balanceAmount * +v) / 100;
    form.setValues({ amount: newAmount });
  };

  // submit transfer mutation
  function submitTransfer(qrCode: string | undefined) {
    setLoading(true);
    const body = {
      body: {
        ...form.values,
        currencyFrom: currencyId,
        currencyTo: currencyId,
      },
      qrCode,
    };
    if (reCaptchaRef.current) {
      execute((token) => mutate({
        ...body,
        chaKey: token,
      }));
    } else {
      mutate(body);
    }
  }
  // return calculated fee
  const fees = typeof form.values.amount === 'number'
    ? FeesTransferCalculate(
      feesProps?.feesFixed,
      feesProps?.feesPercentage,
      feesProps?.feesMin,
      feesProps?.feesMax,
      form.values.amount,
    )
    : 0;
  // return boolean if the amount was more than max amount or less than min amount or more than the user balance.
  const minMaxError = () => {
    let isError = false;
    if (typeof form.values.amount === 'number' && form.values.amount > 0) {
      if (transferMin && form?.values?.amount < transferMin) isError = true;
      else if (
        (transferMax && form?.values?.amount > transferMax)
        || form?.values?.amount > balanceAmount
      ) {
        isError = true;
      } else isError = false;
    }
    return isError;
  };
  // Sync form field with URL accountId parameter
  // useEffect(() => {
  //   if (accountId && form.values.userTo !== `${accountId}`) {
  //     form.setFieldValue('userTo', `${accountId}`);
  //   }
  // }, [accountId]);

  // reset form if selected currency changed, but preserve URL accountId
  // useEffect(() => {
  //   form.reset();
  //   // Restore accountId from URL after reset
  //   if (accountId) {
  //     form.setFieldValue('userTo', `${accountId}`);
  //   }
  // }, [currencyId]);

  // return usd  price calculated in usd
  const usdPrice = rate && typeof form.values.amount === 'number'
    ? currency((1 / rate) * form.values.amount, {
      symbol: '',
    }).format()
    : 0;
  // return fee value after format it
  const feeValue = currency(fees, {
    symbol: '',
    precision: currencyPrecision,
  }).format();
  // return fee value after format it
  const toBeReceived = currency(
    (typeof form.values.amount === 'number' ? form.values.amount : 0) - fees,
    {
      symbol: '',
      precision: currencyPrecision,
    },
  ).format();
  // this boolean will return true if the received amount is less than 0
  // to be received amount = amount - fee
  const isToBeReceivedError = typeof form.values.amount === 'number' && form.values.amount - fees <= 0;

  // conditions to disabled and enabled submit button
  const isSubmitButtonDisabled = !currencyId
    || (typeof form.values.amount === 'number'
      && (form?.values?.amount <= 0 || form.values.amount - fees <= 0))
    || !form?.values?.userTo
    || typeof form.values.amount !== 'number'
    || minMaxError();
  return (
    <>
      <form>
        <Stack my="md" mx="auto">
          <MinMaxAmountTransfer currencyItem={currencyItem} />
          <NumberInput
            precision={currencyPrecision ?? 2}
            radius="lg"
            size="sm"
            step={0}
            label={<Text tt="capitalize">{t('common:amount')}</Text>}
            hideControls
            formatter={numberInputFormatter}
            {...form.getInputProps('amount')}
          />
          {minMaxError() && (
            <Text mx={2} color="red" size="sm">
              {t('common:minMaxError')}
            </Text>
          )}
          <SimpleGrid cols={3} spacing="xs">
            <TextInput
              disabled
              readOnly
              radius="lg"
              size="sm"
              label={t('common:usdPrice')}
              value={usdPrice}
            />
            <TextInput
              disabled
              readOnly
              radius="lg"
              size="sm"
              value={feeValue}
              label={t('common:fees')}
            />
            <div>
              <TextInput
                readOnly
                radius="lg"
                size="sm"
                value={toBeReceived}
                label={<Text tt="capitalize">{t('common:toBeReceived')}</Text>}
              />
              {isToBeReceivedError && (
                <Text mx={2} color="red" size="sm">
                  {t('common:toBeReceivedError')}
                </Text>
              )}
            </div>
          </SimpleGrid>
          {/* <TextInputWithQRScanner
            radius="lg"
            size="sm"
            label={
              <Text tt="capitalize">{t('common:accountNumberEmail')}</Text>
            }
            value={currentUserToValue}
            onChange={updateUserToField}
            error={form.errors.userTo}
            placeholder={t('common:enterAccountIdOrEmail') || ''}
            accountId={userData?.accountId}
            currentUserAccountId={userData?.accountId}
            allowSelfTransfer={false}
            showSuccessAlert
          /> */}
          <TextInput
            size="sm"
            radius="lg"
            label={
              <Text tt="capitalize">{t('common:accountNumberEmail')}</Text>
            }
            placeholder={t('common:enterAccountIdOrEmail')}
            {...form.getInputProps('userTo')}
          />
          <GifSelector
            onGifSelected={(v) => {
              form.setFieldValue('gif', v?.url);
            }}
            componentApi={(apis) => {
              setGifSelectorApis(apis);
            }}
          />
          <Textarea
            size="sm"
            radius="lg"
            placeholder={t('common:note') ?? ''}
            label={<Text tt="capitalize">{t('common:note')}</Text>}
            {...form.getInputProps('notes')}
          />
          <ChipGroupPercentage
            disabled={false}
            handlePercentChange={handlePercentChange}
          />
          <SubmitButton
            disabled={isSubmitButtonDisabled}
            onClick={() => (getCookie('2fa-enabled') ? setOpenedCode(true) : open())}
            fullWidth
          >
            {TRANSFER_TRANSLATE}
          </SubmitButton>
        </Stack>
        <TransactionConfirmModal
          close={close}
          openedDefault={opened}
          isLoading={isLoading}
          onClick={() => submitTransfer(undefined)}
          operationType="transfer"
        >
          <TransferDetails
            currencyCode={currencyItem?.code}
            currencySymbol={currencyItem?.symbol}
            amount={form.values.amount as number}
            actualAmount={(form.values.amount as number) - fees}
            precision={currencyPrecision}
            fees={fees}
            sender={undefined}
            receiver={form.values.userTo}
          />
        </TransactionConfirmModal>
        <CodeConfirmModal
          codeError={codeError}
          btnText={t('common:transfer')}
          setOpenedCode={setOpenedCode}
          isLoading={isLoading}
          onClick={(v) => submitTransfer(v)}
          openedDefault={openedCode}
          code={code}
          setCode={setCode}
          title={t('common:enterCode')}
          closeable={false}
          additionalContent={undefined}
        />
        {responseData && (
          <PopUpSuccess
            operationType="transfer"
            currencySymbol={responseData?.snapshot?.currency?.symbol}
            currencyToSymbol=""
            setResponseData={setResponseData}
            operationId={responseData?.transactionId}
            amount={responseData?.amount}
            actualAmount={responseData?.actualAmount}
            fees={responseData?.snapshot?.fees}
            status={responseData ? 'approved' : undefined}
            note={responseData?.note ?? undefined}
            date={responseData?.createdAt}
            sender={responseData?.snapshot?.sender?.account_id}
            receiver={responseData?.snapshot?.receiver?.account_id}
            currencyCode={responseData?.snapshot?.currency?.code}
            currencyToCode={undefined}
            paymentMethod={undefined}
            fields={undefined}
            rate={0}
            precision={responseData?.currencyFrom?.precision}
            currencyToPrecision={2}
            gif={responseData.gif}
            cryptoGetaway={undefined}
            originPaymentAmount={undefined}
            isLoading={false}
            adminMessage={undefined}
          />
        )}
        <ErrorPopup
          open={openLimitPopup}
          setOpen={setLimitPopup}
          message={t('errors:limit-error')}
          actionButton={(
            <Button
              fullWidth
              radius="lg"
              component={Link}
              href={`${ROUTES.myAccount.path}?page=limits`}
            >
              {t('common:goToLimits')}
            </Button>
          )}
        />
      </form>
      <Captcha reCaptchaRef={reCaptchaRef} active={playCaptcha} />
    </>
  );
}
