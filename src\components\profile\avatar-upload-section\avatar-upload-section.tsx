import { AvatarCustom } from '@/components/common/avatar';
import { UploadFileSize } from '@/data';
import { addFileMutation } from '@/store/file';
import { UpdateUserApiRequest, UserApiResponse } from '@/store/user';
import { preserveUserData } from '@/utils/profile-utils';
import {
  FileInput,
  Loader,
  useMantineTheme,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { UseMutateFunction, useMutation } from '@tanstack/react-query';
import useTranslation from 'next-translate/useTranslation';
import React, { useState } from 'react';

interface Props {
  data: UserApiResponse | undefined;
  isLoading: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  mutate: UseMutateFunction<any, unknown, UpdateUserApiRequest, unknown>;
}
function AvatarUploadSection({ data, mutate, isLoading }: Props) {
  const { t } = useTranslation();
  const theme = useMantineTheme();
  const idDarkMode = theme.colorScheme === 'dark';
  const [avatar, setAvatar] = useState('');
  const mutateFile = useMutation({
    ...addFileMutation(),
    onSuccess(res) {
      setAvatar(res[0]?.url);
      mutate(preserveUserData({ avatar: res[0]?.id }, data));
    },
  });

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const onFileChange = (v: any) => {
    if (v && v.size > 1024 * UploadFileSize * 1024) {
      notifications.show({
        message: t('errors:fileSizeError'),
        color: 'red',
      });
    } else {
      const formData = new FormData();
      formData.append('files', v);
      mutateFile.mutate(formData);
    }
  };
  return (
    <div
      style={{
        position: 'relative',
        textAlign: 'center',
        margin: '0 auto',
        height: 122,
        border: '0.0625rem solid ',
        borderColor: idDarkMode ? '#373A40' : '#dee2e6',
        borderRadius: '50%',
      }}
    >

      <AvatarCustom
        name={`${data?.firstName} ${data?.lastName}`}
        size={120}
        src={avatar || `${data?.avatar?.url ?? ''}`}
      />
      <FileInput
        sx={{
          opacity: 0,
          '& .mantine-ltr-FileInput-input,& .mantine-rtl-FileInput-input': {
            height: '120px',
            width: '120px',
            borderRadius: '60px',
            position: 'absolute',
            opacity: 0,
            top: -120,
            border: 'none',
          },
        }}
        w={120}
        h={120}
        disabled={mutateFile.isLoading || isLoading}
        accept="image/png,image/jpeg"
        onChange={onFileChange}
      />

      {(mutateFile.isLoading || isLoading) && (
        <Loader
          sx={{
            position: 'absolute',
            top: 45,
            right: 40,
          }}
        />
      )}
    </div>
  );
}

export default AvatarUploadSection;
