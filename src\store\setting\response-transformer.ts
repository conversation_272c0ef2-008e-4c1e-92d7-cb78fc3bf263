/**
 * setting response schema
 */
import { NotificationsTypes } from '@/types';
import { z } from 'zod';

export const settingBackendSchema = z.object({
  attributes: z.object({
    agents: z
      .array(
        z.object({
          id: z.number(),
          city: z.string(),
          name: z.string(),
          region: z.string(),
          currency: z.string(),
          mobile: z.string().nullable().optional(),
          telegram: z.string().nullable().optional(),
          whatsapp: z.string().nullable().optional(),
          accountId: z.string().nullable().optional(),
          location: z.string().nullable().optional(),
        }),
      )
      .nullable()
      .optional(),
    announcements: z
      .array(
        z.object({
          id: z.number().optional(),
          link: z.string().optional(),
          text: z.string().optional(),
          type: z.nativeEnum(NotificationsTypes).optional(),
          date: z.string().optional(),
          sticky: z.boolean().optional().default(false),
        }),
      )
      .nullable()
      .optional(),
    additional_fields: z
      .object({
        homePageSlides: z
          .array(
            z.object({
              link: z.string().optional(),
              image_url: z.string().optional(),
              image_url_ar: z.string().optional(),
            }),
          )
          .optional(),
      })
      .nullable()
      .optional(),
  }),
});

export const settingApiResponseSchema = z
  .object({
    data: settingBackendSchema,
  })
  .transform((data) => ({
    agents: data?.data?.attributes?.agents ?? [],
    notifications: data?.data?.attributes?.announcements ?? [],
    homePageSlides: data?.data?.attributes?.additional_fields?.homePageSlides?.map((item) => ({
      link: item.link,
      imageUrl: item.image_url,
      imageUrlAr: item.image_url_ar,
    })) ?? [],
  }));
