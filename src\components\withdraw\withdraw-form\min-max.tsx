import { Group, Text } from '@mantine/core';
import currency from 'currency.js';
import useTranslation from 'next-translate/useTranslation';
import React from 'react';

interface Props {
  minAmount: number;
  maxAmount: number | null;
  symbol: string | null;
  currencyAmount: number;
  currencyPrecision: number | null;
}
function MinMaxAmount({
  maxAmount,
  minAmount,
  symbol,
  currencyAmount,
  currencyPrecision,
}: Props) {
  const { t } = useTranslation();
  return (
    <Group spacing="xs">
      <div>
        <Text span weight={500} color="dimmed">
          {t('common:min')}
          {' '}
          {currency(minAmount ?? 0, {
            symbol: '',
            precision: currencyPrecision ?? 2,
          }).format()}
        </Text>
        <Text mx={4} span weight={500} color="dimmed">
          {symbol}
        </Text>
      </div>
      -
      <div>
        <Text span weight={500} color="dimmed">
          {t('common:max')}
          {' '}
          {currency(maxAmount || currencyAmount, {
            symbol: '',
            precision: currencyPrecision ?? 2,
          }).format()}
        </Text>
        <Text mx={4} span weight={500} color="dimmed">
          {symbol}
        </Text>
      </div>
      {currencyAmount === 0 && (
        <Text mx={2} weight={500} color="red">
          {t('common:youDoNotHaveBalanceToDoOperation')}
        </Text>
      )}
    </Group>
  );
}

export default MinMaxAmount;
