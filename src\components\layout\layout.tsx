import { ReactNode, useEffect } from 'react';
import TopMenu from './top-menu';
import { Sidebar } from './sidebar/sidebar';
import Footer from './footer';
import { useDisclosure } from '@mantine/hooks';
import { AppShell, Container, useMantineTheme } from '@mantine/core';

import {
  footerLandingItems,
  footerNavbarMenuLinks,
  topMenuLinks,
} from './list-items';
import { useRouter } from 'next/router';
import HeaderHome from './landing-page-layout/header';
import FooterLandingPage from './landing-page-layout/footer';
import { signIn, useSession } from 'next-auth/react';
import { ROUTES, authPages } from '@/data/routes';
import { WalkthroughSlider } from '../walkthrough-slider';
import { Spotlight } from '../spotlight';
import { usePwa } from '@/store/pwa/store';

interface Props {
  children: ReactNode;
}

export function Layout({ children }: Props) {
  const [opened, { open, close }] = useDisclosure(false);
  const { status } = useSession();
  const isAuth = status === 'authenticated';
  const theme = useMantineTheme();
  const setDeferredPrompt = usePwa((state) => state.setDeferredPrompt);
  const { pathname, locale } = useRouter();
  const isLandingOrMerchantPages = pathname === '/'
    || pathname === ROUTES.merchant.path
    || pathname === ROUTES.agent.path;
  const bgColor = 'rgb(0, 13, 35)';
  const layoutBakground = () => {
    let backgroundColor = '';
    if (isLandingOrMerchantPages) backgroundColor = bgColor;
    else if (theme.colorScheme === 'light') {
      backgroundColor = `${theme.colors.gray[0]}`;
    } else backgroundColor = `${theme.colors.dark[7]}`;
    return backgroundColor;
  };
  useEffect(() => {
    if (authPages.includes(pathname) && status === 'unauthenticated') {
      signIn('keycloak');
    }
  }, [status, pathname]);

  // this for pwa install button
  useEffect(() => {
    const handleBeforeInstallPrompt = (event: Event) => {
      event.preventDefault();
      setDeferredPrompt(event);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

    return () => {
      window.removeEventListener(
        'beforeinstallprompt',
        handleBeforeInstallPrompt,
      );
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return (
    <AppShell
      padding={0}
      styles={{
        main: {
          minHeight: 'calc(100vh - 4rem)',
          backgroundColor: layoutBakground(),
        },
      }}
      navbar={(
        <Sidebar
          isAuth={isAuth}
          bgColor={isLandingOrMerchantPages ? bgColor : ''}
          links={footerNavbarMenuLinks}
          opened={opened}
          close={close}
        />
      )}
      header={
        isLandingOrMerchantPages ? (
          <HeaderHome
            isAuth={isAuth}
            open={open}
            close={close}
            opened={opened}
          />
        ) : (
          <TopMenu
            isAuth={isAuth}
            links={topMenuLinks}
            open={open}
            opened={opened}
          />
        )
      }
      footer={
        isLandingOrMerchantPages ? (
          <FooterLandingPage data={footerLandingItems(locale)} />
        ) : (
          <Footer links={footerNavbarMenuLinks} />
        )
      }
    >
      <Container
        bg={isLandingOrMerchantPages ? bgColor : ''}
        pt={isLandingOrMerchantPages ? 50 : 'md'}
        pb={isLandingOrMerchantPages ? 0 : 'md'}
        px={isLandingOrMerchantPages ? 0 : 'sm'}
        size={isLandingOrMerchantPages ? 1700 : 'lg'}
      >
        {children}
        <WalkthroughSlider isAuth={isAuth} />
        {isAuth && <Spotlight />}
      </Container>
    </AppShell>
  );
}
