import {
  Mo<PERSON>, Button, Group, Text,
} from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';
import { ReactNode } from 'react';

interface Props {
  isLoading: boolean;
  onClick: () => void;
  message:ReactNode| string;
  openedDefault: boolean;
  close: () => void;
}
export default function ConfirmModal({
  isLoading,
  message,
  onClick,
  openedDefault, close,
}: Props) {
  const { t } = useTranslation();
  return (
    <Modal
      radius="lg"
      centered
      opened={openedDefault}
      onClose={close}
      title={(
        <Text color="red" size="lg" weight={700}>
          {t('common:areYouSure')}
        </Text>
)}
    >
      <div style={{ margin: '0 0 25px 0' }}>
        {message}
      </div>
      <Group position="right">
        <Button radius="lg" variant="outline" loading={isLoading} onClick={onClick}>
          {t('common:confirm')}
        </Button>
        <Button
          radius="lg"
          variant="outline"
          color="red"
          onClick={close}
        >
          {t('common:close')}
        </Button>
      </Group>

    </Modal>
  );
}
