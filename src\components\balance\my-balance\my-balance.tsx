import { CurrenciesApiResponse } from '@/store/currencies';
import {
  Group, Image, MantineTheme, Stack, Text,
} from '@mantine/core';
import currency from 'currency.js';
import useTranslation from 'next-translate/useTranslation';
import React from 'react';

interface Props {
  currency: CurrenciesApiResponse['data'][0]['id'] | undefined;
  icon: string | undefined;
  balance: number;
  symbol: string | undefined;
  symbolCurrency: string | undefined | null;
  precision: number;
}
function MyBalance({
  icon,
  balance,
  symbol,
  precision,
  symbolCurrency,
  currency: currencyId,
}: Props) {
  const { t } = useTranslation();
  const balanceFormat = currency(balance, { symbol: '', precision })
    .format()
    .split('.');
  const textStyle = (theme: MantineTheme) => ({
    fontSize: 34,
    [theme.fn.smallerThan('sm')]: {
      fontSize: 30,
    },
  });
  const smallTextStyle = (theme: MantineTheme) => ({
    fontSize: 24,
    [theme.fn.smallerThan('sm')]: {
      fontSize: 20,
    },
  });
  return currencyId ? (
    <Group h="100%" align="center">
      {icon && (
        <Image src={icon} width={45} height={45} radius={50} alt="currency" />
      )}
      {symbol && (
        <Text color="dimmed" weight="bold" size={30}>
          {symbol}
        </Text>
      )}
      <Text sx={textStyle} weight="bold">
        {balanceFormat[0]}
        {balanceFormat[1] && (
          <Text span sx={smallTextStyle}>
            .
            {balanceFormat[1]}
          </Text>
        )}
        {' '}
        <span style={{ fontSize: 20 }}>{symbolCurrency ?? ''}</span>
      </Text>
    </Group>
  ) : (
    <Stack h="100%" justify="center">
      <Text tt="capitalize" size="lg" weight={500}>
        {t('common:selectCurrency')}
      </Text>
    </Stack>
  );
}

export default MyBalance;
