import { UserApiResponse } from '@/store/user';
import {
  Group, Paper, Stack, Text,
} from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';
import React from 'react';
import BadgeItem from './badge-item';

interface BadgesProps {
  badges: UserApiResponse['badges'];
}
function Badges({ badges }: BadgesProps) {
  const { t } = useTranslation();
  return (
    <Stack align="stretch" w="100%" m="auto" spacing="xs">
      <Paper p="md" withBorder radius="lg">
        <Text>{t('common:badges')}</Text>
        <Group mt="xs">
          {badges?.map((badge) => (
            <BadgeItem key={badge?.title} badge={badge} />
          ))}
        </Group>
      </Paper>
    </Stack>
  );
}

export default Badges;
