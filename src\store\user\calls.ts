import { apiEndpoints } from '@/data';
import { ApiClient } from '@/lib';
import { handleApiError } from '@/utils/api-utils/handle-backend-error';
import {
  UpdateUserApiRequest,
  UserApiResponse,
  getUserQueryProps,
} from './types';
import { deleteCookie } from 'cookies-next';

enum queryKeys {
  balance = 'balance',
  user = 'user',
  update = 'update',
  generate = 'generate',
  unblock = 'unblock',
  resend = 'resend',
}
/** ************************************************* */
/**
 * @description function calls handler in "/users" api route with "get" method to fetch user data.
 * @param props
 * @returns user data
 */
const getUserRequest = (props: getUserQueryProps) => {
  const { populate } = props;
  return ApiClient.get<UserApiResponse>(apiEndpoints.users(), {
    params: {
      userCurrencies: populate?.userCurrencies,
      favoriteCurrencies: populate?.favoriteCurrencies,
    },
  })
    .then((res) => {
      deleteCookie('ACCOUNT_BLOCK');
      return res?.data;
    })
    .catch((e) => {
      handleApiError(e, true);
      throw e.response?.data;
    });
};

export const getUserQuery = (props: getUserQueryProps) => ({
  queryKey: [queryKeys.user],
  queryFn: () => getUserRequest(props),
  refetchOnWindowFocus: false,
  enabled: props?.loggedIn,
});
/** ************************************************* */
/**
 * @description function calls handler in "/custom/user/wallettotal" api route with "get" method to return user balance.
 * @returns user balance
 */
const getTotalBalanceRequest = () => ApiClient.get(apiEndpoints.totalBalance())
  .then((res) => res?.data)
  .catch((e) => {
    handleApiError(e, true);
    throw e.response?.data;
  });

export const getTotalBalanceQuery = () => ({
  queryKey: [queryKeys.balance],
  queryFn: () => getTotalBalanceRequest(),
  refetchOnWindowFocus: false,
});

/** ************************************************* */
/**
 * @description function calls handler in "/users" api route with "put" method to update user data.
 * @param body
 * @returns user data
 */
const updateUserRequest = (body: UpdateUserApiRequest) => ApiClient.put(apiEndpoints.users(), body)
  .then((res) => res.data)
  .catch((err) => {
    handleApiError(err);
    throw err;
  });

export const updateUserMutation = () => ({
  mutationKey: [queryKeys.update],
  mutationFn: updateUserRequest,
});

/** ************************************************* */
/**
 * @description function calls handler in "/custom/user/regenrateApiKey" api route with "post" method to return new api key.
 * @returns new api key
 */
const regenerateApiKeyRequest = () => ApiClient.post(apiEndpoints.regenerateApiKey())
  .then((res) => res.data)
  .catch((err) => {
    handleApiError(err);
    throw err;
  });

export const regenerateApiKeyMutation = () => ({
  mutationKey: [queryKeys.generate],
  mutationFn: regenerateApiKeyRequest,
});
/** ************************************************* */
/**
 * @description function calls handler in "/custom/user/check-otp-after-block" api route with "post" method to unblock user account.
 */
const unblockUserAccountRequest = (body: { code: string; chaKey?: string }) => ApiClient.post(apiEndpoints.unblockUserAccount(), body, {
  params: {
    chaKey: body?.chaKey,
  },
})
  .then((res) => res.data)
  .catch((err) => {
    handleApiError(err);
    throw err;
  });

export const unblockUserAccountMutation = () => ({
  mutationKey: [queryKeys.unblock],
  mutationFn: unblockUserAccountRequest,
});
/** ************************************************* */
/**
 * @description function calls handler in "/custom/user/resend-otp-email" api route with "post" method to resend otp code to user email to unblock his account.
 */
const resendOtpCodeRequest = () => ApiClient.post(apiEndpoints.resendUnblockAccountOtpCode())
  .then((res) => res.data)
  .catch((err) => {
    handleApiError(err);
    throw err;
  });

export const resendOtpCodeMutation = () => ({
  mutationKey: [queryKeys.resend],
  mutationFn: resendOtpCodeRequest,
});
