import MetaTags from '@/components/common/meta-tags';
import {
  ActionIcon,
  Button,
  Container,
  Group,
  Image,
  Stack,
  Title,
  useMantineTheme,
} from '@mantine/core';
import React, { useEffect } from 'react';
import classes from './style.module.scss';
import { useRouter } from 'next/router';
import { signOut } from 'next-auth/react';
import { assetBaseUrl, ROUTES } from '@/data';
import { getCookie } from 'cookies-next';
import { BlockedErrorKeys } from '@/types/error.type';
import Link from 'next/link';
import useTranslation from 'next-translate/useTranslation';
import { useQuery } from '@tanstack/react-query';
import { getUserQuery } from '@/store/user';
import { LanguagesMenu } from '@/components/common/languages-menu';
import { IconWorld } from '@tabler/icons-react';

function AccountBlocked() {
  const { t } = useTranslation();
  const theme = useMantineTheme();
  const { replace, locale } = useRouter();
  const BlockedError = getCookie('ACCOUNT_BLOCK');
  const { refetch } = useQuery(getUserQuery({}));
  useEffect(() => {
    refetch();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  useEffect(() => {
    if (!BlockedError || BlockedError !== BlockedErrorKeys.USER_BLOCKED) {
      replace(ROUTES.root.path);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [BlockedError]);

  return (
    <div className={classes.root}>
      <MetaTags />
      <Group className={classes.language} p="xl" position="right">
        <LanguagesMenu
          setMenuOpened={undefined}
          button={(
            <ActionIcon
              variant="outline"
              size="xl"
              color="gray"
              aria-label="switch-lang"
            >
              <IconWorld color="gray" size={32} />
            </ActionIcon>
          )}
        />
      </Group>
      <Container className={classes.content}>
        <Image
          mx="auto"
          width={350}
          src={
            locale === 'ar'
              ? `${assetBaseUrl}/assets/logo/logo-full-ar-dark.png`
              : `${assetBaseUrl}/assets/logo/logo-full-en-dark.png`
          }
        />

        <Title
          order={3}
          ta="center"
          color="white"
          sx={{
            whiteSpace: 'pre-line',
            fontFamily: `Greycliff CF, ${theme.fontFamily}`,
          }}
        >
          {t('common:accountBlockedError')}
        </Title>

        <Stack align="center" mt={20}>
          <Group w={300} position="apart" mt="md">
            <Button
              w={130}
              onClick={() => signOut({ redirect: true, callbackUrl: ROUTES.root.path })}
            >
              {t('common:logout')}
            </Button>

            <Button
              w={130}
              variant="outline"
              component={Link}
              href="https://kazawallet.trengohelp.com"
              target="_blank"
            >
              {t('common:helpCenter')}
            </Button>
          </Group>
        </Stack>
      </Container>
    </div>
  );
}

export default AccountBlocked;
