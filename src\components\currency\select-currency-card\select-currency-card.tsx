/* eslint-disable react/require-default-props */
import {
  Group, Text, Select, Image,
} from '@mantine/core';
import { ReactNode, forwardRef } from 'react';
import currency from 'currency.js';

import { UserApiResponse } from '@/store/user';
import useTranslation from 'next-translate/useTranslation';
import { Icon } from '@/components/common/icon';
import { CurrenciesOrPaymentMethodsPopup } from '@/components/currencies-payment-methods-popup';
import { useStyles } from '@/components/payment-method/select-payment-methods/style';
import { prop, sortBy } from 'ramda';
import { getTranslatedTextValue } from '@/utils';
import { useRouter } from 'next/router';

interface ItemProps extends React.ComponentPropsWithoutRef<'div'> {
  image: string;
  label: string;
  symbol: string;
  amount: number;
  precision: number;
}

const SelectItem = forwardRef<HTMLDivElement, ItemProps>(
  ({
    image, label, amount, precision, symbol, ...others
  }: ItemProps, ref) => (
    <div style={{ borderRadius: 20 }} ref={ref} {...others}>
      <Group noWrap>
        <Image src={image} height={35} width={35} radius={50} alt="currency" />
        <Group w="100%" position="apart">
          <Text weight={500} tt="capitalize" size="sm">
            {label}
          </Text>
          <Group spacing={5} dir="ltr">
            <Text size="sm">{symbol}</Text>
            <Text size="sm">
              {currency(amount, {
                symbol: '',
                precision: precision ?? 2,
              }).format()}
            </Text>
          </Group>
        </Group>
      </Group>
    </div>
  ),
);

interface SelectCardProps {
  setSelectedItem: (v: UserApiResponse['currencies'][0]) => void;
  selectedItem: UserApiResponse['currencies'][0] | undefined;
  children: ReactNode;
  data: UserApiResponse['currencies'];
  disabled?: boolean;
  value?: string;
  type: 'select' | 'popup';
}
export default function SelectCurrencyCard({
  setSelectedItem,
  selectedItem,
  children,
  data,
  disabled = false,
  value,
  type,
}: SelectCardProps) {
  const { classes } = useStyles();
  const { t } = useTranslation();
  const { locale } = useRouter();
  const sortByOrder = sortBy(prop('order'));
  const nullishCurrenciesOrder = data?.filter((item) => item.order === null) ?? [];
  const unNullishCurrenciesOrder = data?.filter((i) => i.order !== null) ?? [];
  const currenciesSortedByOrder = sortByOrder(unNullishCurrenciesOrder).concat(
    nullishCurrenciesOrder,
  );

  return (
    <div style={{ position: 'relative', height: '50px' }}>
      {type === 'select' ? (
        <>
          <Select
            readOnly={disabled}
            radius="xl"
            label=""
            placeholder=""
            itemComponent={SelectItem}
            data={currenciesSortedByOrder?.map((i) => ({
              label: getTranslatedTextValue(locale, i?.label, i?.labelAr),
              image: i?.image,
              symbol: i?.symbol,
              amount: i?.amount,
              precision: i?.precision,
              value: i?.value,
            }))}
            maxDropdownHeight={315}
            miw={300}
            nothingFound={t('common:noCurrenciesAvailableToThisOperation')}
            onChange={(v) => {
              const item = data?.filter((i) => i.value === v);
              setSelectedItem(item[0]);
            }}
            classNames={classes}
            transitionProps={{
              transition: 'fade',
              duration: 280,
              timingFunction: 'ease',
            }}
            value={value}
            rightSection={(
              <Group mr={20} mt={-8} position="right">
                <Icon icon="selector" color="" size={26} />
              </Group>
            )}
          />
          {children}
        </>
      ) : (
        <CurrenciesOrPaymentMethodsPopup
          dataType="currency"
          data={currenciesSortedByOrder}
          setSelectedItem={setSelectedItem}
          selectedItem={selectedItem}
          disabled={disabled}
          currencySymbol={undefined}
          paymentType={undefined}
          setLoading={() => {}}
        >
          {children}
        </CurrenciesOrPaymentMethodsPopup>
      )}
    </div>
  );
}
