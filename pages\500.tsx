import {
  Title,
  Text,
  Button,
  Container,
  Group,
  useMantineTheme,
} from '@mantine/core';
import Link from 'next/link';

import classes from './style.module.scss';
import MetaTags from '@/components/common/meta-tags';

export default function InternalErrorTitle() {
  const theme = useMantineTheme();
  return (
    <div>
      <MetaTags />
      <Container className={classes.root}>
        <div
          className={classes.label}
          style={{
            color:
              theme.colorScheme === 'dark'
                ? theme.colors.dark[4]
                : theme.colors.gray[2],

            [theme.fn.smallerThan('sm')]: {
              fontSize: 120,
            },
          }}
        >
          500
        </div>
        <Title
          className={classes.title}
          sx={{
            fontFamily: `Greycliff CF, ${theme.fontFamily}`,
            [theme.fn.smallerThan('sm')]: {
              fontSize: 32,
            },
          }}
        >
          Something Bad Just Happened...
        </Title>
        <Text
          color="dimmed"
          size="lg"
          align="center"
          className={classes.description}
        >
          Our servers could not handle your request. don&apos;t worry, our
          development team was already notified. try refreshing the page.
        </Text>
        <Group position="center">
          <Link href="/" passHref>
            <Button variant="subtle" size="md">
              Take me back to home page
            </Button>
          </Link>
        </Group>
      </Container>
    </div>
  );
}
