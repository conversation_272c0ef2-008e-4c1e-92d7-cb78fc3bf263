import { Chip, Group } from '@mantine/core';
import React from 'react';

function ChipGroupPercentage({
  handlePercentChange,
  disabled,
}: {
  handlePercentChange: (v: string) => void;
  disabled:boolean
}) {
  return (
    <Chip.Group onChange={handlePercentChange}>
      <Group position="center" spacing="xs">
        <Chip
          disabled={disabled}
          sx={{
            label: {
              border: 'none',
              height: '40px',
              lineHeight: '40px',
              textAlign: 'center',
            },
          }}
          value="25"
        >
          25%
        </Chip>
        <Chip
          disabled={disabled}
          sx={{
            label: {
              border: 'none',
              height: '40px',
              lineHeight: '40px',
              textAlign: 'center',
            },
          }}
          value="50"
        >
          50%
        </Chip>
        <Chip
          disabled={disabled}
          sx={{
            label: {
              border: 'none',
              height: '40px',
              lineHeight: '40px',
              textAlign: 'center',
            },
          }}
          value="75"
        >
          75%
        </Chip>
        <Chip
          disabled={disabled}
          sx={{
            label: {
              border: 'none',
              height: '40px',
              lineHeight: '40px',
              textAlign: 'center',
            },
          }}
          value="100"
        >
          100%
        </Chip>
      </Group>
    </Chip.Group>
  );
}

export default ChipGroupPercentage;
