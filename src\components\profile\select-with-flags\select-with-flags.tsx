/* eslint-disable react/require-default-props */
/* eslint-disable @typescript-eslint/no-explicit-any */

import getCountryFlag from '@/utils/get-country-flag';
import {
  Avatar, Group, Select, SelectProps, Text,
} from '@mantine/core';
import { keys, lensPath, view } from 'ramda';
import { ComponentPropsWithoutRef, forwardRef, useMemo } from 'react';

interface selectItemProps extends ComponentPropsWithoutRef<'div'> {
  image: string;
  label: string;
  code: string;
  labelValue: 'code' | 'name';
}
interface SelectWithFlagsProps<T> extends Omit<SelectProps, 'data'> {
  data: T;
  blackList?: (keyof T)[];
  whiteList?: (keyof T)[];
  priorityList?: (keyof T)[];
  labelValue?: 'code' | 'name';
}

const SelectItem = forwardRef<HTMLDivElement, selectItemProps>((props, ref) => {
  const {
    image, label, code, labelValue, ...others
  } = props;
  return (
    <div ref={ref} {...others} key={label}>
      <Group miw={120} position="apart">
        <Text maw={150} lineClamp={1} size="sm">
          {labelValue === 'code' ? code : label}
        </Text>
        <Avatar src={image} size="sm" alt="flag" />
      </Group>
    </div>
  );
});
function SelectWithFlags<T>(props: SelectWithFlagsProps<T>) {
  const {
    data,
    blackList,
    whiteList,
    priorityList,
    labelValue = 'name',
    ...rest
  } = props;
  // eslint-disable-next-line sonarjs/cognitive-complexity
  const countriesWithFlagsData = useMemo(() => {
    const firstData = priorityList?.flatMap((v: any) => {
      if (blackList?.includes(v)) return [];
      if (!!whiteList && !whiteList?.includes(v)) return [];
      return {
        label:
          labelValue === 'code'
            ? `+${view(lensPath([v, 'phone']), data)}`
            : view(lensPath([v, 'name']), data),
        code: `+${view(lensPath([v, 'phone']), data)}`,
        image: getCountryFlag(v),
        value: v,
      };
    });

    const secondData = keys<any>(data).flatMap((v: any) => {
      if (blackList?.includes(v)) return [];
      if (priorityList?.includes(v)) return [];
      if (!!whiteList && !whiteList?.includes(v)) return [];
      return {
        label:
          labelValue === 'code'
            ? `+${view(lensPath([v, 'phone']), data)}`
            : view(lensPath([v, 'name']), data),
        code: `+${view(lensPath([v, 'phone']), data)}`,
        image: getCountryFlag(v),
        value: v,
      };
    });
    return [...(firstData || []), ...secondData];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [priorityList, blackList, whiteList]);
  return (
    <Select
      searchable
      {...rest}
      data={countriesWithFlagsData?.map((i) => ({ labelValue, ...i }))}
      itemComponent={SelectItem}
      transitionProps={{ duration: 0 }}
    />
  );
}

export default SelectWithFlags;
