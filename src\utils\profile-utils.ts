import { UpdateUserApiRequest, UserApiResponse } from '@/store/user';

export const preserveUserData = (
  updateData: UpdateUserApiRequest,
  existingUserData?: UserApiResponse,
): UpdateUserApiRequest => {
  if (!existingUserData) return updateData;

  const {
    firstName,
    lastName,
    country,
    webhookUrl,
    authenticatorEnabled,
    sendVerificationConditions,
    favoriteCurrencies,
  } = existingUserData;

  const preserved: Partial<UpdateUserApiRequest> = {
    ...(firstName && { firstName }),
    ...(lastName && { lastName }),
    ...(country && { country }),
    ...(webhookUrl && { webhookUrl }),
    ...(typeof authenticatorEnabled === 'boolean' && { authenticatorEnabled }),
    ...(sendVerificationConditions && { sendVerificationConditions }),
    ...(favoriteCurrencies?.length && {
      favoriteCurrencies: favoriteCurrencies
        .map(({ id }) => id)
        .filter((id): id is number => id !== undefined),
    }),
  };

  return { ...preserved, ...updateData };
};
