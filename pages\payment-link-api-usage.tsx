/**
 * This component renders payment link api documentation page
 * @description
 * Render markdown text.
 * Get the text from this file "payment-link-api.md"
 */
import MetaTags from '@/components/common/meta-tags';
import { Layout } from '@/components/layout/layout';
import { Container } from '@mantine/core';
import fs from 'fs';
import path from 'path';
import React from 'react';
import MarkdownView from 'react-showdown';

function PaymentLinkApi(props: { title: string; data: string }) {
  const { title, data } = props;

  return (
    <>
      <MetaTags title={title} />
      <Layout>
        <Container dir="ltr">
          <MarkdownView
            className="markdown"
            markdown={data}
            options={{ tables: true, emoji: true }}
          />
        </Container>
      </Layout>
    </>
  );
}
export default PaymentLinkApi;
export async function getStaticProps() {
  const filePath = path.join(process.cwd(), 'payment-link-api.md');
  const markdownContent = fs.readFileSync(filePath, 'utf-8');
  return {
    props: {
      title: 'Payment Link Api',
      data: markdownContent,
    },
  };
}
