/**
 * This component renders a deposit page.
 *
 * @description
 * Tn this page we have a form to create payment type "deposit"
 * We have select to choose the currency and after choose it will show select contain one
 *  or more payment method support the currency selected.
 * The currencies data it come from "get all currencies and get user currencies" apis calls and merge it
 *  to get the user balance in each currency and show it.
 * We can add currency uid to initial the currency item select in the route like this "deposit/USD" then the default
 *  item in currencies select will be "USD" and we can change it to another currencies.
 * After form submitting will open popup to show create payment response details.
 */
import MyBalanceSkeleton from '@/components/balance/my-balance/my-balance-skeleton';
import MetaTags from '@/components/common/meta-tags';

import { Layout } from '@/components/layout/layout';

import { UserApiResponse, getUserQuery } from '@/store/user';
import { Paper, Stack, Text } from '@mantine/core';
import { useQuery } from '@tanstack/react-query';
import useTranslation from 'next-translate/useTranslation';
import { useRouter } from 'next/router';

import React, { useState } from 'react';
import { getPaymentMethodsQuery } from '@/store/payment-methods';

import { getRatesQuery } from '@/store/rate';
import { getCurrenciesQuery } from '@/store/currencies';
import { eqBy, prop, unionWith } from 'ramda';

import { MyBalance } from '@/components/balance/my-balance';
import { PageTitle } from '@/components/common/page-title';
import { SelectCurrencyCard } from '@/components/currency/select-currency-card';

import PaymentMethodsSelect from '@/components/payment-method/select-payment-methods';

function Deposit() {
  const router = useRouter();
  const { query } = router;
  const { t } = useTranslation();
  const { data: ratesData } = useQuery(getRatesQuery());

  const [selectedItem, setSelectedItem] = useState<UserApiResponse['currencies'][0]>();

  const handleSelectCurrency = (v: UserApiResponse['currencies'][0]) => {
    setSelectedItem(v);
    router.query.currency = v.uid;
    delete router.query.method;
    router.push(router);
  };
  // call user api to get user currencies
  const { data, isLoading } = useQuery(getUserQuery({}));
  // call get all currencies api to get all currencies
  const allCurrencies = useQuery(getCurrenciesQuery({}));
  const isLoadingCurrencies = isLoading || allCurrencies.isLoading;
  // merge all currencies with user currencies to get "amount" props from user currencies
  const mergedCurrencies = unionWith(
    eqBy(prop('id')),
    data?.currencies ?? [],
    allCurrencies?.data?.data ?? [],
  );
  // init currency item selected if there is a "uid" query
  const initCurrency = query?.currency
    ? mergedCurrencies?.filter((i) => i.uid === query?.currency)
    : [];
  // get initial item or the selected user item
  const item = selectedItem ?? (initCurrency && initCurrency[0]);
  // call payment methods and filter by the currency selected
  const paymentMethodsData = useQuery(
    getPaymentMethodsQuery({
      populate: {
        depositCurrencies: true,
        depositCustomFields: true,
      },
      filters: {
        depositCurrencyId: item?.uid,
      },
    }),
  );

  return (
    <div>
      <MetaTags title={t('common:deposit')} />
      <Layout>
        <PageTitle title="deposit" />
        <Stack align="center">
          <Text tt="capitalize" size="lg" weight={500}>
            {t('common:selectCurrency')}
          </Text>
          <Paper withBorder py="xs" px="md" radius={20}>
            <SelectCurrencyCard
              type="popup"
              data={mergedCurrencies ?? []}
              setSelectedItem={handleSelectCurrency}
              selectedItem={item}
            >
              {isLoadingCurrencies ? (
                <MyBalanceSkeleton />
              ) : (
                <MyBalance
                  precision={item?.precision ?? 2}
                  icon={item?.image}
                  balance={item?.amount ? +item.amount : 0}
                  symbol={undefined}
                  symbolCurrency={item?.symbol}
                  currency={item?.id}
                />
              )}
            </SelectCurrencyCard>
          </Paper>

          {item && (
            <Text tt="capitalize" weight={500} color="dimmed" size="lg">
              {t('common:myBalance')}
            </Text>
          )}
        </Stack>
        {item && (
          <Stack mt="md" mx="auto" maw={800}>
            <Text color="dimmed" tt="capitalize" weight={500} size="lg">
              {t('common:chosePaymentSystem')}
            </Text>

            <PaymentMethodsSelect
              rates={ratesData?.rates}
              selectedCurrency={item}
              data={paymentMethodsData?.data?.data}
              transactionType="deposit"
              dataLoading={paymentMethodsData?.isFetching}
            />
          </Stack>
        )}
      </Layout>
    </div>
  );
}

export default Deposit;
export async function getServerSideProps() {
  return { props: {} };
}
