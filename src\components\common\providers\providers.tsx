import {
  Hydrate,
  QueryClient,
  QueryClientProvider,
} from '@tanstack/react-query';
import { PropsWithChildren, useState } from 'react';
import { useHotkeys, useLocalStorage } from '@mantine/hooks';

import { SessionProvider } from 'next-auth/react';
import {
  ColorScheme,
  ColorSchemeProvider,
  MantineProvider,
} from '@mantine/core';
import { ltrCache, rtlCache } from '@/lib';
import ErrorBoundaries from '@/components/error-boundaries/error-boundaries';
import { useRouter } from 'next/router';
import { Cairo } from 'next/font/google';
import { Notifications } from '@mantine/notifications';
import RouterTransition from '@/components/router-transition/router-transition';
import { GoogleAnalytics } from 'nextjs-google-analytics';

const cairo = Cairo({
  subsets: ['arabic', 'latin'],
  weight: ['200', '300', '400', '500', '600', '700', '800', '900'],
});

function Providers(props: PropsWithChildren) {
  const { children } = props;
  const [queryClient] = useState(() => new QueryClient());
  const [colorScheme, setColorScheme] = useLocalStorage<ColorScheme>({
    key: 'color-scheme',
    defaultValue: 'light',
    getInitialValueInEffect: true,
  });
  const router = useRouter();
  const rtl = router.locale === 'ar';

  const toggleColorScheme = (value?: ColorScheme) => setColorScheme(value || (colorScheme === 'dark' ? 'light' : 'dark'));

  useHotkeys([['mod+J', () => toggleColorScheme()]]);
  //* ************************************************************************** */
  return (
    <main dir={rtl ? 'rtl' : 'ltr'} className={rtl ? cairo.className : ''}>
      <QueryClientProvider client={queryClient}>
        <Hydrate>
          <ColorSchemeProvider
            colorScheme={colorScheme}
            toggleColorScheme={toggleColorScheme}
          >
            <MantineProvider
              withGlobalStyles
              withNormalizeCSS
              withCSSVariables
              emotionCache={rtl ? rtlCache : ltrCache}
              theme={{
                /** Put your mantine theme override here */
                dir: rtl ? 'rtl' : 'ltr',
                colorScheme,
                fontFamily: cairo.style.fontFamily,
                headings: {
                  fontFamily: cairo.style.fontFamily,
                },
                colors: {
                  secondary: [
                    '#FFFDE5',
                    '#FFF8B8',
                    '#FFF48A',
                    '#FFF05C',
                    '#FFEB2E',
                    '#FFE700',
                    '#CCB900',
                    '#998B00',
                    '#665C00',
                    '#332E00',
                  ],
                  primary: [
                    '#EAF5FA',
                    '#C4E4F2',
                    '#9FD3EA',
                    '#79C2E2',
                    '#54B1D9',
                    '#2EA0D1',
                    '#2580A7',
                    '#1C607D',
                    '#124054',
                    '#09202A',
                  ],
                },
                primaryColor: 'primary',
              }}
            >
              <RouterTransition />
              <Notifications />
              <SessionProvider refetchInterval={60 * 30}>
                <ErrorBoundaries>
                  <GoogleAnalytics
                    trackPageViews
                    strategy="lazyOnload"
                    gaMeasurementId="G-QGYD2BL0ZT"
                  />
                  {children}
                </ErrorBoundaries>
              </SessionProvider>
            </MantineProvider>
          </ColorSchemeProvider>
        </Hydrate>
      </QueryClientProvider>
    </main>
  );
}

export default Providers;
