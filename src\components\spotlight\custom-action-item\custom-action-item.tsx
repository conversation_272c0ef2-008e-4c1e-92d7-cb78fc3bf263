import { UnstyledButton } from '@mantine/core';
import { SpotlightActionProps } from '@mantine/spotlight';
import React from 'react';
import { HistoryItem } from './history-item';
import ActionItem from './action-item';
import { useStyles } from './style';

function CustomAction({
  action,
  styles,
  classNames,
  hovered,
  onTrigger,
  ...others
}: SpotlightActionProps) {
  const { classes } = useStyles(undefined, {
    styles,
    classNames,
    name: 'Spotlight',
  });

  return (
    <UnstyledButton
      className={classes.action}
      data-hovered={hovered || undefined}
      tabIndex={-1}
      onMouseDown={(event) => event.preventDefault()}
      onClick={onTrigger}
      {...others}
    >
      {action?.group === 'history' ? (
        <HistoryItem itemData={action?.data} />
      ) : (
        <ActionItem action={action} />
      )}
    </UnstyledButton>
  );
}

export default CustomAction;
