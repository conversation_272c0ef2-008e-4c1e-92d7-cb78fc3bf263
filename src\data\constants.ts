export const websiteName = 'kazawallet';
export const defaultLocale = 'ar';
export const LocaleCookie = 'NEXT_LOCALE';
export const FALLBACK_ERROR_MESSAGE = 'Something Went Wrong';
export const FALLBACK_ERROR_MESSAGE_KEY = 'wrong';
export const TOKEN_SECRET = 'INp8IvdIyeMcoGAgFGoA61DdBglwwSqnXJZkgz8PSnw';

export const websiteUrl = 'https://www.kazawallet.com';

export const googlePlayAppLink = 'https://play.google.com/store/apps/details?id=com.kazawallet';
export const apkDownloadLink = 'https://fsn1.your-objectstorage.com/public-assets/kazawallet/kazawallet.apk';

export const assetBaseUrl = 'https://fsn1.your-objectstorage.com/public-assets/kazawallet';

export const kycBlogEnUrl = 'https://blog.kazawallet.com/how-to-verify-your-account-on-kazawallet-kyc/';
export const kycBlogArUrl = 'https://blog.kazawallet.com/ar/%D8%AA%D9%88%D8%AB%D9%8A%D9%82-%D8%A7%D9%84%D8%AD%D8%B3%D8%A7%D8%A8-%D9%81%D9%8A-%D9%83%D8%B0%D8%A7%D9%88%D8%A7%D9%84%D9%8A%D8%AA-kyc/';
export const kycVideoEnUrl = 'https://youtu.be/VjeJ9RbQqxc?si=PNE6A5V1SP1y9gYy';
export const kycVideoArUrl = 'https://youtu.be/T-Tf-J3vvr0?si=5RKsO4Mc-lpHgZqe';

export const logo = {
  fullAr: `${assetBaseUrl}/assets/logo/logo-full-ar-dark.png`,
  fullEn: `${assetBaseUrl}/assets/logo/logo-full-en-dark.png`,
};

export const DATE_FORMAT = 'YYYY-MM-DD hh:mm:ss A';

export const UploadFileSize = 5; // used to accept upload file size in all system (number of MB)

export const playCaptcha = true; // this boolean to activate captcha for transactions

export const NOTION_DATABASE_ID = {
  agent: '182a03b502158051b21ffd54142e4b31',
  feedBack: '7b8e48ada2d349ebbc907e6f92c9374b',
  givaway: '15ca03b502158038b658d7a34d6f263a',
  merchant: '1de081bb8a8849af9ead31c209882441',
  merchants: '8cae99c4d7a84cf39009dec63909c03a',
};
