/**
 * This component renders a user profile page and form to update user data.
 *
 */
import MetaTags from '@/components/common/meta-tags';

import { Container } from '@mantine/core';

import useTranslation from 'next-translate/useTranslation';

import { useQuery } from '@tanstack/react-query';
import { getUserQuery } from '@/store/user';
import ProfileSkeleton from '@/components/profile/profile-tab/profile-skeleton';
import { PageTitle } from '@/components/common/page-title';
import ProfileTab from '@/components/profile/profile-tab';

export default function Account() {
  const { t } = useTranslation();
  const { data, isLoading } = useQuery(getUserQuery({}));

  return (
    <div>
      <MetaTags title={t('common:myAccount')} />
      <Container pt="xl" p={0} size={800}>
        <PageTitle title="myAccount" />
        {isLoading ? <ProfileSkeleton /> : <ProfileTab data={data} />}
      </Container>
    </div>
  );
}

export async function getServerSideProps() {
  return { props: {} };
}
