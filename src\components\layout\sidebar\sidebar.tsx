import { useState } from 'react';
import {
  Navbar,
  Text,
  Drawer,
  Image,
  UnstyledButton,
  Group,
  ScrollArea,
  Kbd,
  Space,
} from '@mantine/core';
import Link from 'next/link';

import {
  ROUTES, assetBaseUrl, googlePlayAppLink, websiteName,
} from '@/data';
import { useStyles } from './style';
import useTranslation from 'next-translate/useTranslation';
import { useRouter } from 'next/router';
import { Icon } from '@/components/common/icon';
import { signIn } from 'next-auth/react';
import { spotlight } from '@mantine/spotlight';

interface Props {
  opened: boolean;
  close: () => void;
  links: { link: string; label: string; icon: string, target?:string }[];
  bgColor: string;
  isAuth: boolean;
}
export function Sidebar({
  opened,
  close,
  links: data,
  bgColor,
  isAuth,
}: Props) {
  const { classes, cx } = useStyles();
  const { t } = useTranslation();
  const { push } = useRouter();
  const [active, setActive] = useState('');
  const links = data.map((item) => (
    <Link
      className={cx(classes.link, {
        [classes.linkActive]: item.label === active,
      })}
      href={item.link}
      key={item.label}
      target={item?.target}
      onClick={() => {
        setActive(item.label);
      }}
    >
      <Icon icon={item.icon} size="22" color="dark" />
      <span style={{ margin: '0 8px' }}>
        {' '}
        {t(`common:${item.label}`)}
      </span>
    </Link>
  ));

  return (
    <Drawer
      opened={opened}
      onClose={close}
      title={(
        <Group>
          <Link href={ROUTES.root.path}>
            <Image
              alt="kazawallet-logo"
              src={`${assetBaseUrl}/assets/logo/logo.png`}
              width={30}
              height={30}
            />
          </Link>
          <Text color={bgColor ? 'white' : ''} weight={500}>
            {t(`common:${websiteName}`)}
          </Text>
        </Group>
      )}
      overlayProps={{ opacity: 0.5, blur: 4 }}
      size="xs"
      sx={{
        '.mantine-ltr-Drawer-header,.mantine-rtl-Drawer-header': {
          backgroundColor: bgColor,
        },
        '.mantine-ltr-Drawer-content,.mantine-rtl-Drawer-content': {
          overflowY: 'hidden',
        },
      }}
    >
      <Navbar
        bg={bgColor}
        mt="lg"
        sx={{
          borderRight: 'none',
        }}
      >
        <Navbar.Section grow>
          <ScrollArea h="86vh">
            {links}
            {isAuth && (
              <UnstyledButton
                w="100%"
                className={classes.link}
                onClick={() => {
                  close();
                  spotlight.open();
                }}
              >
                <Icon icon="search" size="22" color="dark" />
                <Kbd mx="8px">Ctrl+K</Kbd>
              </UnstyledButton>
            )}

            <Link
              href={googlePlayAppLink}
              target="_blank"
              className={classes.link}
            >
              <Icon icon="device-mobile" size="22" color="dark" />
              <Space w="8px" />
              {t('common:installApp')}
            </Link>

            {isAuth ? (
              <UnstyledButton
                w="100%"
                className={classes.link}
                onClick={() => push(ROUTES.logout)}
              >
                <Icon icon="logout" size="22" color="dark" />
                <Text mx="8px">{t('common:logout')}</Text>
              </UnstyledButton>
            ) : (
              <UnstyledButton
                w="100%"
                className={classes.link}
                onClick={() => signIn('keycloak', {
                  callbackUrl: ROUTES.wallets.path,
                })}
              >
                <Icon icon="login" size="22" color="dark" />
                <Text mx="8px">{t('common:loginOrRegister')}</Text>
              </UnstyledButton>
            )}
          </ScrollArea>
        </Navbar.Section>
      </Navbar>
    </Drawer>
  );
}
