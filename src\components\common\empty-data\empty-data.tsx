import {
  Stack, Text, useMantineTheme,
} from '@mantine/core';
import { IconDatabaseOff } from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';

import React from 'react';

interface Props {
  message: string;
}
function EmptyData({ message }: Props) {
  const theme = useMantineTheme();
  const { t } = useTranslation();
  return (
    <Stack
      justify="center"
      align="center"
      color="primary"
    >
      <IconDatabaseOff color={theme.colors.gray[6]} size={36} />
      <Text size="lg" weight={700} color={theme.colors.gray[6]}>
        {t(`common:${message === '' ? 'noDataToShow' : message}`)}
      </Text>
    </Stack>
  );
}
export default EmptyData;
