import {
  Button,
  Container,
  Grid,
  Group,
  Loader,
  MultiSelect,
  NumberInput,
  Stack,
  Text,
  TextInput,
  useMantineTheme,
} from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';
import React, { useEffect } from 'react';
import styles from '../styles.module.scss';
import { useStyles } from './style';
import { useForm, zodResolver } from '@mantine/form';
import { useMutation } from '@tanstack/react-query';
import { notifications } from '@mantine/notifications';
import { Captcha } from '@/components/common/captcha';
import { useCaptcha } from '@/hooks';
import { countries } from 'countries-list';
import SelectWithFlags from '@/components/profile/select-with-flags/select-with-flags';
import {
  CreateAgentRequestApiRequest,
  createAgentRequestMutation,
} from '@/store/agent';
import { createAgentRequestFormSchema } from '@/store/agent/request-transformer';

const CUrrenciesList = ['SYP', 'USD'];
function AgentForm() {
  const { t } = useTranslation();
  const theme = useMantineTheme();
  const { classes } = useStyles();
  const { execute, reCaptchaRef, reset } = useCaptcha();
  const form = useForm<CreateAgentRequestApiRequest>({
    initialValues: {
      email: '',
      name: '',
      firstName: '',
      lastName: '',
      code: '',
      phone: '',
      telegramOrWhatsapp: '',
      address: '',
      currencies: [],
      jobType: '',
      liquidity: '',
      location: '',
    },

    validate: zodResolver(createAgentRequestFormSchema(t)),
  });

  const { mutate, isLoading } = useMutation({
    ...createAgentRequestMutation(),
    onSuccess() {
      form.reset();
      reset();
      notifications.show({
        message: t('common:requestSentSuccessfully'),
        color: 'blue',
      });
    },
    onError() {
      reset();
    },
  });
  const submit = (values: CreateAgentRequestApiRequest) => {
    if (reCaptchaRef.current) {
      execute((token) => mutate({
        ...values,
        phone: countries[form.values?.code as 'AD'].phone + values.phone,
        token,
      }));
    } else {
      mutate({
        ...values,
        phone: countries[form.values?.code as 'AD'].phone + values.phone,
      });
    }
  };
  useEffect(() => {
    if (form.values.phone) {
      form.setFieldValue('phone', '');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [form.values?.code]);
  return (
    <div style={{ paddingTop: 40 }} id="agent-form">
      <Container
        py={40}
        px={{
          xs: 20,
          sm: 30,
          lg: 40,
          base: 20,
        }}
        size={1200}
      >
        <Grid justify="space-between">
          <Grid.Col sm={5} span={12}>
            <Text
              ff="BauhausC,__Cairo_0685b6,__Cairo_Fallback_0685b6"
              size={43}
              weight="bold"
              color="white"
            >
              {t('common:readyToBecomeAgent')}
            </Text>
            <Text mt="xl" color="dimmed" size="lg" weight={500} mb="lg">
              {t('common:readyToBecomeAgentDesc')}
            </Text>
          </Grid.Col>
          <Grid.Col sm={6} span={12}>
            <form onSubmit={form.onSubmit(submit)}>
              <Stack>
                <TextInput
                  withAsterisk
                  radius="md"
                  className={classes.input}
                  label={t('common:firstName')}
                  placeholder={t('common:firstName')}
                  {...form.getInputProps('firstName')}
                />
                <TextInput
                  withAsterisk
                  radius="md"
                  className={classes.input}
                  label={t('common:lastName')}
                  placeholder={t('common:lastName')}
                  {...form.getInputProps('lastName')}
                />
                <TextInput
                  withAsterisk
                  radius="md"
                  className={classes.input}
                  label={`${t('common:businessEmail')} ${t(
                    'common:shouldBeRegisteredInKazawallet',
                  )}`}
                  placeholder={t('common:businessEmail')}
                  {...form.getInputProps('email')}
                />

                <Group position="apart" align="start">
                  <SelectWithFlags
                    withAsterisk
                    labelValue="code"
                    clearable
                    className={classes.input}
                    w="34%"
                    radius="md"
                    data={countries}
                    label={t('common:code')}
                    placeholder={t('common:code') as string}
                    priorityList={['US', 'CA', 'GB', 'DE', 'RU', 'SY']}
                    rightSection={!countries && <Loader size="xs" />}
                    {...form.getInputProps('code')}
                  />
                  <TextInput
                    readOnly={!form.values?.code}
                    withAsterisk
                    className={classes.input}
                    w="61%"
                    radius="md"
                    iconWidth={40}
                    label={t('common:phone')}
                    placeholder={`${t('common:phone')}`}
                    {...form.getInputProps('phone')}
                  />
                </Group>
                <TextInput
                  withAsterisk
                  radius="md"
                  className={classes.input}
                  label={t('common:marketName')}
                  placeholder={t('common:marketName')}
                  {...form.getInputProps('name')}
                />
                <TextInput
                  withAsterisk
                  radius="md"
                  className={classes.input}
                  label={t('common:jobType')}
                  placeholder={t('common:jobType')}
                  {...form.getInputProps('jobType')}
                />
                <TextInput
                  withAsterisk
                  radius="md"
                  className={classes.input}
                  label={t('common:addressDetails')}
                  placeholder={t('common:addressDetails')}
                  {...form.getInputProps('address')}
                />
                <TextInput
                  withAsterisk
                  radius="md"
                  className={classes.input}
                  label={t('common:googleMapLocation')}
                  placeholder={t('common:googleMapLocation')}
                  {...form.getInputProps('location')}
                />
                <MultiSelect
                  data={CUrrenciesList}
                  radius="md"
                  className={classes.input}
                  label={t('common:currenciesSupport')}
                  placeholder={t('common:currenciesSupport')}
                  classNames={{
                    input: classes.input,
                  }}
                  {...form.getInputProps('currencies')}
                />
                <NumberInput
                  withAsterisk
                  hideControls
                  radius="md"
                  className={classes.input}
                  label={t('common:liquidity')}
                  placeholder={t('common:liquidity')}
                  {...form.getInputProps('liquidity')}
                />
                <TextInput
                  withAsterisk
                  radius="md"
                  className={classes.input}
                  label={t('common:telegramOrWhatsapp')}
                  placeholder={t('common:telegramOrWhatsapp')}
                  {...form.getInputProps('telegramOrWhatsapp')}
                />

                <Button
                  loading={isLoading}
                  type="submit"
                  c={theme.colors.primary[7]}
                  className={styles.aHero}
                >
                  {t('common:send')}
                </Button>
              </Stack>
            </form>
          </Grid.Col>
        </Grid>
      </Container>
      <Captcha reCaptchaRef={reCaptchaRef} />
    </div>
  );
}

export default AgentForm;
