/* eslint-disable complexity */
/* eslint-disable no-nested-ternary */
/* eslint-disable max-lines */
/* eslint-disable react/require-default-props */
/* eslint-disable no-promise-executor-return */
import {
  Modal,
  Box,
  Text,
  PinInput,
  keyframes,
  useMantineTheme,
  rem,
  Stack,
  Group,
  Paper,
  createStyles,
} from '@mantine/core';
import { useEffect, useState, useRef } from 'react';
import {
  IconAlertCircle, IconCheck, IconX, IconShieldCheck,
} from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';

type Props = {
  opened: boolean;
  onClose: () => void;
  onVerified: () => void;
  title?: string;
  description?: string;
};

// Simple fade animations
const fadeIn = keyframes({
  from: {
    opacity: 0,
  },
  to: {
    opacity: 1,
  },
});

const shimmer = keyframes({
  '0%': {
    backgroundPosition: '-200px 0',
  },
  '100%': {
    backgroundPosition: '200px 0',
  },
});

// eslint-disable-next-line sonarjs/cognitive-complexity
const useStyles = createStyles((theme) => ({
  modal: {
    '.mantine-Modal-content': {
      background: theme.colorScheme === 'dark'
        ? `linear-gradient(145deg, ${theme.colors.dark[7]}, ${theme.colors.dark[6]})`
        : `linear-gradient(145deg, ${theme.white}, #f8f9fa)`,
      border: theme.colorScheme === 'dark'
        ? `1px solid ${theme.colors.dark[4]}`
        : `1px solid ${theme.colors.gray[2]}`,
      boxShadow: theme.colorScheme === 'dark'
        ? '0 25px 50px -12px rgba(0, 0, 0, 0.5)'
        : '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    },

    '.mantine-Modal-header': {
      backgroundColor: 'transparent',
      borderBottom: `1px solid ${theme.colorScheme === 'dark' ? theme.colors.dark[4] : theme.colors.gray[2]}`,
      paddingBottom: theme.spacing.md,
    },

    '.mantine-Modal-title': {
      fontSize: rem(20),
      fontWeight: 600,
      color: theme.colorScheme === 'dark' ? theme.white : theme.colors.dark[7],
    },

    '.mantine-Modal-close': {
      border: 'none',
      backgroundColor: theme.colorScheme === 'dark' ? theme.colors.dark[5] : theme.colors.gray[1],
      borderRadius: '50%',
      width: rem(36),
      height: rem(36),
      color: theme.colorScheme === 'dark' ? theme.colors.gray[4] : theme.colors.gray[6],
      transition: 'all 0.2s ease',

      '&:hover': {
        backgroundColor: theme.colorScheme === 'dark' ? theme.colors.dark[4] : theme.colors.gray[2],
        transform: 'scale(1.1)',
      },
    },
  },

  alertCard: {
    background: theme.colorScheme === 'dark'
      ? `linear-gradient(135deg, ${theme.colors.blue[9]}, ${theme.colors.blue[8]})`
      : `linear-gradient(135deg, ${theme.colors.blue[0]}, ${theme.colors.blue[1]}`,
    border: `1px solid ${theme.colorScheme === 'dark' ? theme.colors.blue[7] : theme.colors.blue[2]}`,
    borderRadius: theme.radius.lg,
    padding: theme.spacing.lg,
    position: 'relative',
    overflow: 'hidden',

    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: '-200px',
      width: '200px',
      height: '100%',
      background: theme.colorScheme === 'dark'
        ? 'linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent)'
        : 'linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent)',
      animation: `${shimmer} 2s ease-in-out infinite`,
    },
  },

  pinContainer: {
    position: 'relative',
    width: '100%',
    maxWidth: rem(280),
    margin: '0 auto',
    padding: theme.spacing.md,
    backgroundColor: theme.colorScheme === 'dark' ? theme.colors.dark[6] : theme.white,
    borderRadius: theme.radius.xl,
    border: `2px solid ${theme.colorScheme === 'dark' ? theme.colors.dark[4] : theme.colors.gray[2]}`,
    boxShadow: theme.colorScheme === 'dark'
      ? 'inset 0 2px 4px rgba(0, 0, 0, 0.3)'
      : 'inset 0 2px 4px rgba(0, 0, 0, 0.1)',
  },

  statusIcon: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: rem(60),
    height: rem(60),
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 15,
    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
    pointerEvents: 'none',
  },

  successIcon: {
    backgroundColor: theme.colors.green[6],
    animation: `${fadeIn} 0.3s ease-in-out`,
  },

  errorIcon: {
    backgroundColor: theme.colors.red[6],
    animation: `${fadeIn} 0.3s ease-in-out`,
  },

  developmentHint: {
    backgroundColor: theme.colorScheme === 'dark' ? theme.colors.yellow[9] : theme.colors.yellow[0],
    color: theme.colorScheme === 'dark' ? theme.colors.yellow[2] : theme.colors.yellow[8],
    border: `1px solid ${theme.colorScheme === 'dark' ? theme.colors.yellow[7] : theme.colors.yellow[3]}`,
    borderRadius: theme.radius.md,
    padding: `${rem(8)} ${rem(12)}`,
    fontSize: rem(12),
    fontWeight: 500,
    display: 'inline-block',
  },
}));

export function PinVerificationModal({
  opened,
  onClose,
  onVerified,
  title,
  description,
}: Props) {
  const { t } = useTranslation();
  const [pinValue, setPinValue] = useState('');
  const [pinError, setPinError] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [showError, setShowError] = useState(false);
  const pinContainerRef = useRef<HTMLDivElement>(null);
  const theme = useMantineTheme();
  const { classes } = useStyles();

  const CORRECT_PIN = '000000';

  const handleClose = () => {
    setPinValue('');
    setPinError('');
    setIsVerifying(false);
    setShowSuccess(false);
    setShowError(false);
    onClose();
  };

  // Reset state when modal opens
  useEffect(() => {
    if (opened) {
      setPinValue('');
      setPinError('');
      setIsVerifying(false);
      setShowSuccess(false);
      setShowError(false);
    }
  }, [opened]);

  // Focus first input when modal opens
  // eslint-disable-next-line consistent-return
  useEffect(() => {
    if (opened) {
      const timer = setTimeout(() => {
        if (pinContainerRef.current) {
          const firstInput = pinContainerRef.current.querySelector('input') as HTMLInputElement;
          if (firstInput) {
            firstInput.focus();
          }
        }
      }, 150);

      return () => clearTimeout(timer);
    }
  }, [opened]);

  const handlePinComplete = async (value: string) => {
    setIsVerifying(true);
    setPinError('');
    setShowError(false);

    // Simulate API call delay
    await new Promise((resolve) => setTimeout(resolve, 800));

    if (value === CORRECT_PIN) {
      setShowSuccess(true);
      // Wait for success animation
      await new Promise((resolve) => setTimeout(resolve, 1200));
      setPinValue('');
      setShowSuccess(false);
      onVerified();
      handleClose();
    } else {
      setShowError(true);
      setPinError(t('common:incorrectPin'));
      setPinValue('');

      // Focus the first PIN input after clearing the value
      setTimeout(() => {
        if (pinContainerRef.current) {
          const firstInput = pinContainerRef.current.querySelector('input') as HTMLInputElement;
          if (firstInput) {
            firstInput.focus();
          }
        }
      }, 150);

      // Hide error icon after animation
      setTimeout(() => {
        setShowError(false);
      }, 1500);
    }
    setIsVerifying(false);
  };

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      title={(
        <Group spacing="sm">
          <IconShieldCheck
            size={24}
            color={theme.colorScheme === 'dark' ? theme.colors.blue[4] : theme.colors.blue[6]}
          />
          <Text>{t('common:enterPin')}</Text>
        </Group>
      )}
      centered
      radius="xl"
      size="md"
      padding="xl"
      className={classes.modal}
      closeButtonProps={{
        size: 'lg',
      }}
    >
      <Stack spacing="xl">
        {/* Alert Section */}
        <Paper className={classes.alertCard}>
          <Group spacing="md" align="flex-start">
            <IconAlertCircle
              size={20}
              color={theme.colorScheme === 'dark' ? theme.colors.blue[3] : theme.colors.blue[6]}
            />
            <Stack spacing="xs" style={{ flex: 1 }}>
              <Text
                size="sm"
                weight={600}
                color={theme.colorScheme === 'dark' ? theme.white : theme.colors.dark[7]}
              >
                {title || t('common:securityVerificationRequired')}
              </Text>
              <Text
                size="xs"
                color={theme.colorScheme === 'dark' ? theme.colors.gray[3] : theme.colors.gray[7]}
                lineClamp={3}
              >
                {description || t('common:pciDssStandards')}
              </Text>
            </Stack>
          </Group>
        </Paper>

        {/* PIN Input Section */}
        <Stack spacing="md" align="center">
          <Text size="lg" weight={600} align="center" color={theme.colorScheme === 'dark' ? theme.white : theme.colors.dark[7]}>
            {t('common:pinDescription')}
          </Text>

          {/* Development hint */}
          <div className={classes.developmentHint}>
            Development PIN: 000000
          </div>

          {/* PIN Input Container */}
          <Box className={classes.pinContainer} ref={pinContainerRef}>
            <PinInput
              length={6}
              mask
              value={pinValue}
              onChange={(value) => {
                setPinValue(value);
                if (pinError) setPinError('');
              }}
              onComplete={handlePinComplete}
              size="lg"
              spacing="sm"
              placeholder="●"
              disabled={isVerifying}
              error={!!pinError}
              styles={() => ({
                root: {
                  justifyContent: 'center',
                  gap: rem(8),
                },
                input: {
                  width: rem(44),
                  height: rem(44),
                  fontSize: rem(18),
                  fontWeight: 700,
                  textAlign: 'center',
                  border: `2px solid ${
                    pinError
                      ? theme.colors.red[5]
                      : theme.colorScheme === 'dark'
                        ? theme.colors.dark[3]
                        : theme.colors.gray[3]
                  }`,
                  borderRadius: theme.radius.lg,
                  backgroundColor: theme.colorScheme === 'dark' ? theme.colors.dark[7] : theme.white,
                  color: theme.colorScheme === 'dark' ? theme.white : theme.colors.dark[7],
                  transition: 'all 0.2s ease',

                  '&:focus': {
                    borderColor: pinError ? theme.colors.red[5] : theme.colors.blue[5],
                    boxShadow: `0 0 0 3px ${
                      pinError
                        ? `${theme.colors.red[1]}40`
                        : `${theme.colors.blue[1]}60`
                    }`,
                    transform: 'translateY(-2px)',
                  },

                  '&[data-filled="true"]': {
                    borderColor: pinError ? theme.colors.red[5] : theme.colors.green[5],
                    backgroundColor: pinError
                      ? `${theme.colors.red[0]}40`
                      : `${theme.colors.green[0]}40`,
                    transform: 'scale(1.05)',
                  },

                  '&:disabled': {
                    backgroundColor: theme.colorScheme === 'dark' ? theme.colors.dark[6] : theme.colors.gray[1],
                    borderColor: theme.colorScheme === 'dark' ? theme.colors.dark[4] : theme.colors.gray[3],
                    opacity: 0.7,
                  },
                },
              })}
            />

            {/* Success Animation */}
            {showSuccess && (
              <div className={`${classes.statusIcon} ${classes.successIcon}`}>
                <IconCheck
                  size={28}
                  color="white"
                  strokeWidth={3}
                />
              </div>
            )}

            {/* Error Animation */}
            {showError && (
              <div className={`${classes.statusIcon} ${classes.errorIcon}`}>
                <IconX
                  size={28}
                  color="white"
                  strokeWidth={3}
                />
              </div>
            )}
          </Box>

          {/* Error Message */}
          {pinError && (
            <Text
              color="red"
              size="sm"
              align="center"
              weight={500}
              style={{
                animation: `${fadeIn} 0.3s ease-out`,
              }}
            >
              {pinError}
            </Text>
          )}

          {/* Loading State */}
          {isVerifying && (
            <Text
              size="sm"
              color="dimmed"
              align="center"
              style={{
                animation: `${fadeIn} 0.3s ease-out`,
              }}
            >
              {t('common:verifying')}
            </Text>
          )}
        </Stack>
      </Stack>
    </Modal>
  );
}
