import {
  Container, Accordion, Text, List,
} from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';
import { useStyles } from './style';

interface FaqSimpleProps {
  list: {
    question: string;
    answer: string | string[];
  }[];
}
export default function FaqSimple({ list }: FaqSimpleProps) {
  const { t } = useTranslation();
  const { classes } = useStyles();
  return (
    <Container
      py={40}
      px={{
        xs: 20,
        sm: 30,
        lg: 40,
        base: 20,
      }}
      size={1200}
    >
      <Text
        ta="center"
        ff="BauhausC,__Cairo_0685b6,__Cairo_Fallback_0685b6"
        size={43}
        weight="bold"
        variant="gradient"
        gradient={{ deg: 100, from: '#2CD4C6', to: '#8CD48B' }}
      >
        {t('common:faqMerchantTitle')}
      </Text>

      <Accordion>
        {list?.map((item) => (
          <Accordion.Item key={item.question} value={item.question}>
            <Accordion.Control className={classes.control}>
              {item.question}
            </Accordion.Control>
            <Accordion.Panel c="white">
              {typeof item.answer === 'string' ? (
                item.answer
              ) : (
                <List>
                  {item.answer.map((ans) => (
                    <List.Item c="white" key={ans}>{ans}</List.Item>
                  ))}
                </List>
              )}
            </Accordion.Panel>
          </Accordion.Item>
        ))}
      </Accordion>
    </Container>
  );
}
