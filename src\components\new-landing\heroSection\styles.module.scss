.root {
  padding-top: 100px;
  padding-bottom: 20px;
  background-size: contain;
}
.title{
  font-family: 'BauhausC';
}

.p1 {
  font-weight: 600;
  padding: 8px 12px;
  font-size: 16px;
  line-height: 1.1875;
  text-transform: uppercase;
  display: inline-block;
  letter-spacing: 0.07em;
  margin-bottom: 60px;
}

.p1 span {
  background: linear-gradient(116.49deg, #26d4ca 15.76%, #baf2b5 82.3%);
  background-clip: text;
  color: transparent;
}

.btnHero {
  display: grid;
  grid-template-columns: repeat(3, 155px);
  grid-gap: 10px;
  gap: 10px;
}
@media (max-width: 900px) {
  .btnHero {
    grid-template-columns: unset;
  }
}

.aHero {
  background-image: linear-gradient(
    90deg,
    #26d4ca,
    #90d48a,
    #26cf91,
    #33aa7e,
    #33aa7e
  );
  outline: none;
  border: none;
  box-sizing: border-box;
  font-weight: 700;
  font-size: 16px;
  line-height: 1.1875;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 3px;
  height: 48px;
  transition: all 0.3s ease-in-out;
  position: relative;
  overflow: hidden;
  background-size: 400% 100%;
}
.aHero:hover {
  background-position-x: 66%;
}

.googlePlay {
  border: 1px solid rgba(169, 184, 207, 0.2);
  transition: 0.3s ease;
  background-color: initial;
  border-radius: 3px;
  height: 48px;
  display: flex;
  font-size: 0;
  justify-content: center;
  align-items: center;
  position: relative;
}

.googlePlay:hover {
  background-color: rgba(169, 184, 207, 0.2);
}

.appStore {
  border: 1px solid rgba(169, 184, 207, 0.2);
  transition: 0.3s ease;
  background-color: initial;
  border-radius: 3px;
  height: 48px;
  display: flex;
  font-size: 0;
  justify-content: center;
  align-items: center;
  position: relative;
}

.appStore:hover {
  background-color: rgba(169, 184, 207, 0.2);
}

.imageSection {
  position: absolute;
  right: 0%;
}

.heroTwo {
  position: absolute;
  top: 15%;
  right: -9%;
}


