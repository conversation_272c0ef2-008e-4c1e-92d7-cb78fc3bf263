/**
 * request params to get payment methods api call
 */
import { NextApiRequest } from 'next';

export const returnPaymentMethodsParams = (req: NextApiRequest) => {
  const params = req.query;
  return {
    'pagination[page]': params.page,
    'pagination[pageSize]': params.pageSize,
    'pagination[start]': params.start,
    'pagination[limit]': 100,
    'populate[deposit_currencies][populate]':
      params.depositCurrencies === 'true' ? '*' : null,
    'populate[withdraw_currencies][populate]':
      params.withdrawCurrencies === 'true' ? '*' : null,
    'populate[icon][populate]': '*',
    'populate[withdraw_custom_fields][populate]':
      params.withdrawcustomFields === 'true' ? '*' : null,
    'populate[deposit_custom_fields][populate]':
      params.depositCustomFields === 'true' ? '*' : null,
    'populate[badges][populate]': '*',
    'filters[show_in_rates_bar][$eq]':
      params.showInRatesBar === 'true' ? true : null,
    'filters[deposit_currencies][uid][$eq]': params?.depositCurrencyId,
    'filters[withdraw_currencies][uid][$eq]': params?.withdrawCurrencyId,
    sort: 'order:asc',
  };
};
