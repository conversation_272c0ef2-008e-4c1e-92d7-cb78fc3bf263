import Head from 'next/head';
import useTranslation from 'next-translate/useTranslation';
import { assetBaseUrl } from '@/data';

interface SeoProps {
  title?: string | null;
  description?: string;
  customSeoTitle?: string;
}

function MetaTags({
  title = '',
  description = '',
  customSeoTitle = '',
}: SeoProps) {
  const { t } = useTranslation();
  const defaultDescription = description && description !== ''
    ? description
    : t('common:kazawalletDesc');

  const defaultImage = `${assetBaseUrl}/assets/kazawallet-meta.png`;
  const defaultTitle = title;
  const seoTitle = customSeoTitle || title || `${t('common:defaultSeoTitle')}`;
  return (
    <Head>
      {/* primary */}
      <title>
        {defaultTitle
          ? `${defaultTitle} | ${t('common:kazawallet')}`
          : `${t('common:defaultTitle')} | ${t('common:kazawallet')}`}
      </title>
      <meta name="title" content={seoTitle} />
      <meta name="description" content={defaultDescription} />
      <meta name="theme-color" content="#000D23" />
      {/* google */}
      <meta property="og:type" content="website" />
      <meta property="og:title" content={seoTitle} />
      <meta property="og:description" content={defaultDescription} />
      <meta property="og:image" content={defaultImage} />
      {/* twitter */}
      <meta property="twitter:card" content={defaultImage} />
      <meta property="twitter:title" content={seoTitle} />
      <meta property="twitter:description" content={defaultDescription} />
      <meta property="twitter:image" content={defaultImage} />
    </Head>
  );
}
MetaTags.defaultProps = {
  title: '',
  description: '',
  customSeoTitle: '',
};
export default MetaTags;
