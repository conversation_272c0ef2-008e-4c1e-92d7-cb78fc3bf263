import { assetBaseUrl } from '@/data';
import { useMantineTheme } from '@mantine/core';
import Image from 'next/image';

import { useRouter } from 'next/router';

interface PropsType {
  h: number;
  w: number;
}

export default function LogoBox({ h, w }: PropsType) {
  const theme = useMantineTheme();
  const { locale } = useRouter();
  let logo;

  if (theme.colorScheme === 'dark' && locale === 'en') {
    logo = `${assetBaseUrl}/assets/logo/logo-full-en-dark.png`;
  } else if (theme.colorScheme !== 'dark' && locale === 'en') {
    logo = `${assetBaseUrl}/assets/logo/logo-full-en-dark.png`;
  } else if (theme.colorScheme === 'dark' && locale !== 'en') {
    logo = `${assetBaseUrl}/assets/logo/logo-full-ar-dark.png`;
  } else {
    logo = `${assetBaseUrl}/assets/logo/logo-full-ar-dark.png`;
  }
  return <Image src={logo} height={h} width={w} alt="kazawallet-logo" />;
}
