/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Box,
  Card,
  Image,
  Group,
  Input,
  Text,
  useMantineTheme,
  Stack,
} from '@mantine/core';
import { Dropzone, DropzoneProps } from '@mantine/dropzone';

import useTranslation from 'next-translate/useTranslation';
import React, { useState } from 'react';

import DropzoneFile from './dropzone-file';
import { filePostResponseData } from '@/store/file/response-transformer';
import { Icon } from '../common/icon';
import { assetBaseUrl, UploadFileSize } from '@/data';

interface NewDropzoneProps {
  title: string;
  description: string[];
  error: string | undefined;
  dropzoneProps: Omit<DropzoneProps, 'children' | 'onDrop'>;
  onDelete: (deletedFile: filePostResponseData[number]) => void;
}

function NewDropzone(props: NewDropzoneProps) {
  const {
    title,
    dropzoneProps,
    description,
    error,
    onDelete,
  } = props;

  const theme = useMantineTheme();
  const [files, setFiles] = useState<filePostResponseData>([]);
  const [rejectFileMessage, setRejectFileMessage] = useState('');
  const { t } = useTranslation();

  const selectedFileMessage = files.length === 1
    ? `${files.length} ${t('common:fileSelected')}`
    : `${files.length} ${t('common:filesSelected')}`;

  const dropzoneMessage = files.length === 0
    ? title || t('common:dropYourFilesHere')
    : selectedFileMessage;

  function handleDeleteFile(value: number) {
    const deletedFile = files.find((v) => v.id === value);
    if (onDelete && deletedFile) onDelete(deletedFile);
    const newFiles = files.filter((file) => file.id !== value);
    setFiles(newFiles);
  }

  //* **************************************************** */
  const renderMediaTitles = (filesParam: filePostResponseData) => filesParam.map((v) => (
    <DropzoneFile
      key={v.id}
      deleteFile={(fileId) => handleDeleteFile(fileId)}
      file={v}
    />
  ));

  const handleSelectFile = (data: any) => {
    setRejectFileMessage('');
    const formData = new FormData();
    data.map((file: any) => formData.append('files', file));
  };
  const renderDescriptions = (desc?: string[]) => {
    if (!desc) return [];
    return desc.map((v) => (
      <Text
        key={v}
        size="xs"
        lineClamp={1}
        weight={500}
        color="dimmed"
        inline
        maw={{
          sm: 230,
          md: 175,
          lg: 230,
          xs: 230,
          base: 160,
        }}
        mih={25}
      >
        {v}
      </Text>
    ));
  };
  return (
    <>
      <Card radius="lg" p={0} sx={{ borderBottom: 0 }}>
        <Dropzone
          maxSize={1024 * UploadFileSize * 1024}
          // loading={isLoading}
          sx={{
            zIndex: 1,
            borderColor:
              theme.colorScheme === 'light'
                ? theme.colors.gray[2]
                : theme.colors.gray[8],
            borderBottomLeftRadius: 10,
            borderBottomRightRadius: 10,
          }}
          p={0}
          {...dropzoneProps}
          onDrop={handleSelectFile}
          mih={70}
          radius="lg"
          onReject={(errors) => {
            setRejectFileMessage(
              t(`errors:${errors[0].errors[0].code}`) as string,
            );
          }}
        >
          <Group p="xs" spacing="xs" align="center">
            <Dropzone.Accept>
              <Icon
                icon="upload"
                size={50}
                color={
                  theme.colors[theme.primaryColor][
                    theme.colorScheme === 'dark' ? 4 : 6
                  ]
                }
              />
            </Dropzone.Accept>
            <Dropzone.Reject>
              <Icon
                icon="x"
                size={50}
                color={theme.colors.red[theme.colorScheme === 'dark' ? 4 : 6]}
              />
            </Dropzone.Reject>
            <Dropzone.Idle>
              <Image src={`${assetBaseUrl}/assets/icons/img.png`} width={30} mt="xs" alt="file-icon" />
            </Dropzone.Idle>

            <div style={{ textAlign: 'left' }}>
              <Text size={14} weight={500} inline mt="md" align="left">
                {dropzoneMessage}
              </Text>
              <Stack spacing={0} mt="xs">
                {renderDescriptions(description)}
              </Stack>
            </div>
          </Group>
        </Dropzone>
        {/* {isLoading && <Text p="xs">{t('common:uploading')}</Text>} */}
        {(!!error || !!rejectFileMessage) && (
          <Input.Error p="xs">{error || rejectFileMessage}</Input.Error>
        )}
      </Card>
      <Box mt="xs">{renderMediaTitles(files)}</Box>
    </>
  );
}
export default NewDropzone;
