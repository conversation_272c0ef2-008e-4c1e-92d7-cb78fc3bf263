import { TranslatedTextValue } from '@/components/common/translated-text-value';
import {
  Group, TextInput, Text, ActionIcon,
} from '@mantine/core';
import { UseFormReturnType } from '@mantine/form';
import { IconPlus, IconTrash } from '@tabler/icons-react';

interface Props {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  form: UseFormReturnType<any>;
  fieldIndex: number;
}

export default function MultipleFieldsForm({ form, fieldIndex }: Props) {
  const fieldName = form.values?.fields[fieldIndex].name;
  const fieldNameAr = form.values?.fields[fieldIndex].nameAr;
  const fields = form.values?.fields[fieldIndex].value?.map(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (i: any, index: number) => (
      <Group mt="xs" key={i?.id}>
        <TextInput
          radius="lg"
          label=""
          placeholder=""
          {...form.getInputProps(`fields.${fieldIndex}.value.${index}`)}
        />
        <ActionIcon
          radius="lg"
          onClick={() => form.removeListItem(`fields.${fieldIndex}.value`, index)}
          color="red"
          variant="outline"
        >
          <IconTrash size="1rem" />
        </ActionIcon>
      </Group>
    ),
  );

  return (
    <div>
      <Text mb="xs" size="sm" weight={500} tt="capitalize">
        <TranslatedTextValue keyEn={fieldName} keyAr={fieldNameAr} />
      </Text>
      <div>{fields}</div>

      <Group mt="md">
        <ActionIcon
          radius="lg"
          color="blue"
          variant="outline"
          onClick={() => form.insertListItem(`fields.${fieldIndex}.value`, '')}
        >
          <IconPlus size="1rem" />
        </ActionIcon>
      </Group>
    </div>
  );
}
