import { TranslatedTextValue } from '@/components/common/translated-text-value';
import { CurrenciesApiResponse } from '@/store/currencies';

import {
  Box, Button, Group, Image, Modal, Stack, Text,
} from '@mantine/core';
import { IconExclamationCircle } from '@tabler/icons-react';
import currency from 'currency.js';
import useTranslation from 'next-translate/useTranslation';
import React from 'react';

interface RedeemConfirmPopupProps {
  loading: boolean;
  opened: boolean;
  setOpened: (v: boolean) => void;
  onClick: () => void;
  giftCard: {
    amount: number;
    currency: CurrenciesApiResponse['data'][0];
  };
}
function RedeemConfirmPopup(props: RedeemConfirmPopupProps) {
  const {
    opened, setOpened, loading, onClick, giftCard,
  } = props;
  const { t } = useTranslation();
  const close = () => setOpened(false);

  return (
    <Modal radius="lg" centered opened={opened} onClose={close}>
      <Box mb="md">
        <Group position="center">
          <IconExclamationCircle size={48} color="#1C6092" />
        </Group>
        <Text fw={700} size="lg" ta="center">
          {t('common:attention')}
        </Text>
      </Box>
      <Text>
        {' '}
        {t('common:redeemThisGift')}
      </Text>
      <Stack spacing="sm" mt="xs">
        <Group>
          <Text tt="capitalize" weight={500} miw={100}>
            {t('common:currency')}
            {' '}
            :
          </Text>
          <Group align="center">
            <Image width={40} height={40} src={giftCard?.currency?.image} />
            <Text>
              <TranslatedTextValue
                keyEn={giftCard?.currency?.label}
                keyAr={giftCard?.currency?.labelAr}
              />
            </Text>
          </Group>
        </Group>
        <Group>
          <Text tt="capitalize" weight={500} miw={100}>
            {t('common:amount')}
            {' '}
            :
          </Text>

          <Text>
            {`${currency(giftCard?.amount, {
              precision: giftCard?.currency?.precision ?? 0,
              symbol: '',
            }).format()} ${giftCard?.currency?.symbol}`}
          </Text>
        </Group>
      </Stack>

      <Group mt="lg" position="right">
        <Button radius="lg" variant="outline" onClick={close}>
          {t('common:cancel')}
        </Button>
        <Button
          radius="lg"
          variant="filled"
          loading={loading}
          onClick={onClick}
        >
          {t('common:redeem')}
        </Button>
      </Group>
    </Modal>
  );
}

export default RedeemConfirmPopup;
