/**
 * This handler to upload file.
 */
import { apiEndpoints, apiMethods, httpCode } from '@/data';
import { BackendClient } from '@/lib';
import { filesPostApiResponseSchema } from '@/store/file';
import createApiError from '@/utils/api-utils/create-api-error';
import createApiResponse from '@/utils/api-utils/create-api-response';
import { getJwt } from '@/utils/api-utils/jwt';
import type { NextApiRequest, NextApiResponse } from 'next';

export const config = {
  api: {
    bodyParser: false,
  },
};
const POST = async (
  req: NextApiRequest,
  res: NextApiResponse,
) => {
  const { token } = await getJwt(req);
  try {
    const data = await BackendClient(req).post(apiEndpoints.uploadFile(), req, {
      headers: {
        'Content-Type': req.headers['content-type'],
        authorization: token,
      },
    });
    return createApiResponse(res, filesPostApiResponseSchema, {
      data: data.data,
    });
  } catch (e) {
    const error = createApiError({
      error: e,
    });
    return res.status(error.code).json(error);
  }
};
async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  if (req.method === apiMethods.POST) return POST(req, res);

  const error = createApiError({
    error: {},
  });
  return res.status(httpCode.INTERNAL_SERVER_ERROR).json(error);
}

export default handler;
