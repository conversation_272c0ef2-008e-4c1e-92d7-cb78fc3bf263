import { Icon } from '@/components/common/icon';
import {
  Center, Group, Image, Text,
} from '@mantine/core';
import { SpotlightAction } from '@mantine/spotlight';
import React from 'react';

function ActionItem({ action }: { action: SpotlightAction }) {
  return (
    <Group noWrap>
      {action.image && (
        <Center>
          <Image src={action.image} alt={action.title} width={30} height={30} radius={50} />
        </Center>
      )}
      {action.icon && (
        <Center>
          <Icon icon={action.icon} size={30} color="" />
        </Center>
      )}

      <div style={{ flex: 1 }}>
        <Text>{action.title}</Text>
        {action.description && (
          <Text color="dimmed" size="xs">
            {action.description}
          </Text>
        )}
      </div>
    </Group>
  );
}

export default ActionItem;
