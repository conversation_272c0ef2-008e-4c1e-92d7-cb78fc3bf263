/**
 * This component used to show login button to handle if the user not logged in before.
 * Iframe pages require auth.
 * When add iframe pages to another website if the iframe was not logging in then will navigate to this page and on click
 *  "login" button will login and return to iframe page
 * This page doesn't display if iframe pages are logging in.
 */
import { Button, Stack } from '@mantine/core';
import { signIn } from 'next-auth/react';
import useTranslation from 'next-translate/useTranslation';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';

function Login() {
  const { t } = useTranslation();
  const { query } = useRouter();
  const [loading, setLoading] = useState(false);
  // url the previous page before navigate to this page
  // after login will navigate to this url
  const callbackUrl = query.callbackUrl as string;
  // stop loader on demand
  useEffect(() => () => {
    setLoading(false);
  }, []);
  // on login button click handler
  // wil set loading true and signin with callback url
  const onLoginClick = () => {
    setLoading(true);
    signIn('keycloak', { callbackUrl });
  };

  return (
    <Stack h="100vh" align="center" justify="center">
      <Button
        loading={loading}
        sx={{
          ':hover': {
            backgroundColor: '#79ca53',
          },
        }}
        w={150}
        bg="#79ca53"
        size="lg"
        onClick={onLoginClick}
      >
        {t('common:signIn')}
      </Button>
    </Stack>
  );
}

export default Login;
