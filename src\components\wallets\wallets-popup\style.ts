import { createStyles } from '@mantine/core';

export const useStyles = createStyles((theme) => ({
  button: {
    display: 'flex',
    alignItems: 'center',
    width: '100%',
    transition: 'background-color 150ms ease, border-color 150ms ease',
    padding: theme.spacing.sm,
    border: '0.01rem solid ',
    borderColor:
      theme.colorScheme === 'dark'
        ? theme.colors.gray[9]
        : theme.colors.gray[2],
    borderRadius: 4,
  },
  checkedButton: {
    borderColor: theme.colors.blue[6],
    backgroundColor: 'rgba(34, 139, 230, 0.1)',
  },
  disabledButton: {
    borderColor:
      theme.colorScheme === 'dark' ? theme.colors.gray : theme.colors.gray[1],
    backgroundColor:
      theme.colorScheme === 'dark' ? theme.colors.gray : theme.colors.gray[0],
  },
  body: {
    flex: 1,
    marginLeft: theme.spacing.md,
  },
}));
