{"emailPasswordNotCorrect": "Email or Password not correct", "nameTwoLetters": "Name should have at least 2 letters", "invalidEmail": "Invalid email", "passwordEightCharacters": "Password should be at least 8 characters", "passwordsDontMatch": "Passwords don't match", "acceptTerms": "Please accept to continue", "loginBlocked": "Your account is blocked, please contact us", "loginNotConfirmed": "Your account is not confirmed yet", "loginWrongCredentials": "Email or password is wrong", "signupEmailTaken": "Email already exists", "EMAIL_FOUND_IN_KASAUTH": "Email already exists", "resetPasswordIncorrectCode": "Link is not correct or expired", "userBlocked": "This email is blocked", "alreadyConfirmed": "This email already confirmed", "required": "This field can't be empty", "multipeFeilsdRequired": "These fields can't be empty", "multipeFeilsdMinimum": "At least one item must be added", "passwordStrength": "Weak Password", "includesNumber": "Includes number", "includesUpperCaseLetter": "Includes uppercase letter", "includesLowerCaseLetter": "Includes lowercase letter ", "includesSpecialSymbol": "Includes special symbol", "atLeast8Char": "Includes at least 8 characters", "currentPasswordInvalid": "The provided current password is invalid", "invalidValue": "Invalid value", "USER_NOT_FOUND": "User Account Not Found", "LOW_BALANCE": "Low Balance", "ACCOUNT_ID_CANNOT_BE_CHANGED": "Account Id Can't Be Changed", "CURRENCY_AND_PAYMENT_METHOD_ARE_REQUIRED": "Currency And Payment Method Are Required", "EXCHANGE_FROM_THIS_CURRENCY_NOT_ALLOWED": "Exchange From This Currency Not Allowed", "EXCHANGE_TO_THIS_CURRENCY_NOT_ALLOWED": "Exchange To This Currency Not Allowed", "TRANSFER_FROM_THIS_CURRENCY_NOT_ALLOWED": "Transfer From This Currency Not Allowed", "TRANSFER_TO_THIS_CURRENCY_NOT_ALLOWED": "Transfer To This Currency Not Allowed", "AMOUNT_IS_OUT_OF_ALLOWED_RANGE": "Amount Is Out Of Allowed Range", "TRANSFER_TO_ANOTHER_CURRENCY_NOT_ALLOWED": "Transfer To Another Currency Not Allowed", "EXCHANGE_TO_ANOTHER_USER_NOT_ALLOWED": "Exchange To Another Currency Not Allowed", "EXCHANGE_TO_THE_SAME_CURRENCY_NOT_ALLOWED": "Exchange To The Same Currency Not Allowed", "TRANSFER_TO_THE_SAME_USER_NOT_ALLOWED": "Transfer To The Same User Not Allowed", "DEPOSIT_NOT_ALLOWED_FOR_THIS_CURRENCY": "Deposit Not Allowed For This Currency", "WITHDRAW_NOT_ALLOWED_FOR_THIS_CURRENCY": "Withdraw Not Allowed For This Currency", "CURRENCY_NOT_AVAILABLE": "Currency Not Available", "CHANGING_CURRENCY_NOT_ALLOWED": "Changing Currency Not Allowed", "CHANGING_PAYMENT_METHOD_NOT_ALLOWED": "Changing Payment Method Not Allowed", "USERS_CAN_NOT_BE_EMPTY": "Users Can't Be Empty", "CURRENCIES_CAN_NOT_BE_EMPTY": "Currencies Can't Be Empty", "RATES_NOT_AVAILABLE": "Rates Not Available", "CURRENCY_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON> Not Found", "CURRENCY_IS_NOT_ACTIVE": "Currency Is Not Active", "AMOUNT_MUST_BE_GREATER_THAN_THE_TRANSFER_MINIMUM_LIMIT": "Amount Must Be Greater Than The Transfer Minimum Limit", "AMOUNT_MUST_BE_SMALLER_THAN_THE_TRANSFER_MAXIMUM_LIMIT": "Amount Must Be Smaller Than The Transfer Maximum Limit", "AMOUNT_MUST_BE_GREATER_THAN_THE_EXCHANGE_MINIMUM_LIMIT": "Amount Must Be Greater Than The Exchange Minimum Limit", "AMOUNT_MUST_BE_SMALLER_THAN_THE_EXCHANGE_MAXIMUM_LIMIT": "Amount Must Be Smaller Than The Exchange Maximum Limit", "AN_ERROR_OCCURRED_DURING_SAVE_FILE": "An Error Occurred During Save File", "VALIDATION_ERROR": "Some Data Is Wrong", "PAYMENT_LINK_EXPIRED": "Payment Link Expired", "invalidCode": "Invalid Code", "generalError": "General <PERSON><PERSON><PERSON>", "fileSizeError": "Image size should be less than 5 MB", "file-too-large": "Image size should be less than 5 MB", "captchaError": "Wrong Captcha", "maxFeedbackLength": "The text input should not exceed 500 characters.", "feedbackError": "Please try again later", "noNotification": "There is no notification to show", "shouldEditFile": "There are errors in your file data, fix them and upload the file again to complete the transfer.", "ERROR_MISS_INFO_TO_CHECK_OTP": "Error miss information to check verification code", "ERROR_USER_DOES_NOT_HAVE_ONE_TIME_PASSWORD": "Error the verification code is not initialized", "ERROR_MANY_ATTEMPTS_TO_USE_OTP": "Error many attempts to use verification code", "ERROR_INVALID_OTP_PASSWORD": "Error wrong verification code", "ERROR_EXPIRED_OTP_PASSWORD": "Error the verification code is expired", "ERROR_INVALID_REQUEST_MISS_NETWORK_INFO": "Error invalid request check the address", "ERROR_MANY_ATTEMPTS_TO_GENERATE_OTP": "Error many attempts to send verification code, you can send it once every 5 minutes.", "GIFT_CARD_IS_ALREADY_USED": "Error gift card is already used", "GIFT_CARD_NOT_FOUND": "Error gift card is not found", "limit-error": "Oops! It looks like your account tier doesn't allow this action. You can check your limits in the settings page.", "coderError": "Enter a valid card code in 16 characters of digits and letters.", "redeemCodeError": "This gift card redemption code entered is incorrect or has been redeemed already. Please enter a valid redemption code consisting of 16 characters in digits and letters", "pleaseEnterValidNumbers": "Please enter valid numbers", "UNAUTHORIZED_ACTION": "Error unauthorized action", "ERROR_PAYMENT_NOT_FOUND": "Error payment not found", "ERROR_PAYMENT_INTERNAL_INVALID": "Error invalid origin payment", "ERROR_PAYMENT_IS_ALREADY_USED": "Operation is already used!", "invalidUrl": "Invalid url", "ERROR_PASS_THE_LIMIT": "You have exceeded the allowed limit", "ERROR_INVALID_CUSTOM_FIELDS": "You sent invalid data", "invalidQrCodeFormatInvalidUrl": "Invalid QR code format", "invalidQrCodeFormatMissingAccountId": "QR code missing account ID", "noQrCodeFoundInImage": "No QR code found in the selected image", "invalidImageFile": "Invalid image file", "failedToProcessImage": "Failed to process image", "invalidScanResult": "Invalid scan result", "unknownScannerError": "Unknown scanner error", "cameraPermissionDenied": "Camera access denied. Please allow camera access to scan QR codes.", "cameraNotAvailable": "Camera not available. Please check if your device has a camera and it's not being used by another application.", "cameraAccessError": "Unable to access camera. Please check your camera permissions and try again.", "cameraNotFound": "No camera found on this device.", "cameraInUse": "Camera is currently being used by another application. Please close other apps using the camera and try again."}