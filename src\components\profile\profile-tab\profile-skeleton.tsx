import {
  Group, Paper, Skeleton, Stack,
} from '@mantine/core';
import React from 'react';

function ProfileSkeleton() {
  return (
    <Stack maw={650} align="stretch" w="100%" m="auto">
      <Skeleton radius={60} height="120px" width="120px" m="auto" />
      <Paper p="md" withBorder radius="lg">
        <Skeleton radius="lg" height="20px" mb={20} maw={400} />
        <Skeleton radius="lg" height="40px" mb={30} />
        <Group mb={30}>
          <Skeleton
            miw={250}
            w={{ base: '100%', sm: '48%' }}
            radius="lg"
            height="40px"
          />
          <Skeleton
            miw={250}
            w={{ base: '100%', sm: '48%' }}
            radius="lg"
            height="40px"
          />
        </Group>
        <Group>
          <Skeleton
            miw={250}
            w={{ base: '100%', sm: '48%' }}
            radius="lg"
            height="40px"
          />
          <Skeleton
            miw={250}
            w={{ base: '100%', sm: '48%' }}
            radius="lg"
            height="40px"
          />
        </Group>
      </Paper>
    </Stack>
  );
}

export default ProfileSkeleton;
