export interface ErrorFrontendType {
  response: {
    status: number | string;
    data: {
      message: {
        key: string;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        params?: { [key: string]: any };
      };
      key: string;
      code: number | string;
    };
  };
}

export type BackendErrorBody = {
  data: null;
  error: {
    status: number;
    name: string;
    message: string;
    details: {
      key?: string;
      params?: Record<string, string | string[]>;
    };
  };
};

export type ErrorItem = {
  message: string;
  key: string;
};

export enum BlockedErrorKeys {
  USER_BLOCKED = 'USER_IS_BLOCKED',
  USER_BLOCKED_CHANGE_IP = 'USER_IS_BLOCK_DU_TO_CHANGING_THE_IP',
}
export enum LimitsErrorKeys {
  ERROR_PASS_THE_LIMIT = 'ERROR_PASS_THE_LIMIT',
}
export enum RedeemErrorKeys {
  GIFT_CARD_NOT_FOUND = 'GIFT_CARD_NOT_FOUND',
}
