import { ROUTES } from '@/data';
import { Carousel } from '@mantine/carousel';
import { Image, Stack } from '@mantine/core';
import Autoplay from 'embla-carousel-autoplay';
import Link from 'next/link';
import React, { useRef } from 'react';

interface BannersSliderProps {
  banners: string[];
}
function BannersSlider({ banners }: BannersSliderProps) {
  const autoplay = useRef(Autoplay({ delay: 5000 }));

  return (
    <Carousel
      slideSize="100%"
      slideGap="md"
      loop
      align="start"
      plugins={[autoplay.current]}
      onMouseEnter={autoplay.current.stop}
      onMouseLeave={autoplay.current.reset}
    >
      {banners?.map((banner) => (
        <Carousel.Slide key={banner}>
          <Link href={`${ROUTES.merchant.path}#merchant-form`}>
            <Stack h="100%" justify="center">
              <Image mx="auto" mah={200} maw={1000} radius="lg" src={banner} />
            </Stack>
          </Link>
        </Carousel.Slide>
      ))}
    </Carousel>
  );
}

export default BannersSlider;
