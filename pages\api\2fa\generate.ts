/**
 * This handlers used to generate 2fa qr code using google authenticator 2fa.
 */
import { apiMethods, httpCode } from '@/data';
import { NextApiRequest, NextApiResponse } from 'next';
import { authenticator } from 'otplib';
import { promisify } from 'util';
import { toDataURL } from 'qrcode';
import createApiError from '@/utils/api-utils/create-api-error';
import { verifyCode } from '@/utils/2fa/verify-code';
import { deleteCookie, getCookie } from 'cookies-next';
import { getJwt } from '@/utils/api-utils/jwt';

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const authorization = (await getJwt(req)).token;
  const { email } = await getJwt(req);
  // This function to generate new google authenticator code after get the code sent by email  and return authenticator token and qr
  if (req.method === apiMethods.POST) {
    try {
      const qrCode = req?.query?.qrCode;
      const sendCode = getCookie('email-code', { req, res });
      if (`${qrCode}` !== sendCode) {
        throw new Error('invalidCode');
      }
      const token = authenticator.generateSecret(32);
      const uri = authenticator.keyuri(
        email ?? 'wallet-user',
        'kazawallet',
        token,
      );
      const pToDataUrl = promisify(toDataURL);
      const qrCodeUrl = await pToDataUrl(uri);
      deleteCookie('email-code', { req, res });
      return res.status(httpCode.SUCCESS).json({
        token,
        qr: qrCodeUrl,
      });
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }
  // This function to return authenticator token and qr after get the code sent by google authenticator app
  if (req.method === apiMethods.GET) {
    try {
      const qrCode = req?.query?.qrCode;
      const token = await verifyCode(qrCode as string, authorization, req);
      if (token) {
        const uri = authenticator.keyuri(
          email ?? 'wallet-user',
          'kazawallet',
          token,
        );
        const pToDataUrl = promisify(toDataURL);
        const qrCodeUrl = await pToDataUrl(uri);
        return res.status(httpCode.SUCCESS).json({
          token,
          qr: qrCodeUrl,
        });
      }
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }
  const error = createApiError({ error: '' });
  return res.status(httpCode.INTERNAL_SERVER_ERROR).json(error);
};
export default handler;
