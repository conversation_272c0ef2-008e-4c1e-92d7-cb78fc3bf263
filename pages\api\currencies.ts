/**
 * This handler to get all currencies.
 */
import { apiEndpoints, httpCode } from '@/data';
import { useUserBadges as getUserBadges } from '@/hooks';
import { BackendClient } from '@/lib';
import {
  currenciesApiResponseSchema,
  returnCurrenciesParams,
} from '@/store/currencies';
import createApiError from '@/utils/api-utils/create-api-error';
import createApiResponse from '@/utils/api-utils/create-api-response';
import { getJwt } from '@/utils/api-utils/jwt';
import { NextApiRequest, NextApiResponse } from 'next';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token } = await getJwt(req);
  if (req.method === 'GET') {
    const params = await getUserBadges(req, res);
    try {
      const { data } = await BackendClient(req).get(apiEndpoints.currencies(), {
        headers: {
          authorization: token,
        },
        params: {
          ...returnCurrenciesParams(req),
          ...params?.params,
        },
      });

      return createApiResponse(res, currenciesApiResponseSchema, data);
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }
  const error = createApiError({ error: '' });
  return res.status(httpCode.INTERNAL_SERVER_ERROR).json(error);
}

export default handler;
