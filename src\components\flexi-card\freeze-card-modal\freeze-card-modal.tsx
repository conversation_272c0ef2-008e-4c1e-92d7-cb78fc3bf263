import {
  <PERSON><PERSON>, <PERSON>, <PERSON>ton, Group, Box,
  CloseButton,
  Center,
} from '@mantine/core';
import { IconSnowflake } from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';
import React from 'react';

interface FreezeCardModalProps {
  opened: boolean;
  onClose: () => void;
  onFreeze: () => void;
}

export function FreezeCardModal({ opened, onClose, onFreeze }: FreezeCardModalProps) {
  const { t } = useTranslation();

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      withCloseButton={false}
      centered
      size="md"
      radius="lg"
      padding={0}
      title={t('common:freezingYourCard')}
      styles={{
        body: {
          padding: 0,
        },
        header: {
          display: 'none',
        },
        title: {
          fontSize: '15px',
        },
      }}
    >
      <Box p={24} pos="relative">
        <CloseButton
          onClick={onClose}
          size="lg"
          variant="subtle"
          color="gray"
          pos="absolute"
          top={16}
          right={16}
          styles={{
            root: {
              color: '#9CA3AF',
              '&:hover': {
                backgroundColor: 'transparent',
                color: '#6B7280',
              },
            },
          }}
        />

        <Center display="flex" mb={24}>
          <IconSnowflake size={64} stroke={1.5} color="#6892d7ff" />
        </Center>

        <Box mb={32}>
          <Text size="md" lh={1.5} mb={16}>
            {t('common:freezeCardDescription1')}
          </Text>

          <Text size="md" lh={1.5}>
            {t('common:freezeCardDescription2')}
          </Text>
        </Box>

        <Group spacing={12} grow>
          <Button
            variant="subtle"
            onClick={onClose}
            size="md"
            radius="sm"
            h={48}
          >
            {t('common:cancel')}
          </Button>
          <Button
            onClick={onFreeze}
            size="md"
            radius="sm"
            h={48}
            styles={{
              root: {
                backgroundColor: '#f9dbdbff',
                color: '#c32020ff',
                border: 'none',
                fontWeight: 500,
                '&:hover': {
                  backgroundColor: '#ffa9a9ff',
                },
              },
            }}
          >
            {t('common:freezeCard')}
          </Button>
        </Group>
      </Box>
    </Modal>
  );
}
