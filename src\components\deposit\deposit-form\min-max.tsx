import { Group, Text } from '@mantine/core';
import currency from 'currency.js';
import useTranslation from 'next-translate/useTranslation';
import React from 'react';

interface Props {
  minAmount: number;
  maxAmount: number|null;
  symbol: string|null;
  currencyPrecision: number | null;
}
function MinMaxAmount({
  maxAmount, minAmount, symbol, currencyPrecision,
}: Props) {
  const { t } = useTranslation();
  return (
    <Group spacing="xs">
      <div>
        <Text span weight={500} color="dimmed">
          {t('common:min')}
          {' '}
          {currency(minAmount ?? 0, {
            symbol: '',
            precision: currencyPrecision ?? 2,
          }).format()}
        </Text>
        <Text mx={4} span weight={500} color="dimmed">
          {symbol}
        </Text>
      </div>
      {maxAmount && (
        <div>
          -
          {' '}
          <Text span weight={500} color="dimmed">
            {t('common:max')}
            {' '}
            {currency(maxAmount, {
              symbol: '',
              precision: currencyPrecision ?? 2,
            }).format()}
          </Text>
          <Text mx={4} span weight={500} color="dimmed">
            {symbol}
          </Text>
        </div>
      )}
    </Group>
  );
}

export default MinMaxAmount;
