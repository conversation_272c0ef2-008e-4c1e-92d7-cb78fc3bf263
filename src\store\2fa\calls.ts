import { ApiClient } from '@/lib';
import { handleApiError } from '@/utils/api-utils/handle-backend-error';

// api calls for generate and get and verify google authenticator code

enum queryKeys {
  generate = 'generate-2fa',
  verify = 'verify-2fa',
  qrCode = 'qr-code',
}

//* **************************************************************** */
/**
 *
 * @param qrCode
 * @description
 *  function calls the handler in this route '/2fa/generate' with "post" method
 *   to generate qr code and token using google authenticator
 * @returns qr code and authenticator token
 */
const generate2faTokenRequest = (qrCode: string) => ApiClient.post('/2fa/generate', {}, {
  params: {
    qrCode,
  },
})
  .then((res) => res.data)
  .catch((e) => {
    handleApiError(e);
    throw e.response.data;
  });

export const generate2faTokenMutation = () => ({
  mutationKey: [queryKeys.generate],
  mutationFn: (qrCode: string) => generate2faTokenRequest(qrCode),
});

//* **************************************************************** */
/**
 *
 * @param qrCode
 * @description
 *  function calls the handler in this route '/2fa/generate' with "get" method
 *   to return user qr code and authenticator token.
 * @returns qr code and authenticator token
 */
const get2faTokenRequest = (qrCode: string) => ApiClient.get('/2fa/generate', {
  params: {
    qrCode,
  },
})
  .then((res) => res.data)
  .catch((e) => {
    handleApiError(e, true);
    throw e.response.data;
  });

export const get2faTokenQuery = () => ({
  mutationKey: [queryKeys.qrCode],
  mutationFn: (qrCode: string) => get2faTokenRequest(qrCode),
});

//* **************************************************************** */
/**
 *
 * @param qrCode
 * @description
 *  function calls the handler in this api route '/2fa/verify' with "post" method to verify code.
 * @returns success if the code valid and error if it isn't
 */
const verify2faTokenRequest = (qrCode: string) => ApiClient.post('/2fa/verify', {}, {
  params: {
    qrCode,
  },
})
  .then((res) => res.data)
  .catch((e) => {
    handleApiError(e);
    throw e.response.data;
  });

export const verify2faTokenMutation = () => ({
  mutationKey: [queryKeys.verify],
  mutationFn: (qrCode: string) => verify2faTokenRequest(qrCode),
});
//* **************************************************************** */
