/* eslint-disable @typescript-eslint/no-explicit-any */
import ColorSchemeComponent from './color-scheme-component';
import { ColorSchemeComponentProps } from './types';

interface FactoryProps<T extends (...args: any) => any>
  extends Omit<ColorSchemeComponentProps<T>, 'props' | 'children'> {
  defaultProps?: ColorSchemeComponentProps<T>['props'];
}
const Factory = <T extends (...args: any) => any>(props: FactoryProps<T>) => {
  const { component, defaultProps = {}, sharedProps = {} } = props;

  return (childProps: Omit<ColorSchemeComponentProps<T>, 'component'>) => {
    const {
      props: finalProps,
      sharedProps: childSharedProps,
      children,
    } = childProps;
    return (
      <ColorSchemeComponent
        component={component}
        props={{ ...defaultProps, ...finalProps }}
        sharedProps={{ ...sharedProps, ...childSharedProps } as any}
      >
        {children}
      </ColorSchemeComponent>
    );
  };
};
export default Factory;
