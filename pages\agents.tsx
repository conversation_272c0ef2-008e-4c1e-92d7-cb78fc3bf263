/**
 * This component renders agent page.
 */
import MetaTags from '@/components/common/meta-tags';
import { Layout } from '@/components/layout/layout';
import { FaqSimple } from '@/components/merchant-agent-page/faq';
import { Features } from '@/components/merchant-agent-page/features';
import { AgentForm } from '@/components/merchant-agent-page/agent-form';
import { Integration } from '@/components/merchant-agent-page/wallet-integration';

import useTranslation from 'next-translate/useTranslation';
import { AgentFees } from '@/components/merchant-agent-page/agent-fees';
import { useRouter } from 'next/router';
import { assetBaseUrl } from '@/data';

export default function Merchant() {
  const { t } = useTranslation();
  const { locale } = useRouter();
  const isAr = locale === 'ar';
  return (
    <div
      style={{
        backgroundColor: 'rgb(0, 13, 35)',
      }}
    >
      <MetaTags
        title={t('common:kazawalletAgentProgram')}
        description={t('common:agentPageSeoDesc')}
        customSeoTitle={t('common:agentPageSeoTitle')}
      />
      <Layout>
        <Integration
          withImageBackground={false}
          title={t('common:whyBecomeAgent')}
          image={`${assetBaseUrl}/assets/agent/why-become-merchants.webp`}
          description={t('common:whyBecomeAgentDesc')}
          actions={[
            {
              title: t('common:joinAgentToday'),
              isBlank: false,
              link: '#agent-form',
            },
          ]}
        />
        <Integration
          withImageBackground={false}
          title={t('common:howDosAgentProgramWork')}
          image={`${assetBaseUrl}/assets/agent/benefits.webp`}
          description={t('common:howDosAgentProgramWorkDesc')}
          actions={[
            {
              title: t('common:watchHowItWorks'),
              isBlank: true,
              link: 'https://youtu.be/1LhWEMcDa1M',
            },
            {
              title: t('common:readTheFullGuide'),
              isBlank: true,
              link: isAr
                ? 'https://blog.kazawallet.com/ar/%D8%A8%D8%B1%D9%86%D8%A7%D9%85%D8%AC-%D9%88%D9%83%D9%84%D8%A7%D8%A1-%D9%83%D8%B0%D8%A7%D9%88%D8%A7%D9%84%D9%8A%D8%AA/'
                : 'https://blog.kazawallet.com/kazawallet-agents-program/',
            },
          ]}
        />
        <Features
          benefits={[
            {
              icon: 'file-invoice',
              title: t('common:userFriendlyPlatform'),
              description: t('common:userFriendlyPlatformDesc'),
            },
            {
              icon: 'coins',
              title: t('common:highCommissions'),
              description: t('common:highCommissionsDesc'),
            },
            {
              icon: 'arrows-exchange',
              title: t('common:instantUpdates'),
              description: t('common:instantUpdatesDesc'),
            },
            {
              icon: 'info-octagon',
              title: t('common:dedicatedSupport'),
              description: t('common:dedicatedSupportDesc'),
            },
          ]}
        />
        <AgentForm />
        <AgentFees />
        <FaqSimple
          list={[
            {
              question: t('common:agentQ1'),
              answer: [t('common:agentA11'), t('common:agentA12'), t('common:agentA13')],
            },
            {
              question: t('common:agentQ2'),
              answer: [t('common:agentA21')],
            },
            {
              question: t('common:agentQ3'),
              answer: [t('common:agentA31'), t('common:agentA32')],
            },
            {
              question: t('common:agentQ4'),
              answer: [t('common:agentA41')],
            },
            {
              question: t('common:agentQ5'),
              answer: [t('common:agentA51')],
            },
            {
              question: t('common:agentQ6'),
              answer: [t('common:agentA61')],
            },
          ]}
        />
      </Layout>
    </div>
  );
}
