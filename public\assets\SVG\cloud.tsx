import React from 'react';

const CloudSVG = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="117"
      height="40"
      viewBox="0 0 117 40"
      fill="none"
    >
      <path
        d="M116.01 32.3105C115.458 32.3105 115.019 31.8719 115.019 31.3195C115.019 30.7835 115.458 30.3286 116.01 30.3286C116.545 30.3286 117 30.7672 117 31.3195C117 31.8556 116.545 32.3105 116.01 32.3105ZM116.01 30.5235C115.571 30.5235 115.214 30.8809 115.214 31.3195C115.214 31.7581 115.571 32.1155 116.01 32.1155C116.448 32.1155 116.805 31.7581 116.805 31.3195C116.805 30.8809 116.448 30.5235 116.01 30.5235ZM116.513 31.8394H116.286L116.091 31.4657H115.831V31.8394H115.62V30.751H116.139C116.367 30.751 116.513 30.8972 116.513 31.1084C116.513 31.2708 116.415 31.3845 116.286 31.4333L116.513 31.8394ZM116.123 31.2708C116.204 31.2708 116.286 31.2221 116.286 31.1084C116.286 30.9784 116.221 30.9459 116.123 30.9459H115.799V31.2708H116.123Z"
        fill="white"
      />
      <path
        d="M12.8418 30.2314H15.3743V37.1354H19.7738V39.3447H12.8418V30.2314Z"
        fill="white"
      />
      <path
        d="M22.3875 34.8126V34.7801C22.3875 32.1647 24.4979 30.0366 27.3064 30.0366C30.1149 30.0366 32.1929 32.1322 32.1929 34.7476V34.7801C32.1929 37.3955 30.0824 39.5235 27.2739 39.5235C24.4654 39.5235 22.3875 37.428 22.3875 34.8126ZM29.6279 34.8126V34.7801C29.6279 33.4643 28.6863 32.3271 27.2902 32.3271C25.9103 32.3271 24.9849 33.448 24.9849 34.7638V34.7963C24.9849 36.1121 25.9265 37.2493 27.3064 37.2493C28.7025 37.2493 29.6279 36.1284 29.6279 34.8126Z"
        fill="white"
      />
      <path
        d="M35.2935 35.3485V30.2314H37.8585V35.2998C37.8585 36.6156 38.5241 37.2329 39.5306 37.2329C40.5371 37.2329 41.2027 36.6319 41.2027 35.3648V30.2314H43.7677V35.2836C43.7677 38.2239 42.0956 39.5072 39.4981 39.5072C36.9169 39.5072 35.2935 38.2076 35.2935 35.3485Z"
        fill="white"
      />
      <path
        d="M47.6313 30.2314H51.1379C54.3848 30.2314 56.2841 32.0996 56.2841 34.7312V34.7637C56.2841 37.3954 54.3685 39.3447 51.0892 39.3447H47.6313V30.2314ZM51.1866 37.103C52.6964 37.103 53.7029 36.2745 53.7029 34.7962V34.7637C53.7029 33.3017 52.6964 32.457 51.1866 32.457H50.1639V37.0867H51.1866V37.103Z"
        fill="white"
      />
      <path
        d="M59.9531 30.2314H67.2423V32.4407H62.4694V34.0002H66.7877V36.0958H62.4694V39.3447H59.9531V30.2314Z"
        fill="white"
      />
      <path
        d="M70.7488 30.2314H73.2651V37.1354H77.6808V39.3447H70.7488V30.2314Z"
        fill="white"
      />
      <path
        d="M84.2719 30.1665H86.707L90.587 39.3448H87.8759L87.2103 37.7203H83.7037L83.0544 39.3448H80.4082L84.2719 30.1665ZM86.496 35.7547L85.4895 33.1718L84.4667 35.7547H86.496Z"
        fill="white"
      />
      <path
        d="M93.8337 30.2314H98.1358C99.5319 30.2314 100.49 30.5888 101.107 31.2224C101.642 31.7422 101.918 32.4407 101.918 33.3504V33.3829C101.918 34.78 101.172 35.7059 100.051 36.177L102.227 39.361H99.3046L97.4702 36.5994H96.3663V39.361H93.8337V30.2314ZM98.0221 34.6013C98.8826 34.6013 99.3696 34.1789 99.3696 33.5291V33.4966C99.3696 32.7819 98.8501 32.4245 98.0059 32.4245H96.35V34.6013H98.0221Z"
        fill="white"
      />
      <path
        d="M105.555 30.2314H112.877V32.3757H108.055V33.7565H112.422V35.7546H108.055V37.2004H112.941V39.3447H105.555V30.2314Z"
        fill="white"
      />
      <path
        d="M7.32211 35.8846C6.96495 36.6806 6.21819 37.2491 5.24414 37.2491C3.86424 37.2491 2.92266 36.0958 2.92266 34.7962V34.7637C2.92266 33.4479 3.848 32.327 5.2279 32.327C6.26689 32.327 7.06236 32.9605 7.38704 33.8378H10.0494C9.62735 31.661 7.71173 30.0527 5.24414 30.0527C2.43563 30.0527 0.325195 32.1808 0.325195 34.7962V34.8287C0.325195 37.4441 2.40316 39.5396 5.21167 39.5396C7.61432 39.5396 9.49748 37.9802 9.9845 35.9008L7.32211 35.8846Z"
        fill="white"
      />
      <path
        d="M109.889 15.6923L102.925 11.6961L101.724 11.1763L73.2327 11.3712V25.8452H109.889V15.6923Z"
        fill="white"
      />
      <path
        d="M97.2104 24.5132C97.5513 23.3436 97.4214 22.2714 96.8532 21.4754C96.3338 20.7444 95.4571 20.322 94.4019 20.2733L74.4177 20.0134C74.2878 20.0134 74.1742 19.9484 74.1092 19.8509C74.0443 19.7535 74.0281 19.6235 74.0605 19.4936C74.1255 19.2986 74.3203 19.1524 74.5313 19.1362L94.6941 18.8763C97.0805 18.7626 99.678 16.8294 100.587 14.4577L101.74 11.4524C101.788 11.3225 101.805 11.1925 101.772 11.0626C100.473 5.18197 95.2298 0.795898 88.9635 0.795898C83.1841 0.795898 78.2814 4.53218 76.5281 9.71425C75.3917 8.86952 73.9469 8.41467 72.3884 8.56087C69.6124 8.83703 67.3883 11.0626 67.1123 13.8404C67.0474 14.5552 67.0961 15.2537 67.2584 15.9035C62.7291 16.0334 59.1089 19.7372 59.1089 24.302C59.1089 24.7081 59.1414 25.1142 59.1901 25.5204C59.2225 25.7153 59.3849 25.8615 59.5797 25.8615H96.4636C96.6747 25.8615 96.8695 25.7153 96.9344 25.5041L97.2104 24.5132Z"
        fill="#F5821F"
      />
      <path
        d="M103.574 11.6636C103.396 11.6636 103.201 11.6636 103.022 11.6798C102.892 11.6798 102.779 11.7773 102.73 11.9072L101.951 14.6201C101.61 15.7897 101.74 16.8619 102.308 17.6579C102.827 18.3889 103.704 18.8112 104.759 18.86L109.013 19.1199C109.143 19.1199 109.256 19.1849 109.321 19.2823C109.386 19.3798 109.402 19.526 109.37 19.6397C109.305 19.8347 109.11 19.9809 108.899 19.9971L104.467 20.257C102.064 20.3707 99.4832 22.3039 98.5741 24.6756L98.2494 25.5041C98.1845 25.6665 98.2981 25.829 98.4767 25.829H113.704C113.883 25.829 114.045 25.7152 114.094 25.5366C114.354 24.5944 114.5 23.6034 114.5 22.58C114.5 16.5695 109.597 11.6636 103.574 11.6636Z"
        fill="#FBAE40"
      />
    </svg>
  );
};

export default CloudSVG;
