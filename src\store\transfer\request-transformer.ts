/**
 * create transfer request schema
 */
import { z } from 'zod';

export const createTransferApiRequestSchema = z.object({
  userTo: z.string().or(z.number()),
  currencyFrom: z.string().or(z.number()),
  currencyTo: z.string().or(z.number()),
  type: z.enum(['transfer', 'exchange', 'payment_link']).or(z.string()),
  amount: z.number().or(z.string()).or(z.undefined()),
  notes: z.string().nullable().optional(),
  paymentLink: z.number().nullable().optional(),
  gif: z.string().optional(),
});

export const createTransferBackendRequestSchema = createTransferApiRequestSchema.transform((data) => ({
  data: {
    to_user:
        typeof data.userTo === 'string' ? data.userTo.trim() : data.userTo,
    currency_from: data.currencyFrom,
    currency_to: data.currencyTo,
    type: data.type,
    amount: `${data.amount}`,
    notes: data?.notes?.trim(),
    payment_link_id: data?.paymentLink,
    gif: data.gif,
  },
}));
