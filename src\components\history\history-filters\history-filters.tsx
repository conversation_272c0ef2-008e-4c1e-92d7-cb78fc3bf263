import { Icon } from '@/components/common/icon';
import { CurrenciesApiResponse, getCurrenciesQuery } from '@/store/currencies';
import {
  PaymentMethodsApiResponse,
  getPaymentMethodsQuery,
} from '@/store/payment-methods';
import { getTranslatedTextValue } from '@/utils';
import {
  ActionIcon,
  Box,
  Button,
  Group,
  Image,
  Indicator,
  Loader,
  Modal,
  ScrollArea,
  Select,
  Stack,
  Text,
  Tooltip,
} from '@mantine/core';
import { DatePicker } from '@mantine/dates';
import { useDisclosure } from '@mantine/hooks';
import { useQuery } from '@tanstack/react-query';
import useTranslation from 'next-translate/useTranslation';
import { useRouter } from 'next/router';
import React, { forwardRef, useEffect } from 'react';

interface ItemProps extends React.ComponentPropsWithoutRef<'div'> {
  image: string;
  label: string;
}

const SelectItem = forwardRef<HTMLDivElement, ItemProps>(
  ({ image, label, ...others }: ItemProps, ref) => (
    <div ref={ref} {...others}>
      <Group noWrap>
        <Image width={20} height={20} radius={20} src={image} />
        <Box maw={300} sx={{ overflow: 'hidden' }}>
          <Text size="sm">{label}</Text>
        </Box>
      </Group>
    </div>
  ),
);

interface HistoryFiltersProps {
  currency: string | null;
  setCurrency: (v: string | null) => void;
  paymentMethod: string | null;
  setPaymentMethod: (v: string | null) => void;
  status: string | null;
  setStatus: (v: string | null) => void;
  createdFrom: string | null;
  setCreatedFrom: (v: string | null) => void;
  createdTo: string | null;
  setCreatedTo: (v: string | null) => void;
  schedule: string | null;
  setSchedule: (v: string | null) => void;
  filteredDataType: 'transfers' | 'payments' | 'all';
}
function HistoryFilters(props: HistoryFiltersProps) {
  const { t } = useTranslation();
  const { locale } = useRouter();
  const {
    setCurrency,
    setPaymentMethod,
    setStatus,
    createdFrom,
    createdTo,
    currency,
    paymentMethod,
    setCreatedFrom,
    setCreatedTo,
    status,
    schedule,
    setSchedule,
    filteredDataType,
  } = props;
  const [opened, { open, close }] = useDisclosure(false);
  // fetch currencies
  const { data: currenciesData, isLoading: isCurrenciesLoading } = useQuery(
    getCurrenciesQuery({
      pagination: { limit: 100 },
    }),
  );
  // fetch payment methods
  const { data: paymentMethodsData, isLoading: isPaymentMethodsLoading } = useQuery(
    getPaymentMethodsQuery({
      pagination: { limit: 100 },
    }),
  );
  // clear all filters trigger
  const clearAllFilters = () => {
    setCurrency(null);
    setPaymentMethod(null);
    setStatus(null);
    setCreatedFrom(null);
    setCreatedTo(null);
    setSchedule(null);
  };
  // remove payment method filter if filtered data is transfers.
  useEffect(() => {
    if (filteredDataType === 'transfers') setPaymentMethod(null);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filteredDataType]);

  const showIndicator = currency || paymentMethod || status || createdFrom || createdTo;
  // handle date filter for schedule options
  const calculateDateRange = (value: string | null) => {
    const currentDate = new Date();

    if (value === 'lastDay') {
      const startDate = new Date(currentDate.getTime() - 24 * 60 * 60 * 1000);
      setCreatedFrom(startDate.toISOString());
    }

    if (value === 'lastWeek') {
      const startDate = new Date(
        currentDate.getTime() - 7 * 24 * 60 * 60 * 1000,
      );
      setCreatedFrom(startDate.toISOString());
    }

    if (value === 'lastMonth') {
      const startDate = new Date(
        currentDate.getFullYear(),
        currentDate.getMonth() - 1,
        currentDate.getDate(),
      );
      setCreatedFrom(startDate.toISOString());
    }
    setCreatedTo(currentDate.toISOString());
  };
  // handle select schedule change
  const handleScheduleChange = (value: string | null) => {
    setSchedule(value);
    if (value === null) {
      setCreatedFrom(null);
      setCreatedTo(null);
    } else {
      calculateDateRange(value);
    }
  };

  return (
    <>
      <Group position="apart">
        <Tooltip label={t('common:filters')}>
          <Indicator
            zIndex={10}
            disabled={!showIndicator}
            inline
            color="red"
            size={12}
          >
            <ActionIcon variant="outline" color="primary" onClick={open}>
              <Icon icon="filter" color="" size="18" />
            </ActionIcon>
          </Indicator>
        </Tooltip>
      </Group>
      <Modal
        radius="lg"
        opened={opened}
        onClose={close}
        title={t('common:filters')}
        centered
        scrollAreaComponent={ScrollArea.Autosize}
      >
        <Stack mih={295}>
          <Select
            miw={{ base: '100%', xs: '48%', sm: 250 }}
            radius="lg"
            label={t('common:currency')}
            placeholder={t('common:currency')}
            onChange={setCurrency}
            value={currency}
            clearable
            searchable
            rightSection={isCurrenciesLoading && <Loader size="xs" />}
            data={
              currenciesData?.data
                ? currenciesData?.data?.map(
                  (currencyItem: CurrenciesApiResponse['data'][0]) => ({
                    value: currencyItem?.id,
                    label: getTranslatedTextValue(locale, currencyItem.label, currencyItem?.labelAr),
                    image: currencyItem?.image,
                  }),
                )
                : []
            }
            itemComponent={SelectItem}
          />
          {filteredDataType !== 'transfers' && (
            <Select
              miw={{ base: '100%', xs: '48%', sm: 250 }}
              radius="lg"
              label={t('common:paymentMethod')}
              placeholder={t('common:paymentMethod')}
              onChange={setPaymentMethod}
              value={paymentMethod}
              clearable
              searchable
              rightSection={isPaymentMethodsLoading && <Loader size="xs" />}
              data={
                paymentMethodsData?.data
                  ? paymentMethodsData?.data?.map(
                    (
                      paymentMethodItem: PaymentMethodsApiResponse['data'][0],
                    ) => ({
                      value: paymentMethodItem?.id,
                      label: getTranslatedTextValue(locale, paymentMethodItem?.label, paymentMethodItem?.labelAr),
                      image: paymentMethodItem?.image,
                    }),
                  )
                  : []
              }
              itemComponent={SelectItem}
            />
          )}

          <Select
            miw={{ base: '85%', sm: 180 }}
            radius="lg"
            label={t('common:status')}
            placeholder={t('common:status')}
            onChange={setStatus}
            value={status}
            clearable
            data={[
              { value: 'pending', label: t('common:pending') },
              { value: 'processing', label: t('common:processing') },
              { value: 'approved', label: t('common:approved') },
              { value: 'rejected', label: t('common:rejected') },
            ]}
          />

          <Select
            miw={{ base: '85%', sm: 180 }}
            radius="lg"
            label={t('common:date')}
            placeholder={t('common:date')}
            onChange={handleScheduleChange}
            value={schedule}
            clearable
            data={[
              { value: 'lastDay', label: t('common:last24Hours') },
              { value: 'lastWeek', label: t('common:lastWeek') },
              { value: 'lastMonth', label: t('common:lastMonth') },
              { value: 'custom', label: t('common:custom') },
            ]}
          />
          {schedule === 'custom' && (
            <DatePicker
              mx="auto"
              type="range"
              onChange={(v) => {
                setCreatedFrom(v && v[0] ? `${v[0].toISOString()}` : null);
                setCreatedTo(v && v[1] ? `${v[1].toISOString()}` : null);
              }}
              value={[
                createdFrom ? new Date(createdFrom) : null,
                createdTo ? new Date(createdTo) : null,
              ]}
            />
          )}
          <Button
            radius="lg"
            color="red"
            variant="outline"
            mt="lg"
            onClick={clearAllFilters}
          >
            {t('common:clearAll')}
          </Button>
        </Stack>
      </Modal>
    </>
  );
}

export default HistoryFilters;
