import { useRouter } from 'next/router';
import {
  Box, Button, Card, Group, SimpleGrid, Text, Title,
} from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';
import { CardVisual } from './card-visual/card-visual';
import { CardType } from './type';
import { cardTypes } from './mock-data';

const cardData: CardType[] = cardTypes;

export function FlexiCardOffers() {
  const { t } = useTranslation();
  const router = useRouter();
  return (
    <SimpleGrid
      cols={4}
      spacing="xl"
      breakpoints={[
        { maxWidth: 'md', cols: 2 },
        { maxWidth: 'sm', cols: 1 },
      ]}
    >
      {cardData.map((card) => (
        <Card key={card.id} shadow="sm" padding="lg" radius="md" withBorder>
          <Title order={4}>{card.name}</Title>
          <Card.Section style={{ padding: '20px' }}>
            <CardVisual />
          </Card.Section>

          <Box mt="md" style={{ minHeight: 150 }}>
            <Group position="apart">
              <Text size="sm" color="dimmed">{t('common:activationFee')}</Text>
              <Text size="sm" weight={500}>
                {card.activationFee}
                {' '}
                {card.activationFeeCurrency}
              </Text>
            </Group>
            <Group position="apart">
              <Text size="sm" color="dimmed">{t('common:initialTopUp')}</Text>
              <Text size="sm" weight={500}>
                {card.initialTopUp}
                {' '}
                {card.initialTopUpCurrency}
              </Text>
            </Group>
            <Group position="apart">
              <Text size="sm" color="dimmed">{t('common:topUpFee')}</Text>
              <Text size="sm" weight={500}>{card.topUpFee}</Text>
            </Group>
            <Group position="apart">
              <Text size="sm" color="dimmed">{t('common:monthlyFee')}</Text>
              <Text size="sm" weight={500}>{card.monthlyFee}</Text>
            </Group>
            <Group position="apart">
              <Text size="sm" color="dimmed">{t('common:generalTxnFee')}</Text>
              <Text size="sm" weight={500}>{card.generalTxnFee}</Text>
            </Group>
            <Group position="apart">
              <Text size="sm" color="dimmed">{t('common:foreignTxnFee')}</Text>
              <Text size="sm" weight={500}>{card.foreignTxnFee}</Text>
            </Group>
          </Box>

          {card.available ? (
            <Button
              variant="light"
              color="blue"
              fullWidth
              mt="md"
              radius="md"
              onClick={() => router.push(`/flexi-card/apply-form?cardId=${card.id}`)}
            >
              {t('common:apply')}
            </Button>
          ) : (
            <Text align="center" mt="md" color="dimmed">
              {t('common:comingSoon')}
            </Text>
          )}
        </Card>
      ))}
    </SimpleGrid>
  );
}
