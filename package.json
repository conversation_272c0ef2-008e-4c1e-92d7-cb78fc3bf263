{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky install", "type-check": "tsc --noEmit --strict", "setup:flexi-mock": "node scripts/setup-flexi-mock.js"}, "dependencies": {"@emotion/react": "^11.11.0", "@emotion/server": "^11.11.0", "@mantine/carousel": "^6.0.11", "@mantine/core": "^6.0.10", "@mantine/dates": "^6.0.17", "@mantine/dropzone": "^6.0.11", "@mantine/form": "^6.0.11", "@mantine/hooks": "^6.0.10", "@mantine/next": "^6.0.11", "@mantine/notifications": "^6.0.10", "@mantine/nprogress": "^6.0.17", "@mantine/prism": "^6.0.22", "@mantine/spotlight": "6.0.11", "@notionhq/client": "^2.2.15", "@tabler/icons": "^2.20.0", "@tabler/icons-react": "^2.20.0", "@tanstack/react-query": "^4.29.5", "@types/showdown": "^2.0.6", "@yudiel/react-qr-scanner": "^2.3.1", "@zxing/browser": "^0.1.5", "@zxing/library": "^0.21.3", "axios": "^1.4.0", "cookies-next": "^2.1.1", "countries-list": "^2.6.1", "currency.js": "^2.0.4", "dayjs": "^1.11.7", "embla-carousel-autoplay": "7.1.0", "embla-carousel-react": "7.1.0", "eslint": "8.40.0", "eslint-config-next": "13.4.2", "gif-picker-react": "^1.3.0", "jsqr": "^1.4.0", "jwt-decode": "^3.1.2", "keycloak-js": "^22.0.4", "mantine-flagpack": "2.0.0", "next": "13.4.2", "next-auth": "^4.22.1", "next-pwa": "^5.6.0", "next-translate": "^2.5.3", "nextjs-google-analytics": "^2.3.3", "otplib": "^12.0.1", "papaparse": "^5.4.1", "qrcode": "^1.5.3", "ramda": "^0.29.0", "react": "18.2.0", "react-countdown": "^2.3.5", "react-csv": "^2.2.2", "react-dom": "18.2.0", "react-google-recaptcha": "^3.1.0", "react-player": "^2.13.0", "react-qr-code": "^2.0.12", "react-qrcode-logo": "^3.0.0", "react-showdown": "^2.3.1", "sass": "^1.62.1", "showdown": "^2.1.0", "stylis": "^4.2.0", "stylis-plugin-rtl": "^2.1.1", "typescript": "5.0.4", "zod": "^3.21.4", "zustand": "^4.3.8"}, "devDependencies": {"@types/axios": "^0.14.0", "@types/keycloak-js": "^3.4.1", "@types/next-pwa": "^5.6.2", "@types/node": "20.1.3", "@types/papaparse": "^5.3.7", "@types/qrcode": "^1.5.1", "@types/ramda": "^0.29.1", "@types/react": "18.2.6", "@types/react-csv": "^1.1.10", "@types/react-dom": "18.2.4", "@types/react-google-recaptcha": "^2.1.5", "@typescript-eslint/eslint-plugin": "^5.59.5", "@typescript-eslint/parser": "^5.59.5", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-sonarjs": "^0.19.0", "husky": "^8.0.3", "next-translate-plugin": "^2.5.3", "prettier": "^2.8.8"}, "packageManager": "yarn@1.22.21+sha1.1959a18351b811cdeedbd484a8f86c3cc3bbaf72"}