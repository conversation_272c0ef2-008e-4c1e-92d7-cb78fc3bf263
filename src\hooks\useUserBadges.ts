import { apiEndpoints } from '@/data';
import { BackendClient } from '@/lib';
import createApiError from '@/utils/api-utils/create-api-error';
import { getJwt } from '@/utils/api-utils/jwt';
import { NextApiRequest, NextApiResponse } from 'next';

export const useUserBadges = async (
  req: NextApiRequest,
  res: NextApiResponse,
) => {
  const { token } = await getJwt(req);
  try {
    const userData = await BackendClient(req).get(
      `${apiEndpoints.users()}/me`,
      {
        headers: {
          authorization: token,
        },
        params: {
          'populate[badges][populate]': '*',
        },
      },
    );
    const userBadges = userData.data?.badges?.map(
      (badge: { id: number }) => badge?.id,
    );
    return {
      params: {
        'filters[$or][0][badges][id][$null]': true,
        //   "filters[$or][0][badges][$eq]": null,
        ...Object.assign(
          {},
          ...userBadges.map((badge: number, index: number) => ({
            [`filters[$or][${index + 1}][badges][id][$in][${index}]`]: badge,
          })),
        ),
      },
    };
  } catch (e) {
    const error = createApiError({ error: e });
    return res.status(error.code).json(error);
  }
};
