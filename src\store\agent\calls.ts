import { apiEndpoints } from '@/data';
import { ApiClient } from '@/lib';
import { handleApiError } from '@/utils/api-utils/handle-backend-error';
import {
  CreateAgentPaymentApiRequest,
  CreateAgentRequestApiRequest,
} from './types';

enum queryKeys {
  create = 'create',
  withdrawPayment = 'withdrawPayment',
  createAgent = 'create-agent',
}

/** ****************************************************** */
/**
 * @description function calls handler in "/payments" api route with "post" method to create payment.
 * If user enabled 2FA will get "qrCode" param if type of the payment is "withdraw" ,
 *  and will check code validity before create the payment.
 * @param "body,qrCode"
 * @returns created payment details or error if failed
 */
const createAgentPaymentRequest = ({
  body,
  qrCode,
  chaKey,
}: {
  body: CreateAgentPaymentApiRequest;
  qrCode?: string;
  chaKey?: string;
}) => ApiClient.post(apiEndpoints.agent, body, {
  params: {
    qrCode,
    chaKey,
  },
})
  .then((res) => res.data)
  .catch((err) => {
    handleApiError(err);
    throw err;
  });

export const createAgentPaymentMutation = () => ({
  mutationKey: [queryKeys.create],
  mutationFn: ({
    body,
    qrCode,
    chaKey,
  }: {
    body: CreateAgentPaymentApiRequest;
    qrCode?: string;
    chaKey?: string;
  }) => createAgentPaymentRequest({ body, qrCode, chaKey }),
});

/** ****************************************************** */
/**
 * @description function call handler in "/agent" api route with "get" method to fetch user payments.
 * @param props
 * @returns list of payment or error if failed
 */

const getAgentPaymentRequest = ({
  uid,
  chaKey,
}: {
  uid: string | undefined;
  chaKey?: string;
}) => ApiClient.post(
  apiEndpoints.agent,
  {},
  {
    params: {
      id: uid,
      chaKey,
    },
  },
)
  .then((res) => res.data)
  .catch((err) => {
    handleApiError(err);
    throw err;
  });
export const getAgentPaymentMutation = () => ({
  mutationKey: [queryKeys.withdrawPayment],
  // if founded id call get payment function
  mutationFn: ({ uid, chaKey }: { uid: string | undefined; chaKey?: string }) => getAgentPaymentRequest({ uid, chaKey }),
});

/**
 * @description function call handler in "/agent" api route to create agent request.
 * "token" param using to captcha validation.
 * @param body
 * @returns success of error if failed
 */
const createAgentRequestRequest = (body: CreateAgentRequestApiRequest) => ApiClient.post(apiEndpoints.agentRequest, body)
  .then((res) => res.data)
  .catch((err) => {
    handleApiError(err);
    throw err;
  });

export const createAgentRequestMutation = () => ({
  mutationKey: [queryKeys.createAgent],
  mutationFn: createAgentRequestRequest,
});
