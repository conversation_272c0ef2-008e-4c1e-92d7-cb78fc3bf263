import React from 'react';

import {
  Group, MantineTheme, Text, UnstyledButton,
} from '@mantine/core';

import Link from 'next/link';
import { ROUTES } from '@/data';
import useTranslation from 'next-translate/useTranslation';
import { useRouter } from 'next/router';
import { Icon } from '../common/icon';

function ActionsCard() {
  const { query } = useRouter();
  const { t } = useTranslation();
  const buttonStyle = (theme:MantineTheme) => ({
    borderRadius: 25,
    ':hover': {
      backgroundColor:
        theme.colorScheme === 'light'
          ? theme.colors.gray[1]
          : theme.colors.dark[5],
    },
  });
  const textStyle = (theme:MantineTheme) => ({
    [theme.fn.smallerThan('xs')]: {
      display: 'none',
    },
  });
  return (
    <Group spacing="xs">
      <Link href={`${ROUTES.withdraw.path}?currency=${query?.slug}`}>
        <UnstyledButton
          p="xs"
          sx={buttonStyle}
          ta="center"
        >
          <Icon icon="circle-minus" size={30} color="gray" />
          <Text
            sx={textStyle}
            weight={500}
            mx="auto"
            size="sm"
            maw={65}
            tt="capitalize"
            color="dimmed"
          >
            {t('common:withdraw')}
          </Text>
        </UnstyledButton>
      </Link>
      <Link href={`${ROUTES.transfer.path}?currency=${query?.slug}`}>
        <UnstyledButton
          p="xs"
          sx={buttonStyle}
          ta="center"
        >
          <Icon icon="circle-arrow-up-right" size={30} color="gray" />
          <Text
            sx={textStyle}
            weight={500}
            mx="auto"
            maw={70}
            tt="capitalize"
            color="dimmed"
          >
            {t('common:transfer')}
            {' '}
          </Text>
        </UnstyledButton>
      </Link>
      <Link href={`${ROUTES.exchange.path}?currency=${query?.slug}`}>
        <UnstyledButton
          p="xs"
          sx={buttonStyle}
          ta="center"
        >
          <Icon icon="refresh" size={30} color="gray" />
          <Text
            sx={textStyle}
            weight={500}
            mx="auto"
            maw={70}
            tt="capitalize"
            color="dimmed"
          >
            {t('common:exchangeWord')}
          </Text>
        </UnstyledButton>
      </Link>
      <Link href={`${ROUTES.deposit.path}?currency=${query?.slug}`}>
        <UnstyledButton
          p="xs"
          sx={buttonStyle}
          ta="center"
        >
          <Icon icon="circle-plus" size={30} color="gray" />
          <Text
            sx={textStyle}
            weight={500}
            mx="auto"
            size="sm"
            maw={65}
            tt="capitalize"
            color="dimmed"
          >
            {t('common:deposit')}
          </Text>
        </UnstyledButton>
      </Link>
    </Group>
  );
}

export default ActionsCard;
