/**
 * This component renders merchants page.
 */
import MetaTags from '@/components/common/meta-tags';

import { Layout } from '@/components/layout/layout';

import { ImagesSlider } from '@/components/merchants/images-slider';
import { MerchantsList } from '@/components/merchants/merchants-list';
import { Box, Text } from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';
import { getMerchants } from './api/merchants';

import { MerchantsApiResponse } from '@/store/merchant/types';

import { BannersSlider } from '@/components/merchants/banner-silder';
import { assetBaseUrl } from '@/data';

interface Props {
  data: MerchantsApiResponse;
}
export default function Merchants(props: Props) {
  const { data } = props;
  const { t } = useTranslation();

  // const tagsList = [
  //   { label: t('common:all'), value: 'all' },
  // ];

  // const [filterTag, setFilterTag] = useState('all');

  const merchantList = data?.data;
  //  filterTag !== 'all'
  //   ? data?.data?.filter((merchant) => merchant.tag.name === filterTag)
  //   :

  return (
    <div>
      <MetaTags title={t('common:merchants')} />
      <Layout>
        <Text
          mt="lg"
          mb="md"
          color="dimmed"
          tt="capitalize"
          weight={700}
          size={25}
        >
          {t('common:shopWithKazaWallet')}
        </Text>
        <Text>
          {t('common:getBestDealBuyingYourFavoriteThingsWithKazaWallet')}
        </Text>
        <Box mt="50px">
          <ImagesSlider merchants={data?.data} />
        </Box>
        {/* <Box mt={50}>
          <SegmentedControl
            value={filterTag}
            onChange={setFilterTag}
            mx="auto"
            maw={200}
            data={tagsList}
            fullWidth
          />
        </Box> */}
        <Box mt={50} maw={{ md: 1000, sm: 660 }} mx="auto">
          <MerchantsList data={merchantList} />
        </Box>
        <Box mt="50px">
          <BannersSlider banners={[`${assetBaseUrl}/assets/merchants/banner.webp`]} />
        </Box>
      </Layout>
    </div>
  );
}

export async function getStaticProps(context: { locale: string }) {
  const data = await getMerchants(context?.locale);

  return {
    props: {
      data,
    },
    revalidate: 6 * 60 * 60, // for 6 hours
  };
}
