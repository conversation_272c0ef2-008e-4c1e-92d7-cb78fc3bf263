/**
 * This component renders a Rates page.
 *
 * @description
 * This page used to display list of rates.
 * Call get rates api and display the data.
 * Display list of payment methods with currencies for each one.
 */
import MetaTags from '@/components/common/meta-tags';
import { Layout } from '@/components/layout/layout';

import React from 'react';
import useTranslation from 'next-translate/useTranslation';

import { Container, Group, Text } from '@mantine/core';

import dayjs from 'dayjs';
import { PageTitle } from '@/components/common/page-title';
import { ExchangeRatesList } from '@/components/rates/exhange-rates';

import { getRatesQuery } from '@/store/rate';
import { useQuery } from '@tanstack/react-query';
import { DATE_FORMAT } from '@/data';

// render list of payment methods with currencies for each one

// function RenderPaymentMethods({
//   ratesData,
// }: {
//   ratesData: RatesApiResponse["rates"];
// }) {
//   const { t } = useTranslation();
//   const { data, isLoading } = useQuery(
//     getPaymentMethodsQuery({
//       populate: {
//         depositCurrencies: true,
//       },
//       pagination: {
//         limit: 1000,
//       },
//     })
//   );
//   if (data?.data?.length === 0 && !isLoading) {
//     return <EmptyData message="" />;
//   }
//   return isLoading ? (
//     <Stack align="center">
//       {" "}
//       <Loader mx="auto" my={25} />
//     </Stack>
//   ) : (
//     <div>
//       {data?.data?.map(
//         (i: PaymentMethodsApiResponse["data"][0]) =>
//           i.depositCurrencies.length > 0 && (
//             <div key={i?.id}>
//               <Text
//                 mt="xl"
//                 mb="md"
//                 size="lg"
//                 ta="center"
//                 tt="capitalize"
//                 weight={500}
//               >
//                 <TranslatedTextValue keyEn={i?.label} keyAr={i?.labelAr} />;
//               </Text>
//               <Divider my="lg" />
//               <RatesList
//                 textColor=""
//                 data={i?.depositCurrencies}
//                 rates={ratesData}
//                 isLoading={isLoading || ratesData?.isLoading}
//               />
//             </div>
//           )
//       )}
//     </div>
//   );
// }

function Rates() {
  const { t } = useTranslation();
  const { data, isLoading } = useQuery(getRatesQuery());

  return (
    <div>
      <MetaTags title={t('common:rates')} />
      <Layout>
        <Container px={{ base: 20, md: 0 }} maw={800}>
          <PageTitle title="rates" />
          {data?.updatedAt && (
            <Group spacing={3} position="right">
              <Text>
                {t('common:lastUpdate')}
                :
              </Text>
              <Text>{dayjs(data?.updatedAt).format(DATE_FORMAT)}</Text>
            </Group>
          )}
          {/* <Text my="md" size="lg" ta="center" weight={500} color="dimmed">
            {t('common:paymentMethods')}
          </Text> */}
          {/* <RenderPaymentMethods ratesData={ratesData?.data} /> */}
          <ExchangeRatesList
            color=""
            forLandingPage={false}
            rates={data}
            isLoading={isLoading}
          />
        </Container>
      </Layout>
    </div>
  );
}

export default Rates;

export async function getServerSideProps() {
  return { props: {} };
}
