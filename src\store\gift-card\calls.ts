import { apiEndpoints } from '@/data';
import { ApiClient } from '@/lib';
import { handleApiError } from '@/utils/api-utils/handle-backend-error';
import {
  CreateGiftCardApiRequest,
  getGiftCardsQueryProps,
  RedeemGiftCardApiRequest,
} from './types';
import { QueryFunctionContext } from '@tanstack/react-query';

enum queryKeys {
  create = 'create',
  gifts = 'gifts',
  redeem = 'redeem',
  gift = 'gift',
}

/** ****************************************************** */
/**
 * @description function calls handler in "/gift-cards" api route with "post" method to create gift card.
 * @returns created gift card details or error if failed
 */
const createGiftCardRequest = ({
  body,
  chaKey,
}: {
  body: CreateGiftCardApiRequest;
  chaKey?: string;
}) => ApiClient.post(apiEndpoints.gifts(), body, {
  params: {
    chaKey,
  },
})
  .then((res) => res.data)
  .catch((err) => {
    handleApiError(err);
    throw err;
  });

export const createGiftCardMutation = () => ({
  mutationKey: [queryKeys.create],
  mutationFn: ({
    body,
    chaKey,
  }: {
    body: CreateGiftCardApiRequest;
    chaKey?: string;
  }) => createGiftCardRequest({ body, chaKey }),
});

/** ****************************************************** */
/**
 * @description function call handler in "/gift-cards" api route with "get" method to fetch user gift cards.
 * @param props
 * @returns list of gift cards or error if failed
 */
const getGiftCardsRequest = (props: getGiftCardsQueryProps) => {
  const { pagination, params, filters } = props;
  return ApiClient.get(apiEndpoints.gifts(), {
    params: {
      page: params ? params.pageParam : pagination?.page,
      pageSize: pagination?.pageSize,
      start: pagination?.start,
      limit: pagination?.limit,
      code: filters?.code,
    },
  })
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e, true);
      throw e.response?.data;
    });
};
export const getGiftCardsQuery = (props: getGiftCardsQueryProps) => ({
  queryKey: [
    queryKeys.gifts,
    props?.pagination?.page,
    props?.pagination?.pageSize,
    props?.filters,
  ],
  queryFn: (params: QueryFunctionContext) => getGiftCardsRequest({ ...props, params }),
  refetchOnWindowFocus: false,
  // if the last loaded page from pagination pages is less than the pagination page count set next page is last page +1
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  getNextPageParam: (lastPage: any) => lastPage?.pagination?.page < lastPage?.pagination?.pageCount
    && lastPage.pagination.page + 1,
});
/** ****************************************************** */
/**
 * @description function calls handler in "/gift-cards" api route with "post" method to redeem gift card.
 * @returns redeemed gift card details or error if failed
 */
const redeemGiftCardRequest = ({
  body,
  chaKey,
}: {
  body: RedeemGiftCardApiRequest;
  chaKey?: string;
}) => ApiClient.post(apiEndpoints.giftCardRedeem, body, {
  params: {
    chaKey,
  },
})
  .then((res) => res.data)
  .catch((err) => {
    handleApiError(err);
    throw err;
  });

export const redeemGiftCardMutation = () => ({
  mutationKey: [queryKeys.redeem],
  mutationFn: ({
    body,
    chaKey,
  }: {
    body: RedeemGiftCardApiRequest;
    chaKey?: string;
  }) => redeemGiftCardRequest({ body, chaKey }),
});

/** ****************************************************** */
/**
 * @description function call handler in "/custom/gift-card/get" api route with "get" method to fetch user gift card details.
 * @param props
 * @returns gift card details or error if failed
 */
const getGiftCardRequest = ({
  code,
  chaKey,
}: {
  code: string;
  chaKey?: string;
}) => ApiClient.post(
  apiEndpoints.giftCard,
  { code },
  {
    params: {
      chaKey,
    },
  },
)
  .then((res) => res.data)
  .catch((err) => {
    handleApiError(err, false, true);
    throw err;
  });

export const getGiftCardMutation = () => ({
  mutationKey: [queryKeys.gift],
  mutationFn: ({ code, chaKey }: { code: string; chaKey?: string }) => getGiftCardRequest({ code, chaKey }),
});
