/**
 * create gift card request schema
 */
import { Translate } from 'next-translate';
import { z } from 'zod';

export const createGiftCardApiRequestSchema = z.object({
  amount: z.number().or(z.string()),
  currencyCode: z.string(),
});

export const createGiftCardBackendRequestSchema = createGiftCardApiRequestSchema.transform((data) => ({
  data: {
    amount: `${data.amount}`,
    giftCardCurrency: data.currencyCode,
  },
}));

export const redeemGiftCardFormRequestSchema = (t: Translate) => z.object({
  code: z
    .string()
    .length(16, { message: t('errors:coderError') })
    .regex(/^(?=.*[A-Z])(?=.*\d)[A-Z0-9]+$/, {
      message: t('errors:coderError'),
    }),
});

export const redeemGiftCardApiRequestSchema = z.object({
  code: z
    .string()
    .length(16)
    .regex(/^(?=.*[A-Z])(?=.*\d)[A-Z0-9]+$/),
});

export const redeemGiftCardBackendRequestSchema = redeemGiftCardApiRequestSchema.transform((data) => ({
  data: {
    giftCardCode: data.code,
  },
}));
