import { MerchantsApiResponse } from '@/store/merchant/types';
import {
  Badge,
  Box,
  Button,
  Group,
  Image,
  Modal,
  ScrollArea,
  Text,
  useMantineTheme,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import useTranslation from 'next-translate/useTranslation';
import Link from 'next/link';
import React, { ReactNode } from 'react';

interface MerchantDetailsPopupProps {
  merchant: MerchantsApiResponse['data'][0];
  modalAction: ReactNode;
}
function MerchantDetailsPopup({
  merchant,
  modalAction,
}: MerchantDetailsPopupProps) {
  const { t } = useTranslation();
  const theme = useMantineTheme();
  const {
    description, image, name, shortDescription, tag, link,
  } = merchant;
  const [opened, { open, close }] = useDisclosure(false);
  return (
    <>
      <Modal
        radius="lg"
        opened={opened}
        onClose={close}
        centered
        scrollAreaComponent={ScrollArea.Autosize}
      >
        <Image
          mx="auto"
          height="auto"
          radius="lg"
          src={image}
          alt="merchant"
        />
        <Group mt="lg" position="apart">
          <Text fw={500} size="lg" tt="capitalize">
            {name}
          </Text>
          {tag.name && (
            <Badge size="lg" color={tag.color}>
              {tag.name}
            </Badge>
          )}
        </Group>
        <Text
          color={theme.colorScheme === 'dark' ? 'primary.3' : 'primary'}
          mt="lg"
        >
          {shortDescription}
        </Text>

        <Text color="dimmed" size="sm" mt="sm">
          {description}
        </Text>
        <Button
          variant="filled"
          color="primary"
          fullWidth
          mt="md"
          radius="md"
          component={Link}
          href={link}
          target="_blank"
        >
          {t('common:visitStore')}
        </Button>
      </Modal>

      <Group position="center">
        <Box sx={{ cursor: 'pointer' }} w="100%" onClick={open}>
          {modalAction}
        </Box>
      </Group>
    </>
  );
}
export default MerchantDetailsPopup;
