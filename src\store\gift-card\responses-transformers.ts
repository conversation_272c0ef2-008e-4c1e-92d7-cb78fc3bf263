/**
 * payment response schema
 */
import { globalPaginationBackendSchema } from '@/utils';
import { z } from 'zod';
import {
  currencyBackendSchema,
  currencyTransfersPaymentsApiResponseSchema,
} from '../currencies';
import { imageBackedSchema } from '../currencies/responses-transformers';

// gift card backend schema
export const giftCardBackendResponseSchema = z.object({
  id: z.number(),
  attributes: z.object({
    uid: z.string(),
    amount: z.string(),
    code: z.string(),
    expiry_date: z.string().nullable(),
    used: z.boolean(),
    currency: z
      .object({
        data: z.object({
          id: z.number(),
          attributes: currencyBackendSchema,
        }),
      })
      .optional(),
    createdAt: z.string(),
    updatedAt: z.string().nullable(),
  }),
});
// list gift cards
export const giftCardsBackendResponseSchema = z.array(
  giftCardBackendResponseSchema,
);
// gift card front end schema
export const giftCardApiResponseSchema = (
  item: z.infer<typeof giftCardBackendResponseSchema>,
) => ({
  // id: item.id,
  uid: item.attributes.uid,
  amount: +item.attributes.amount,
  code: item.attributes.code,
  expiryDate: item.attributes.expiry_date,
  isUsed: item.attributes.used,
  currency: item.attributes.currency
    ? {
      ...currencyTransfersPaymentsApiResponseSchema(
        item.attributes.currency?.data.attributes,
      ),
    }
    : undefined,
  createdAt: item.attributes.createdAt,
  updatedAt: item.attributes.updatedAt,
});
// list gift cards with pagination
export const giftCardsApiResponseSchema = z
  .object({
    data: giftCardsBackendResponseSchema,
    meta: globalPaginationBackendSchema,
  })
  .transform(({ data, meta }) => ({
    data: data.map(giftCardApiResponseSchema),
    pagination: {
      page: meta.pagination?.page,
      pageSize: meta.pagination?.pageSize,
      pageCount: meta.pagination?.pageCount,
      total: meta.pagination.total,
    },
  }));

export const customGiftBackendResponseSchema = z.object({
  giftCard: z.object({
    amount: z.string(),
    currency: z.object({
      code: z.string(),
      name: z.string(),
      name_ar: z.string(),
      symbol: z.string().nullable().optional(),
      icon: imageBackedSchema.optional(),
    }),
  }),
});
export const customGiftApiResponseSchema = (
  item: z.infer<typeof customGiftBackendResponseSchema>,
) => ({
  amount: +item.giftCard.amount,
  currency: {
    code: item.giftCard.currency.code,
    label: item.giftCard.currency.name,
    labelAr: item.giftCard.currency.name_ar,
    symbol: item.giftCard.currency.symbol,
    image:
      item.giftCard.currency.icon?.formats?.thumbnail.url
      ?? item.giftCard.currency.icon?.url,
  },
});
