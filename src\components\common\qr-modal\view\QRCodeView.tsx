import {
  Box, Text, Stack, Tooltip, ActionIcon, useMantineTheme,
} from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';
import { useRouter } from 'next/router';
import { Icon } from '@/components/common/icon';
import { CustomCopyButton } from '@/components/common/custom-copy-button';
import { QRCodeDisplay } from './QRCodeDisplay';
import { QRSizeConfig } from '../qr-constants';

interface QRCodeViewProps {
  generatedQR: string;
  accountId: string | undefined;
  sizes: QRSizeConfig;
  isMobile: boolean;
  qrWrapperRef: React.RefObject<HTMLDivElement>;
  onShare: () => void;
}

// eslint-disable-next-line complexity
export function QRCodeView({
  generatedQR,
  accountId,
  sizes,
  isMobile,
  qrWrapperRef,
  onShare,
}: QRCodeViewProps) {
  const theme = useMantineTheme();
  const { t } = useTranslation();
  const { locale } = useRouter();

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        backgroundColor: theme.colorScheme === 'dark' ? theme.colors.dark[7] : '#ffffff',
        minHeight: isMobile ? 'auto' : '600px',
      }}
    >
      {/* QR Code Section with Header */}
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          padding: isMobile ? theme.spacing.xs : theme.spacing.sm,
        }}
      >
        {/* Header with Kazawallet branding and action buttons */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            width: '100%',
            maxWidth: '400px',
            marginBottom: theme.spacing.xl,
            paddingTop: isMobile ? 0 : 50,
            paddingInline: isMobile ? 7 : 0,
          }}
        >
          <Box>
            <Text
              ff={locale === 'ar' ? theme.fontFamily : 'BauhausC, sans-serif'}
              size={isMobile ? 24 : 28}
              weight="bold"
              sx={{
                background: 'linear-gradient(90deg, #79CA53)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundSize: '400% 100%',
                width: isMobile ? 130 : 140,
                textAlign: 'center',
              }}
            >
              {t('common:kazawallet')}
            </Text>
          </Box>
          <Tooltip label={t('common:share')}>
            <ActionIcon
              onClick={onShare}
              size="lg"
              color="gray"
              variant="subtle"
              sx={() => ({
                backgroundColor: theme.colorScheme === 'dark'
                  ? theme.fn.rgba(theme.colors.dark[6], 0.8)
                  : theme.fn.rgba(theme.colors.gray[5], 0.8),
                '&:hover': {
                  backgroundColor: theme.colorScheme === 'dark'
                    ? theme.fn.rgba(theme.colors.dark[5], 0.9)
                    : theme.fn.rgba(theme.colors.gray[6], 0.9),
                },
              })}
            >
              <Icon icon="share" size="1.3rem" color="white" />
            </ActionIcon>
          </Tooltip>
        </Box>

        {/* QR Code */}
        <QRCodeDisplay
          value={generatedQR}
          sizes={sizes}
          qrWrapperRef={qrWrapperRef}
        />
      </Box>

      {/* Account ID Section - Enhanced Footer */}
      <Box
        sx={{
          padding: isMobile ? theme.spacing.md : theme.spacing.lg,
          paddingTop: theme.spacing.md,
          paddingBottom: isMobile ? theme.spacing.lg : theme.spacing.xl,
          borderTop: `1px solid ${theme.colorScheme === 'dark' ? theme.colors.dark[5] : theme.colors.gray[2]}`,
          backgroundColor: theme.colorScheme === 'dark' ? theme.colors.dark[6] : theme.colors.gray[0],
        }}
      >
        <Stack spacing="md" align="center">
          <Text
            size="sm"
            color="dimmed"
            weight={500}
            sx={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}
          >
            {t('common:yourWalletAddress')}
          </Text>

          {/* Address Display */}
          <Box
            sx={{
              backgroundColor: theme.colorScheme === 'dark' ? theme.colors.dark[7] : '#ffffff',
              border: `1px solid ${theme.colorScheme === 'dark' ? theme.colors.dark[4] : theme.colors.gray[3]}`,
              borderRadius: 12,
              padding: theme.spacing.md,
              width: '100%',
              maxWidth: 400,
              position: 'relative',
              overflow: 'hidden',
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: theme.spacing.xs }}>
              <Text
                fw={600}
                fz={isMobile ? 12 : 14}
                sx={{
                  color: '#79ca53',
                  wordBreak: 'break-all',
                  fontFamily: 'monospace',
                  lineHeight: 1.4,
                  flex: 1,
                }}
              >
                {accountId}
              </Text>

              <Tooltip label={t('common:copyAddress')} position="top">
                <CustomCopyButton value={accountId || ''} />
              </Tooltip>
            </Box>
          </Box>
        </Stack>
      </Box>
    </Box>
  );
}
