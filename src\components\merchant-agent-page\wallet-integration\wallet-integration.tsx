import {
  Container,
  Text,
  Image,
  SimpleGrid,
  Stack,
  Box,
  BackgroundImage,
  useMantineTheme,
  Group,
} from '@mantine/core';
import styles from '../styles.module.scss';
import Link from 'next/link';
import { assetBaseUrl } from '@/data';

interface IntegrationProps {
  title: string;
  image: string;
  description: string;
  actions: {
    title: string;
    link: string;
    isBlank: boolean;
  }[];
  withImageBackground: boolean;
}
export default function Integration({
  actions,
  description,
  image,
  title,
  withImageBackground,
}: IntegrationProps) {
  const theme = useMantineTheme();

  return (
    <Box className={styles.root}>
      <Container
        py={40}
        px={{
          xs: 20,
          sm: 30,
          lg: 40,
          base: 20,
        }}
        size={1200}
      >
        <SimpleGrid
          mih={500}
          cols={2}
          breakpoints={[
            { maxWidth: 'sm', cols: 2, spacing: 'md' },
            //   { maxWidth: 'sm', cols: 1, spacing: 'sm' },
            { maxWidth: 'xs', cols: 1, spacing: 'sm' },
          ]}
        >
          <Stack justify="center">
            <Text
              color="white"
              ff="BauhausC,__Cairo_0685b6,__Cairo_Fallback_0685b6"
              size={43}
              weight="bold"
            >
              {title}
            </Text>
            <Text lh={1.7} size="lg" mt="lg" color="dimmed">
              {description}
            </Text>
            <Group>
              {actions?.map((action) => (
                <Link
                  key={action.title}
                  href={action.link}
                  target={action.isBlank ? '_blank' : ''}
                  style={{
                    textDecoration: 'none',
                    color: theme.colors.primary[7],
                    width: 191,
                  }}
                  className={styles.aHero}
                >
                  {action.title}
                </Link>
              ))}
            </Group>
          </Stack>
          <Stack
            h={{ md: '100%', sm: '65%', xs: '55%' }}
            justify="center"
            sx={() => ({
              [theme.fn.smallerThan('xs')]: {
                display: 'none',
              },
            })}
          >
            {withImageBackground ? (
              <BackgroundImage
                h="100%"
                w="100%"
                src={`${assetBaseUrl}/assets/new-landing/blur-hero.webp`}
                radius="xs"
              >
                <Image width="100%" src={image} alt="security" mx="auto" />
              </BackgroundImage>
            ) : (
              <Image width="100%" src={image} alt="security" mx="auto" />
            )}
          </Stack>
          <Stack
            mt="lg"
            justify="center"
            sx={() => ({
              display: 'none',
              [theme.fn.smallerThan('xs')]: {
                display: 'block',
              },
            })}
          >
            {withImageBackground ? (
              <BackgroundImage
                h={300}
                mx="auto"
                w={300}
                src={`${assetBaseUrl}/assets/new-landing/blur-hero.webp`}
                radius="xs"
              >
                <Image width={300} src={image} alt="security" mx="auto" />
              </BackgroundImage>
            ) : (
              <Image width={300} src={image} alt="security" mx="auto" />
            )}
          </Stack>
        </SimpleGrid>
      </Container>
    </Box>
  );
}
