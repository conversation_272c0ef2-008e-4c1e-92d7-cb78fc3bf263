import { Icon } from '@/components/common/icon';
import { UserApiResponse } from '@/store/user';
import { Group, Text } from '@mantine/core';
import currency from 'currency.js';
import useTranslation from 'next-translate/useTranslation';
import React from 'react';

interface Props {
  currencyItem: UserApiResponse['currencies'][0] | undefined;
  hiddenMinMax: boolean;
}

function MinMaxBalance({ currencyItem, hiddenMinMax }: Props) {
  const { t } = useTranslation();

  const formatCurrency = (value: string | number | undefined) => {
    if (!value) return '0.00';
    return currency(value, {
      symbol: '',
      precision: currencyItem?.precision ?? 2,
    }).format();
  };

  const getDisplayedMax = () => {
    const balance = Number(currencyItem?.amount || 0);
    const min = Number(currencyItem?.exchangeMin || 0);
    const max = Number(currencyItem?.exchangeMax || 0);

    const isBelowMin = balance <= min;
    const isAboveMax = balance > max;

    if (isBelowMin || isAboveMax) {
      return max;
    }

    return balance;
  };

  return (
    <Group spacing="xs" position="apart">
      {!hiddenMinMax ? (
        <Group spacing="xs">
          <div>
            <Text span weight={500} color="dimmed">
              {t('common:min')}
              {' '}
              {formatCurrency(currencyItem?.exchangeMin)}
            </Text>
            <Text mx={4} span weight={500} color="dimmed">
              {currencyItem?.symbol ?? ''}
            </Text>
          </div>
          -
          <div>
            <Text span weight={500} color="dimmed">
              {t('common:max')}
              {' '}
              {formatCurrency(getDisplayedMax())}
            </Text>
            <Text mx={4} span weight={500} color="dimmed">
              {currencyItem?.symbol ?? ''}
            </Text>
          </div>
        </Group>
      ) : (<div />)}

      <div style={{ display: 'flex', alignItems: 'center', gap: 5 }}>
        <Icon icon="wallet" size="1.6rem" color="gray" />
        <Text size="lg" weight={500} color="dimmed">
          {formatCurrency(currencyItem?.amount)}
        </Text>
        <Text size="lg" weight={500} color="dimmed">
          {currencyItem?.symbol}
        </Text>
      </div>
    </Group>
  );
}

export default MinMaxBalance;
