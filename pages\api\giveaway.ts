/**
 * This handler to create giveaway in notion database.
 */
import { httpCode, NOTION_DATABASE_ID } from '@/data';
import { notionMutation } from '@/lib';
import { createGiveawayBackendRequestSchema } from '@/store/giveaway';
import createApiError from '@/utils/api-utils/create-api-error';
import { captchaValidator } from '@/utils/captcha-validator';
import { NextApiRequest, NextApiResponse } from 'next';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST') {
    try {
      // get token "cha<PERSON>ey" returned from captcha and pass it to "captchaValidator" to check toke validity
      // if validation success will call  api,else return error.
      const chaKey = req?.query?.chaKey;
      await captchaValidator({
        token: chaKey as string,
        secret: process.env.RECAPTCHA_SECRET_KEY as string,
      });

      const response = await notionMutation({
        database: NOTION_DATABASE_ID.givaway,
        body: createGiveawayBackendRequestSchema.parse(req.body),
      });

      return res.status(httpCode.SUCCESS).json({
        message: 'Link sent successfully',
        date: (response as { created_time: string })?.created_time,
      });
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }
  const error = createApiError({ error: '' });
  return res.status(httpCode.INTERNAL_SERVER_ERROR).json(error);
}

export default handler;
