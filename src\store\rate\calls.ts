import { apiEndpoints } from '@/data';
import { ApiClient } from '@/lib';

enum queryKeys {
  rates = 'rates',
}
/**
 * @description function calls handler in "/rate" api route to return rates
 * @returns array of rates
 */
const getRatesRequest = () => ApiClient.get(apiEndpoints.rate())
  .then((res) => res?.data)
  .catch((e) => {
    // handleApiError(e, true);
    throw e.response?.data;
  });

export const getRatesQuery = () => ({
  queryKey: [queryKeys.rates],
  queryFn: () => getRatesRequest(),
  refetchOnWindowFocus: false,
});
