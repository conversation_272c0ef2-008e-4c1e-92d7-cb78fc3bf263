export const operationIcon = ({ operationType }:{operationType:'deposit'|'withdraw'|'transfer'|'exchange'|'received'|string}) => {
  if (operationType === 'deposit') {
    return 'circle-plus';
  }
  if (operationType === 'withdraw') {
    return 'circle-minus';
  }
  if (operationType === 'transfer' || operationType === 'payment_link') {
    return 'circle-arrow-up-right';
  }
  if (operationType === 'received') {
    return 'circle-arrow-down';
  }
  if (operationType === 'massPayout') {
    return 'arrows-move';
  }
  if (operationType === 'scan') {
    return 'scan';
  }
  return 'refresh';
};
