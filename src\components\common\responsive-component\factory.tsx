/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { ResponsiveComponentProps } from './types';
import ResponsiveComponent from './responsive-component';

interface FactoryProps<T extends (...args: any) => any>
  extends Omit<ResponsiveComponentProps<T>, 'props' | 'children'> {
  defaultProps?: ResponsiveComponentProps<T>['props'];
}
const Factory = <T extends (...args: any) => any>(props: FactoryProps<T>) => {
  const { sharedProps = {}, defaultProps = {}, component } = props;
  return (childProps: Omit<ResponsiveComponentProps<T>, 'component'>) => {
    const {
      props: childrenProps = {},
      sharedProps: childrenSharedProps = {},
      children = null,
    } = childProps;
    return (
      <ResponsiveComponent
        component={component}
        props={{ ...defaultProps, ...childrenProps }}
        sharedProps={{ ...sharedProps, ...childrenSharedProps } as any}
      >
        {children}
      </ResponsiveComponent>
    );
  };
};

export default Factory;
