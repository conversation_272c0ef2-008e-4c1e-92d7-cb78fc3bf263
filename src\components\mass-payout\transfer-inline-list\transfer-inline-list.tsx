import { Stack } from '@mantine/core';
import React, { useState } from 'react';

import { UserApiResponse } from '@/store/user';
import { TransferInLineCard } from '../transfer-inline-card';
import useTranslation from 'next-translate/useTranslation';
import { useMutation, useQueryClient } from '@tanstack/react-query';

import { ConfirmModal } from '@/components/common/confirm-modal';
import { CodeConfirmModal } from '@/components/2fa/code-confirm-modal';
import { useDisclosure } from '@mantine/hooks';
import { getCookie } from 'cookies-next';

import { ErrorFrontendType } from '@/types';
import { createMassPayoutMutation } from '@/store/mass-payout/calls';
import { MassPayoutApiResponse, TransferItemType } from '@/store/mass-payout';
import SubmitButton from '@/components/common/submit-button';
import { Captcha } from '@/components/common/captcha';
import { useCaptcha } from '@/hooks';
import { playCaptcha } from '@/data';

interface Props {
  selectedCurrency: UserApiResponse['currencies'][0] | undefined;
  responseData: MassPayoutApiResponse['data'] | undefined;
  setResponseData: (v: MassPayoutApiResponse['data']) => void;
  fileDataChecked: TransferItemType[];
  hasError: boolean;
}
function TransferInlineList({
  fileDataChecked,
  selectedCurrency,
  responseData,
  setResponseData,
  hasError,
}: Props) {
  const { t } = useTranslation();
  const [opened, { open, close }] = useDisclosure(false);
  const [openedCode, setOpenedCode] = useState(false);
  const [code, setCode] = useState('');
  const [codeError, setCodeError] = useState('');
  const [isLoading, setLoading] = useState(false);

  const { execute, reCaptchaRef, reset } = useCaptcha();

  const queryClient = useQueryClient();
  const { mutate } = useMutation({
    ...createMassPayoutMutation(),
    onSuccess: (res) => {
      setResponseData(res?.data);
      queryClient.invalidateQueries({ queryKey: ['user'] });
      close();
      setCodeError('');
      setOpenedCode(false);
      setCode('');
      reset();
      setLoading(false);
    },
    onError(error: ErrorFrontendType) {
      setCode('');
      if (error?.response?.data?.message?.key === 'invalidCode') {
        setCodeError('invalidCode');
      } else setCodeError('');
      reset();
      setLoading(false);
    },
  });
  const onSubmit = (qrCode: string | undefined) => {
    setLoading(true);
    const body = {
      body: {
        currency: selectedCurrency?.id ?? '',
        transfers: fileDataChecked?.map((i) => ({
          account: i?.account,
          amount: `${i?.amount}`,
          notes: i?.notes ?? '',
        })),
      },
      qrCode,
    };
    if (reCaptchaRef.current) {
      execute((token) => mutate({
        ...body,
        chaKey: token,
      }));
    } else {
      mutate(body);
    }
  };
  if (fileDataChecked?.length === 0) {
    return <div />;
  }
  if (responseData && responseData?.length > 0) {
    return (
      <Stack>
        {responseData?.map((i, index) => (
          <TransferInLineCard
            id={index + 1}
            selectedCurrency={selectedCurrency}
            key={Math.random()}
            amount={i?.amount}
            account={i?.account}
            note={i?.notes}
            error={i?.error ?? ''}
            success={i?.success}
            isReady={false}
          />
        ))}
      </Stack>
    );
  }
  return (
    <>
      <form>
        <Stack>
          {fileDataChecked?.map((i, index) => (
            <TransferInLineCard
              id={index + 1}
              selectedCurrency={selectedCurrency}
              key={i?.id}
              amount={i?.amount}
              account={i?.account}
              note={i?.notes}
              error={i?.error}
              success={i?.success}
              isReady={i?.success}
            />
          ))}
          <SubmitButton
            disabled={hasError}
            loading={isLoading}
            color="red"
            onClick={
              getCookie('2fa-enabled') ? () => setOpenedCode(true) : open
            }
          >
            {t('common:transfer')}
          </SubmitButton>
        </Stack>
        <ConfirmModal
          close={close}
          openedDefault={opened}
          isLoading={isLoading}
          onClick={() => onSubmit(undefined)}
          message={<div>{t('common:completeOperation')}</div>}
        />
        <CodeConfirmModal
          codeError={codeError}
          btnText={t('common:transfer')}
          setOpenedCode={setOpenedCode}
          isLoading={isLoading}
          onClick={onSubmit}
          openedDefault={openedCode}
          code={code}
          setCode={setCode}
          title={t('common:enterCode')}
          closeable={false}
          additionalContent={undefined}
        />
      </form>
      <Captcha reCaptchaRef={reCaptchaRef} active={playCaptcha} />
    </>
  );
}

export default TransferInlineList;
