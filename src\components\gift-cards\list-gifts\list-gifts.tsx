import { EmptyData } from '@/components/common/empty-data';
import { getGiftCardsQuery, GiftCardsApiResponse } from '@/store/gift-card';
import {
  Group, Stack, UnstyledButton, useMantineTheme,
} from '@mantine/core';
import { useInfiniteQuery } from '@tanstack/react-query';
import useTranslation from 'next-translate/useTranslation';
import React, { useState } from 'react';
import GiftCardItem from './gift-card-item';
import GiftCardItemSkeleton from './gift-card-item-skeleton';

interface RenderGiftsListProps {
  gifts: GiftCardsApiResponse['data'] | undefined;
}
export function RenderGiftsList({ gifts }: RenderGiftsListProps) {
  return (
    <Stack mt="md">
      {gifts?.map((item) => (
        <GiftCardItem gift={item} key={item?.uid} />
      ))}
    </Stack>
  );
}

function ListGifts() {
  const { t } = useTranslation();
  const theme = useMantineTheme();
  const [keys, setKeys] = useState([1]);
  const {
    data, fetchNextPage, hasNextPage, isFetchingNextPage, status,
  } = useInfiniteQuery(
    getGiftCardsQuery({
      pagination: {
        pageSize: 10,
      },
    }),
  );
  return status === 'loading' ? (
    <Stack mt="md">
      <GiftCardItemSkeleton />
      <GiftCardItemSkeleton />
      <GiftCardItemSkeleton />
    </Stack>
  ) : (
    <>
      {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        data?.pages?.map((i: any, index: number) => (i?.data?.length === 0 ? (
          <EmptyData message="" key={keys[index]} />
        ) : (
          <RenderGiftsList key={keys[index]} gifts={i?.data} />
        )))
      }

      {isFetchingNextPage ? (
        <Stack mt="md">
          <GiftCardItemSkeleton />
          <GiftCardItemSkeleton />
          <GiftCardItemSkeleton />
        </Stack>
      ) : (
        <Group my="md" position="center">
          <UnstyledButton
            sx={{
              display: !hasNextPage ? 'none' : '',
              color: theme.colors.primary[5],
            }}
            onClick={() => {
              fetchNextPage();
              keys.push(keys[keys.length - 1] + 1);
              setKeys(keys);
            }}
          >
            {t('common:showMore')}
          </UnstyledButton>
        </Group>
      )}
    </>
  );
}

export default ListGifts;
