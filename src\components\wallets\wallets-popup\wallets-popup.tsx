import {
  ActionIcon,
  Button,
  Group,
  Modal,
  ScrollArea,
  SimpleGrid,
  Text,
  Tooltip,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';

import useTranslation from 'next-translate/useTranslation';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { getUserQuery, updateUserMutation } from '@/store/user';
import { getCurrenciesQuery } from '@/store/currencies';
import { prop, unionWith, eqBy } from 'ramda';
import currency from 'currency.js';
import { IconEdit } from '@tabler/icons-react';
import { useEffect, useState } from 'react';
import { WalletItem } from './wallet-item';
import { WalletItemSkeleton } from './wallet-item-skeleton';
import { preserveUserData } from '@/utils/profile-utils';

function WalletsPopup() {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const [opened, { open, close }] = useDisclosure(false);
  const [selectedItems, setSelectedItems] = useState<string[] | undefined>();
  // fetch user currencies and favorites currencies
  // note: enabled is false because we don't need to refetch data just read it from cashing query
  const { data, isFetching } = useQuery({
    ...getUserQuery({}),
    enabled: false,
  });
  useEffect(() => {
    setSelectedItems(data?.favoriteCurrencies?.map((i) => i?.value));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // merge favorites currencies with user currencies
  const allUserCurrenciesWithFavorites = unionWith(
    eqBy(prop('id')),
    data?.currencies?.map((curr) => ({
      ...curr,
      disabled: true,
      checked: true,
    })) ?? [],
    data?.favoriteCurrencies?.map((curr) => ({
      ...curr,
      disabled: false,
      checked: true,
    })) ?? [],
  );

  // call get all currencies api to get all currencies
  const allCurrencies = useQuery(getCurrenciesQuery({}));
  // merge all currencies with user currencies and his favorite currencies to get "amount" props from user currencies
  const mergedCurrencies = unionWith(
    eqBy(prop('id')),
    allUserCurrenciesWithFavorites ?? [],
    allCurrencies?.data?.data ?? [],
  );

  const loading = isFetching || allCurrencies.isFetching;
  // update user mutation to save favorite currencies
  const { mutate, isLoading } = useMutation({
    ...updateUserMutation(),
    onSuccess: () => {
      queryClient.invalidateQueries(['user']);
      close();
    },
  });
  //  submit handler
  const onSubmit = () => {
    const newFavorites = selectedItems?.map((i) => +i) ?? [];

    mutate(preserveUserData({ favoriteCurrencies: newFavorites }, data));
  };
  // handle item check trigger
  const handleItemClick = (id: string) => {
    if (selectedItems && selectedItems.length > 0) {
      if (selectedItems.includes(id)) {
        setSelectedItems(selectedItems.filter((i) => i !== id));
      } else setSelectedItems([...selectedItems, id]);
    } else setSelectedItems([id]);
  };

  const items = mergedCurrencies?.map((item) => (
    <WalletItem
      onChange={() => handleItemClick(item?.value)}
      key={item?.id}
      disabled={item?.disabled}
      defaultChecked={item?.checked}
      description={`${currency(item?.amount, {
        symbol: '',
        precision: item?.precision ?? 2,
      })} ${item?.symbol ?? ''}`}
      image={item?.image ?? ''}
      title={item?.label}
      titleAr={item?.labelAr}
    />
  ));

  const skeleton = Array.from(Array(14), (_, index) => (
    <WalletItemSkeleton key={index} />
  ));

  return (
    <>
      <Modal
        opened={opened}
        onClose={close}
        title={t('common:wallets')}
        centered
        radius="lg"
        size="auto"
      >
        <ScrollArea scrollbarSize={5} h={480} pr={8} mr={-8}>
          <SimpleGrid
            cols={1}
            breakpoints={[
              { maxWidth: 'sm', cols: 1 },
              { minWidth: 'sm', cols: 2 },
            ]}
          >
            {loading ? skeleton : items}
          </SimpleGrid>
        </ScrollArea>
        <Button
          onClick={onSubmit}
          mt="md"
          mb="xs"
          fullWidth
          loading={isLoading}
        >
          {t('common:save')}
        </Button>
      </Modal>

      <Group position="apart" mt="md" w="100%">
        <Text>{t('common:wallets')}</Text>
        <Group spacing="xs">
          <Text size="sm">{t('common:addRemoveCurrencies')}</Text>
          <Tooltip label={t('common:editWallets')}>
            <ActionIcon size="md" variant="outline" onClick={open}>
              <IconEdit />
            </ActionIcon>
          </Tooltip>
        </Group>
      </Group>
    </>
  );
}

export default WalletsPopup;
