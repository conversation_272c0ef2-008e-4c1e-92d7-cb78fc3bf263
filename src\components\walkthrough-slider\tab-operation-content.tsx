import {
  But<PERSON>,
  Group,
  Image,
  Slider,
  <PERSON>ack,
  Stepper,
  useMantineTheme,
} from '@mantine/core';
import React, { useState } from 'react';
import { useStyles } from './style';
import { useRouter } from 'next/router';
import { useLocalStorage, useMediaQuery } from '@mantine/hooks';
import useTranslation from 'next-translate/useTranslation';

type imageUrl = {
  ar: string[];
  en: string[];
};

interface Props {
  mobileImages: imageUrl;
  desktopImages: imageUrl;
  imageWidth: number;
  close: () => void;
  operation: string;
  setOperationType: (v: string | null) => void;
}

function TabOperationContent({
  mobileImages,
  desktopImages,
  imageWidth,
  close,
  operation,
  setOperationType,
}: Props) {
  const { classes } = useStyles();
  const [, setValue] = useLocalStorage<string>({
    key: 'walkthrough-opened',
    defaultValue: 'false',
    getInitialValueInEffect: false,
  });
  const [active, setActive] = useState(0);
  const { t } = useTranslation();
  const theme = useMantineTheme();
  const { locale } = useRouter();
  const listOperations = [
    'deposit',
    'exchange',
    'massPayout',
    'transfer',
    'withdraw',
  ];
  const isArLanguage = locale === 'ar';
  const matchesMaxXs = useMediaQuery(
    `(max-width: ${theme.breakpoints.xs})`,
    true,
    {
      getInitialValueInEffect: false,
    },
  );
  const allImages = matchesMaxXs ? mobileImages : desktopImages;
  const languageImages = isArLanguage ? allImages.ar : allImages.en;
  const nextStep = () => setActive((current) => (current < languageImages.length - 1 ? current + 1 : current));
  const prevStep = () => setActive((current) => (current > 0 ? current - 1 : current));
  const steps = languageImages.map((url) => (
    <Stepper.Step key={url}>
      <Image
        mx="auto"
        height={matchesMaxXs ? '562px' : '100%'}
        width={matchesMaxXs ? '316px' : '100%'}
        src={url}
        radius="lg"
      />
    </Stepper.Step>
  ));

  const onWalkthroughDon = () => {
    setValue('true');
    setActive(0);
    setOperationType(listOperations[0]);
    close();
  };
  const onNextBtnClick = () => {
    if (active === languageImages.length - 1 && operation === 'withdraw') {
      onWalkthroughDon();
    } else if (active === languageImages.length - 1) {
      const currentIndex = listOperations.indexOf(operation);
      setOperationType(listOperations[currentIndex + 1]);
    } else {
      nextStep();
    }
  };

  return (
    <Stack pb={matchesMaxXs ? 30 : 0} align="center" justify="center">
      <div>
        <Stepper
          w={imageWidth}
          classNames={classes}
          active={active}
          onStepClick={setActive}
          breakpoint="sm"
        >
          {steps}
        </Stepper>

        <Slider
          my="xs"
          classNames={classes}
          showLabelOnHover={false}
          defaultValue={0}
          value={active}
          max={languageImages?.length}
        />
        <Group position="apart">
          <Group position="center">
            <Button onClick={prevStep}>{t('common:previous')}</Button>
            <Button onClick={onNextBtnClick}>
              {t(
                `common:${
                  active === languageImages.length - 1 ? 'continue' : 'next'
                }`,
              )}
            </Button>
          </Group>
          <Button
            variant="outline"
            color="red"
            size="sm"
            onClick={onWalkthroughDon}
          >
            {t('common:skip')}
          </Button>
        </Group>
      </div>
    </Stack>
  );
}

export default TabOperationContent;
