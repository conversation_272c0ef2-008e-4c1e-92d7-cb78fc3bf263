import { AxiosError } from 'axios';
import { z } from 'zod';
import {
  FALLBACK_ERROR_MESSAGE,
  FALLBACK_ERROR_MESSAGE_KEY,
  httpCode,
} from '@/data';

const translatedFieldSchema = z.object({
  en: z.string().nullable(),
  ar: z.string().nullable(),
});

type TranslatedField = z.infer<typeof translatedFieldSchema>;

type BackendErrorBody = {
  data: null;
  error: {
    status: number;
    name: string;
    message: string;
    details: {
      key?: string;
      params?: Record<
        string,
        string | TranslatedField | TranslatedField[] | string[]
      >;
    };
  };
};

type ApiError = {
  code: number;
  message: {
    fallback: string;
    key: string;
    params?: Record<
      string,
      string | TranslatedField | TranslatedField[] | string[]
    >;
  };
  errors?: Error[];
};
// eslint-disable-next-line @typescript-eslint/no-explicit-any
type CreateApiError = (args: { error: unknown | any }) => ApiError;
// eslint-disable-next-line sonarjs/cognitive-complexity
const createApiError: CreateApiError = ({ error }) => {
  let errorBody: ApiError;

  if (error instanceof AxiosError) {
    const { response } = error as AxiosError<BackendErrorBody>;

    errorBody = {
      code: response?.data?.error?.status || httpCode.BAD_REQUEST,
      message: {
        fallback: response?.data?.error?.message || FALLBACK_ERROR_MESSAGE,
        key:
          (response?.data.error?.details?.key as string)
          || response?.data?.error?.name
          || FALLBACK_ERROR_MESSAGE_KEY,
        params: response?.data.error?.details?.params ?? {},
      },
      errors: process.env.DEBUG ? [error as Error] : [],
    };
  } else if (error?.message === 'invalidCode') {
    errorBody = {
      code: httpCode.BAD_REQUEST,
      message: {
        fallback: 'Invalid Code',
        key: 'invalidCode',
      },
      errors: process.env.DEBUG ? [error as Error] : [],
    };
  } else if (error?.message === 'captcha error') {
    errorBody = {
      code: httpCode.BAD_REQUEST,
      message: {
        fallback: 'Wrong Captcha',
        key: 'captchaError',
      },
      errors: process.env.DEBUG ? [error as Error] : [],
    };
  } else {
    errorBody = {
      code: httpCode.INTERNAL_SERVER_ERROR,
      message: {
        fallback: FALLBACK_ERROR_MESSAGE,
        key: FALLBACK_ERROR_MESSAGE_KEY,
      },
      errors: process.env.DEBUG ? [error as Error] : [],
    };
  }

  // eslint-disable-next-line no-console
  if (process.env.DEBUG) console.log(error);

  return errorBody;
};

export default createApiError;
