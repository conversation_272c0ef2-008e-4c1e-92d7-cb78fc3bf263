import { PaymentMethodsApiResponse } from '@/store/payment-methods';
import { numberInputFormatter, validateAmountStep } from '@/utils';
import { NumberInput, Text } from '@mantine/core';
import { UseFormReturnType } from '@mantine/form';
import useTranslation from 'next-translate/useTranslation';
import React from 'react';

interface Props {
  precision: number;
  selectedPaymentMethod: PaymentMethodsApiResponse['data'][0];
  isMinMaxError: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  form: UseFormReturnType<any>;
  operationType: 'deposit' | 'withdraw';
}

function AmountInputDepositWithdraw({
  precision,
  selectedPaymentMethod,
  isMinMaxError,
  form,
  operationType,
}: Props) {
  const { t } = useTranslation();
  const isAmountStepError = validateAmountStep(
    selectedPaymentMethod?.additionalFields?.step,
    form?.values?.amount,
  );
  const LabelText = (
    <Text tt="capitalize">
      {t(
        `common:${
          operationType === 'withdraw' ? 'toBeReceivedAmount' : 'amount'
        }`,
      )}
    </Text>
  );
  return (
    <>
      <NumberInput
        precision={precision}
        radius="lg"
        size="sm"
        label={LabelText}
        hideControls
        step={0}
        formatter={numberInputFormatter}
        {...form.getInputProps('amount')}
      />
      {isMinMaxError && (
        <Text mx={2} color="red" size="sm">
          {t(
            `common:${
              operationType === 'deposit' ? 'minMaxErrorDeposit' : 'minMaxError'
            }`,
          )}
        </Text>
      )}
      {isAmountStepError && (
        <Text mx={2} color="red" size="sm">
          {`${t('common:stepAmountError')} ${
            selectedPaymentMethod?.additionalFields?.step
          }`}
        </Text>
      )}
    </>
  );
}

export default AmountInputDepositWithdraw;
