import { GlobalCard } from '@/components/common/global-card';
import { TranslatedTextValue } from '@/components/common/translated-text-value';
import { ROUTES } from '@/data';
import {
  Stack, Group, Text, Image,
} from '@mantine/core';
import currency from 'currency.js';

import { useRouter } from 'next/router';

interface MainStatProps {
  title: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  icon: string | any;
  value: number;
  balanceUsd: number;
  currencySign: string;
  currencySignAr: string | null;
  withExchangeList: boolean;
  uid: string;
  precision: number;
}

export default function WalletCardSmall({
  icon,
  title,
  value,
  balanceUsd,
  currencySign,
  currencySignAr,
  withExchangeList,
  uid,
  precision,
}: MainStatProps) {
  const { push } = useRouter();

  return (
    <GlobalCard
      props={{ style: { cursor: 'pointer' } }}
      onClick={() => (withExchangeList ? {} : push(`${ROUTES.walletHistory.path}/${uid}`))}
      key={title}
    >
      <Group position="apart">
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
          <Image src={icon} width={40} radius={50} alt="currency" />
          <div style={{ textAlign: 'start' }}>
            <Text tt="capitalize" weight={700} size="md">
              <TranslatedTextValue
                keyEn={currencySign}
                keyAr={currencySignAr}
              />
            </Text>
            <Text weight={500} size="md" color="dimmed" tt="uppercase">
              {title}
            </Text>
          </div>
        </div>
        <Stack align="end" spacing={2}>
          <Text weight={700}>
            {currency(value, { symbol: '', precision }).format()}
          </Text>
          {!withExchangeList && (
            <Text color="dimmed" weight={500}>
              {currency(balanceUsd, { symbol: '' }).format()}
              {' '}
              USD
            </Text>
          )}
        </Stack>
      </Group>
    </GlobalCard>
  );
}
