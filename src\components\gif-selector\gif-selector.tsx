/* eslint-disable react/require-default-props */
import { useEffect, useState } from 'react';
import GifPicker, { TenorImage } from 'gif-picker-react';
import {
  ActionIcon,
  Box,
  Button,
  Group,
  Image,
  Modal,
  Stack,
  Text,
  useMantineTheme,
} from '@mantine/core';
import { IconX } from '@tabler/icons-react';
import { useRouter } from 'next/router';
import { defaultLocale } from '@/data';
import useTranslation from 'next-translate/useTranslation';

interface GifSelectorApi {
  clear: Function;
}
interface GifSelectorProps {
  onGifSelected?: (v?: TenorImage) => void;
  componentApi?: (props: GifSelectorApi) => void;
}
function GifSelector(props: GifSelectorProps) {
  const { onGifSelected, componentApi } = props;
  const [selectedGif, setSelectedGif] = useState<string | null>(null);
  const [tempFile, setTempFile] = useState<TenorImage | null>(null);
  const [opened, setOpened] = useState(false);
  const theme = useMantineTheme();
  const { locale } = useRouter();
  const { t } = useTranslation('common');
  //* ***********Apis*************************************************** */
  const clear = () => {
    setSelectedGif(null);
    setTempFile(null);
    setOpened(false);
  };
  //* *********************Hooks**************************************** */
  useEffect(() => {
    if (componentApi) componentApi({ clear });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return (
    <>
      <Stack>
        {selectedGif && (
          <Box w={200} pos="relative">
            <Image src={selectedGif} alt="gif" />
            <ActionIcon
              pos="absolute"
              variant="filled"
              size="xs"
              top={-5}
              right={-5}
              color="red"
              onClick={() => {
                setSelectedGif(null);
                if (onGifSelected) onGifSelected(undefined);
              }}
            >
              <IconX stroke={3} />
            </ActionIcon>
          </Box>
        )}
        <Button
          variant="outline"
          onClick={() => {
            setOpened(true);
          }}
        >
          {selectedGif ? t('changeGif') : t('chooseGif')}
        </Button>
      </Stack>
      <Modal
        opened={opened}
        onClose={() => {
          setOpened(false);
          setTempFile(null);
        }}
        withCloseButton={false}
      >
        <Stack>
          <GifPicker
            tenorApiKey="AIzaSyALQAIJwGGpi7iyEQQ1pjRWLVqXWR67aiw"
            width="100%"
            onGifClick={(v) => {
              setTempFile(v);
            }}
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
            theme={theme.colorScheme as any}
            locale={locale || defaultLocale}
          />
          {!!tempFile && (
            <Group spacing={5}>
              <Text weight={500} color="dimmed">
                {t('fileName')}
                :
              </Text>
              <Text size="xs" mt={5}>
                {tempFile.description}
              </Text>
            </Group>
          )}
          <Group>
            <Button
              variant="outline"
              color="gray"
              onClick={() => {
                setOpened(false);
                setTempFile(null);
              }}
            >
              {t('cancel')}
            </Button>
            <Button
              variant="outline"
              onMouseDown={() => {
                setSelectedGif(null);
              }}
              onClick={() => {
                if (tempFile) {
                  setSelectedGif(tempFile.url);
                  if (onGifSelected) onGifSelected(tempFile);
                }
                setOpened(false);
                setTempFile(null);
              }}
              disabled={!tempFile}
            >
              {t('save')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </>
  );
}

export default GifSelector;
