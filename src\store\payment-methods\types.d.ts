import { z } from 'zod';
import { paymentMethodsApiResponseSchema } from './response-transformer';
import { Pagination } from '@/types';

export type Filter = {
  showInRatesBar?: boolean | null;
  depositCurrencyId?: string;
  withdrawCurrencyId?: string;
};
export interface getPaymentMethodsQueryProps {
  populate?: {
    depositCurrencies?: boolean;
    withdrawCurrencies?: boolean;
    icon?: boolean;
    depositCustomFields?: boolean;
    withdrawcustomFields?: boolean;
  };
  pagination?: Pagination;
  filters?: Filter;
}
export type PaymentMethodsApiResponse = z.infer<
  typeof paymentMethodsApiResponseSchema
>;
