import {
  Title, Text, Button, Container, useMantineTheme,
} from '@mantine/core';
import { Dots } from './dots';
import classes from './style.module.css';
import useTranslation from 'next-translate/useTranslation';
import Link from 'next/link';
import { useRouter } from 'next/router';

function HeroHelp() {
  const { t } = useTranslation();
  const { locale } = useRouter();
  const theme = useMantineTheme();
  const darckColorSchema = theme.colorScheme === 'dark';
  return (
    <Container className={classes.wrapper} size={1400}>
      <Dots
        className={darckColorSchema ? classes.dotsLight : classes.dots}
        style={{ left: 0, top: 0 }}
      />
      <Dots
        className={darckColorSchema ? classes.dotsLight : classes.dots}
        style={{ left: 60, top: 0 }}
      />
      <Dots
        className={darckColorSchema ? classes.dotsLight : classes.dots}
        style={{ left: 0, top: 140 }}
      />
      <Dots
        className={darckColorSchema ? classes.dotsLight : classes.dots}
        style={{ right: 0, top: 60 }}
      />

      <div className={classes.inner}>
        <Title
          className={`${classes.title} ${
            darckColorSchema ? classes.titleLightColor : ''
          } `}
        >
          {t('common:helpTitle')}
        </Title>

        <Container p={0} size={600}>
          <Text size="lg" c="dimmed" className={classes.description}>
            {t('common:helpDescription')}
          </Text>
        </Container>

        <div className={classes.controls}>
          <Button
            className={classes.control}
            size="lg"
            variant="default"
            color="gray"
            component={Link}
            href={`https://kazawallet.trengohelp.com/${
              locale === 'ar' ? 'ar' : 'en'
            }`}
            target="_blank"
          >
            {t('common:helpCenter')}
          </Button>
          <Button
            className={classes.control}
            size="lg"
            component={Link}
            href="https://telegram.me/Kazawallet_Bot"
            target="_blank"
          >
            {t('common:telegram')}
          </Button>
        </div>
      </div>
    </Container>
  );
}

export default HeroHelp;
