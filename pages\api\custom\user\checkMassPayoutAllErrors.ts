/**
 * This handler to check mass payout transfers.
 */
import { apiEndpoints, httpCode } from '@/data';
import { BackendClient } from '@/lib';
import {
  checkMassPayoutApiResponseSchema,
  createMassPayoutBackendRequestSchema,
} from '@/store/mass-payout';

import createApiError from '@/utils/api-utils/create-api-error';
import createApiResponse from '@/utils/api-utils/create-api-response';
import { getJwt } from '@/utils/api-utils/jwt';
import { NextApiRequest, NextApiResponse } from 'next';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token } = await getJwt(req);
  if (req.method === 'POST') {
    try {
      // check mass payout function to check items before create transfers
      const { data } = await BackendClient(req).post(
        apiEndpoints.massPayoutCheck(),
        {
          currencyId: createMassPayoutBackendRequestSchema.parse(req.body)
            .currency,
          ...createMassPayoutBackendRequestSchema.parse(req.body),
        },
        {
          headers: {
            authorization: token,
          },
        },
      );
      return createApiResponse(res, checkMassPayoutApiResponseSchema, data);
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }
  const error = createApiError({ error: '' });
  return res.status(httpCode.INTERNAL_SERVER_ERROR).json(error);
}

export default handler;
