import { CustomCopyButton } from '@/components/common/custom-copy-button';
import { Icon } from '@/components/common/icon';
import SubmitButton from '@/components/common/submit-button';
import { TranslatedTextValue } from '@/components/common/translated-text-value';
import { SelectCurrencyCard } from '@/components/currency/select-currency-card';
import { ROUTES, websiteUrl } from '@/data';
import { getCurrenciesQuery } from '@/store/currencies';
import {
  CreatePaymentLinkApiRequest,
  createPaymentLinkMutation,
} from '@/store/payment-links';
import { UserApiResponse } from '@/store/user';
import {
  Button,
  CloseButton,
  Group,
  Image,
  Modal,
  NumberInput,
  Paper,
  Stack,
  Text,
  Textarea,
  rem,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { useMutation, useQuery } from '@tanstack/react-query';
import dayjs from 'dayjs';
import useTranslation from 'next-translate/useTranslation';
import Link from 'next/link';
import { prop, unionWith, eqBy } from 'ramda';
import React, { useState } from 'react';

function CreatePaymentLinkModal({
  openCreatePaymentLink,
  setOpenCreatePaymentLink,
  userData,
}: {
  openCreatePaymentLink: boolean;
  setOpenCreatePaymentLink: (v: boolean) => void;
  userData: UserApiResponse;
}) {
  const { t } = useTranslation();
  const [selectedItem, setSelectedItem] = useState<UserApiResponse['currencies'][0]>();
  const [url, setUrl] = useState('');
  const allCurrencies = useQuery(
    getCurrenciesQuery({
      enabled: openCreatePaymentLink,
    }),
  );
  const mergedCurrencies = unionWith(
    eqBy(prop('id')),
    userData?.currencies ?? [],
    allCurrencies?.data?.data ?? [],
  );
  const form = useForm<CreatePaymentLinkApiRequest>({
    initialValues: {
      amount: '',
      currency: '',
      expiryDate: null,
      redirectUrl: null,
    },
  });
  const { mutate, isLoading } = useMutation({
    ...createPaymentLinkMutation(),
    onSuccess(res) {
      setUrl(`${websiteUrl}/payment-link/${res?.uid}`);
      form.reset();
    },
  });
  const submit = (values: typeof form.values) => {
    mutate({
      ...values,
      currency: `${selectedItem?.id}`,
      expiryDate: dayjs().format('YYYY-MM-DD'),
      amount: values.amount,
    });
  };
  const requestMoney = 'createPaymentLink';
  return (
    <Modal
      radius="lg"
      centered
      opened={openCreatePaymentLink}
      onClose={() => {
        setOpenCreatePaymentLink(false);
        setUrl('');
        setSelectedItem(undefined);
        form.reset();
      }}
      styles={{
        content: { overflow: 'visible !important' },
        header: { display: 'none' },
      }}
    >
      <div>
        <Group position="apart" mt="md" mb="xs">
          <Text weight={500}>{t(`common:${requestMoney}`)}</Text>
          <CloseButton
            onClick={() => {
              setOpenCreatePaymentLink(false);
              setUrl('');
              setSelectedItem(undefined);
              form.reset();
            }}
            aria-label="Close modal"
          />
        </Group>
        {url ? (
          <Stack>
            <Textarea
              radius="lg"
              minRows={2}
              autosize
              label={t('common:paymentLink')}
              readOnly
              value={url}
              rightSection={<CustomCopyButton value={url} />}
            />
            <Button
              onClick={() => {
                setOpenCreatePaymentLink(false);
                setSelectedItem(undefined);
                setUrl('');
              }}
              radius="lg"
            >
              {t('common:close')}
            </Button>
          </Stack>
        ) : (
          <Paper mih={200}>
            <form onSubmit={form.onSubmit(submit)}>
              <Stack>
                <Paper px="sm" withBorder radius={50}>
                  <SelectCurrencyCard
                    type="select"
                    data={mergedCurrencies ?? []}
                    setSelectedItem={(v) => {
                      setSelectedItem(v);
                    }}
                    selectedItem={selectedItem}
                    value={selectedItem?.value as string}
                  >
                    {!selectedItem ? (
                      <Group align="center" h="100%">
                        <Text color="dimmed" weight={500} tt="capitalize">
                          {t('common:selectCurrency')}
                        </Text>
                      </Group>
                    ) : (
                      <Group h="100%">
                        <Image
                          src={selectedItem?.image}
                          height={35}
                          width={35}
                          radius={50}
                          alt="currency"
                        />
                        <div>
                          <Text
                            weight={500}
                            size="lg"
                            color="dimmed"
                            tt="capitalize"
                          >
                            <TranslatedTextValue
                              keyEn={selectedItem?.label}
                              keyAr={selectedItem?.labelAr}
                            />
                          </Text>
                        </div>
                      </Group>
                    )}
                  </SelectCurrencyCard>
                </Paper>
                <NumberInput
                  radius="lg"
                  hideControls
                  min={0}
                  step={0}
                  label={<Text tt="capitalize">{t('common:amount')}</Text>}
                  {...form.getInputProps('amount')}
                  precision={selectedItem?.precision || 0}
                />
                <SubmitButton
                  disabled={!selectedItem || +form.values.amount <= 0}
                  type="submit"
                  loading={isLoading}
                >
                  {t(`common:${requestMoney}`)}
                </SubmitButton>
              </Stack>
            </form>
            <Link
              style={{ textDecoration: 'none' }}
              href={ROUTES.paymentLinkApiUsage.path}
            >
              <Group position="center" mt={10}>
                <Icon icon="info-circle" size={rem(20)} color="gray" />
                <Text span>{t('common:apiUsage')}</Text>
              </Group>
            </Link>
          </Paper>
        )}
      </div>
    </Modal>
  );
}

export default CreatePaymentLinkModal;
