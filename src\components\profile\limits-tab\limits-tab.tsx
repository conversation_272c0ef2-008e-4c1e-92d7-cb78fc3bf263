import { EmptyData } from '@/components/common/empty-data';
import { flags } from '@/components/rates/exhange-rates/rates-flags-mapping';
import { UserApiResponse } from '@/store/user';
import {
  Group,
  Image,
  Paper,
  Stack,
  Table,
  Text,
  Tabs,
  useMantineTheme,
} from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';
import { prop, sortBy } from 'ramda';
import React from 'react';
import { Badges } from '../badges';
import currency from 'currency.js';
import { TranslatedTextValue } from '@/components/common/translated-text-value';

interface LimitsTabProps {
  badges: UserApiResponse['badges'];
}

function LimitsTab({ badges }: LimitsTabProps) {
  const { t } = useTranslation();
  const theme = useMantineTheme();
  const badgeOrderSort = sortBy(prop('order'));
  const maxBadgeOrder = badges?.length > 0 ? badgeOrderSort(badges)[badges.length - 1] : undefined;
  const flagsKeys = Object.keys(flags);

  const renderCurrencyFlag = (currencyCode: string) => {
    if (currencyCode === 'EUR') {
      return <Image width={30} height="22.7px" radius="sm" src={flags.EUR} />;
    }
    if (currencyCode === 'SYP') {
      return <Image width={30} height="22.7px" radius="sm" src={flags.SYP} />;
    }
    if (flagsKeys.includes(currencyCode)) {
      return flags[currencyCode as 'SYP'];
    }
    return (
      <Text size={22} lh={1}>
        {flags.CRYPTO}
      </Text>
    );
  };
  const renderPaymentMethodTable = (
    deposit: Array<{
    currency: string;
    count: string | number;
    amount?: number;
    symbol?: string;
    precision?: number;
  }> = [],
    withdraw: Array<{
    currency: string;
    count: string | number;
    amount?: number;
    symbol?: string;
    precision?: number;
  }> = [],
  ) => {
    const operations = [
      ...deposit.map((d) => ({
        ...d,
        operation: 'deposit' as const,
      })),
      ...withdraw.map((w) => ({
        ...w,
        operation: 'withdraw' as const,
      })),
    ];

    return (
      <Table striped>
        <thead>
          <tr>
            <th style={{ width: 150 }}>{t('common:currency')}</th>
            <th style={{ width: 150 }}>{t('common:operation')}</th>
            <th style={{ width: 150 }}>{t('common:operationsCount')}</th>
            <th style={{ width: 150 }}>{t('common:amount')}</th>
          </tr>
        </thead>
        <tbody>
          {operations.map((op, index) => (
            // eslint-disable-next-line react/no-array-index-key
            <tr key={`${op.currency}-${op.operation}-${index}`}>
              <td style={{ width: 150 }}>
                <Group spacing="sm">
                  {renderCurrencyFlag(op.currency)}
                  {op.currency}
                </Group>
              </td>
              <td style={{ width: 150, color: op.operation === 'withdraw' ? '#e30935' : '#22b14c' }}>
                {t(`common:${op.operation}`)}
              </td>
              <td style={{ width: 150 }}>
                {op.count ?? '-'}
              </td>
              <td style={{ width: 150 }}>
                {op.amount
                  ? `${currency(op.amount, {
                    precision: op.precision,
                    symbol: '',
                  }).format()} ${op.symbol ?? ''}`
                  : '-'}
              </td>
            </tr>
          ))}
        </tbody>
      </Table>
    );
  };

  const renderOperationsTable = (
    currencyLimits: Array<{
      currency: string;
      count: string | number;
      amount?: number;
      symbol?: string;
      precision?: number;
    }>,
  ) => (
    <Table striped>
      <thead>
        <tr>
          <th style={{ width: 150 }}>{t('common:currency')}</th>
          <th style={{ width: 150 }}>{t('common:operationsCount')}</th>
          <th style={{ width: 150 }}>{t('common:amount')}</th>
        </tr>
      </thead>
      <tbody>
        {currencyLimits.map((limit) => (
          <tr key={limit.currency}>
            <td style={{ width: 150 }}>
              <Group spacing="sm">
                {renderCurrencyFlag(limit.currency)}
                {limit.currency}
              </Group>
            </td>
            <td style={{ width: 150 }}>{limit.count ?? '-'}</td>
            <td style={{ width: 150 }}>
              {limit.amount
                ? `${currency(limit.amount, {
                  precision: limit.precision,
                  symbol: '',
                }).format()} ${limit.symbol ?? ''}`
                : '-'}
            </td>
          </tr>
        ))}
      </tbody>
    </Table>
  );

  const renderPaymentMethods = () => {
    if (!maxBadgeOrder?.limits?.paymentMethods
      || Object.keys(maxBadgeOrder.limits.paymentMethods).length === 0) {
      return (
        <Stack mih={150} justify="center">
          <EmptyData message="noBadgeLimitPaymentMethods" />
        </Stack>
      );
    }

    return Object.entries(maxBadgeOrder.limits.paymentMethods).map(([method, limits]) => (
      <Stack spacing="xs" mt="xl" key={method}>
        <Text fw={500}>
          <TranslatedTextValue keyEn={method} keyAr={method} />
        </Text>
        {renderPaymentMethodTable(limits?.deposit, limits?.withdraw)}
      </Stack>
    ));
  };
  const renderOperations = () => {
    const operations = maxBadgeOrder?.limits?.operations;
    const hasValidOperations = operations
    && Object.values(operations).some((limits) => Array.isArray(limits) && limits.length > 0);

    if (!hasValidOperations) {
      return (
        <Stack mih={150} justify="center">
          <EmptyData message="noBadgeLimitOperation" />
        </Stack>
      );
    }

    return Object.entries(operations).map(([operation, limits]) => {
      if (!limits || !Array.isArray(limits) || limits.length === 0) {
        return null;
      }
      return (
        <Stack spacing="xs" mt="xl" key={operation}>
          <Text fw={500}>
            {operation.includes('_')
              ? t(`common:${operation.split('_')
                .map((word, index) => (index === 0 ? word : word.charAt(0).toUpperCase() + word.slice(1)))
                .join('')}`)
              : t(`common:${operation}`)}
          </Text>
          {renderOperationsTable(limits)}
        </Stack>
      );
    });
  };
  if (!badges || badges.length === 0) {
    return (
      <Paper p="md" withBorder radius="lg" mt="lg">
        <Stack mih={200} justify="center">
          <EmptyData message="noBadge" />
        </Stack>
      </Paper>
    );
  }
  return (
    <>
      <Badges badges={badges} />
      {badges?.length > 0 && maxBadgeOrder?.limits ? (
        <Paper p="md" withBorder radius="lg" mt="lg">
          <Text fw={500} mb="xs">{t('common:limitsPerDay')}</Text>
          <Text
            fw={300}
            mb="md"
            size={13}
            color={theme.colorScheme === 'dark' ? 'gray.5' : 'gray.6'}
          >
            {t('common:limitsPerDayDescription')}
          </Text>
          <Tabs defaultValue="payment-methods">
            <Tabs.List>
              <Tabs.Tab value="payment-methods">
                {t('common:paymentMethods')}
              </Tabs.Tab>
              <Tabs.Tab value="operations">
                {t('common:operations')}
              </Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="payment-methods" pt="md">
              {renderPaymentMethods()}
            </Tabs.Panel>

            <Tabs.Panel value="operations" pt="md">
              {renderOperations()}
            </Tabs.Panel>
          </Tabs>
        </Paper>
      ) : (
        <Paper p="md" withBorder radius="lg" mt="lg">
          <Text fw={500}>{t('common:limitsPerDay')}</Text>
          <Stack mih={200} justify="center">
            <EmptyData message="noLimit" />
          </Stack>
        </Paper>
      )}
    </>
  );
}

export default LimitsTab;
