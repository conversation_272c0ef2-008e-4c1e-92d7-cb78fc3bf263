/* eslint-disable @typescript-eslint/no-explicit-any */
import { InferComponentProps } from '@/types/infer-component-props';
import { ReactNode } from 'react';

export type breakpoints<T extends Array<any>> = {
  sm?: T;
  xl?: T;
  lg?: T;
  md?: T;
  xs?: T;
  lowerThanXl?: T;
  lowerThanLg?: T;
  lowerThanMd?: T;
  lowerThanSm?: T;
};
export interface ResponsiveComponentProps<T extends (...args: any) => any> {
  component: T;
  props?: breakpoints<InferComponentProps<T>>;
  sharedProps?: InferComponentProps<T>;
  children?: ReactNode;
}
