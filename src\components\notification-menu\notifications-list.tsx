import {
  Flex,
  Group,
  MantineStyleSystemProps,
  ScrollArea,
  Stack,
  Text,
} from '@mantine/core';
import React from 'react';

import { useQuery } from '@tanstack/react-query';
import NotificationItem from './notification-item';
import useTranslation from 'next-translate/useTranslation';
import NotificationItemSkeleton from './notification-item-skeleton';
import { getSettingQuery, SettingApiResponse } from '@/store/setting';
import { EmptyData } from '../common/empty-data';
import {
  concat, descend, prop, sort,
} from 'ramda';

function RenderNotifications({
  notificationsItems,
}: {
  notificationsItems: SettingApiResponse['notifications'];
}) {
  const returnFormattedData = () => {
    // format data to make date value as date
    const formattedData = notificationsItems?.map((i) => ({
      ...i,
      date: i?.date ? new Date(i?.date) : new Date(),
    }));

    // sort notifications by date
    const sortedData = sort(descend(prop('date')), formattedData);

    // handle sticky notifications to make them at the top of the list
    const stickyItems = sortedData?.filter(
      (notification) => !!notification?.sticky,
    );
    const nonStickyItems = sortedData?.filter(
      (notification) => !notification?.sticky,
    );
    return concat(stickyItems, nonStickyItems);
  };
  return (
    // eslint-disable-next-line react/jsx-no-useless-fragment
    <>
      {returnFormattedData()?.map((v, index) => (
        <NotificationItem
          key={v.id}
          notificationDetails={v}
          isLast={notificationsItems.length - 1 === index}
        />
      ))}
    </>
  );
}

interface NotificationsMenuProps {
  height: MantineStyleSystemProps['h'];
}
function NotificationsList(props: NotificationsMenuProps) {
  const { height } = props;
  const { t } = useTranslation();

  const { data, isLoading } = useQuery(getSettingQuery());

  const hasNotNotification = data?.notifications && data.notifications?.length === 0;

  return (
    <>
      <Flex h={30} px="md" mb="md" align="center" justify="space-between">
        <Group spacing="xs">
          <Text size="xs">{t('common:notification')}</Text>
        </Group>
      </Flex>
      {hasNotNotification && !isLoading && (
        <Stack justify="center" h={height} pr="md">
          <EmptyData message={t('noNotification')} />
        </Stack>
      )}
      {isLoading && (
        <Stack justify="center" h={height} pr="md">
          <NotificationItemSkeleton />
          <NotificationItemSkeleton />
          <NotificationItemSkeleton />
        </Stack>
      )}
      {!isLoading && !hasNotNotification && (
        <ScrollArea h={height} scrollbarSize={5}>
          <RenderNotifications notificationsItems={data?.notifications} />
        </ScrollArea>
      )}
    </>
  );
}

export default NotificationsList;
