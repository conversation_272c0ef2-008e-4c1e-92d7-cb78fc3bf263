/**
 * This App route component wrapped by providers component and handling localization and "ltr , rtl".
 */
import type { AppProps } from 'next/app';
import Head from 'next/head';
import { useRouter } from 'next/router';

import Providers from '@/components/common/providers/providers';
import { useEffect, useState } from 'react';
import { setCookie } from 'cookies-next';
import { LocaleCookie, defaultLocale } from '@/data';
import '../styles.css';

import { MaintenancePage } from '@/components/common/maintenance-page';
import { MetaPixel } from '@/components/meta-pixel';

function App(props: AppProps) {
  const { Component, pageProps } = props;
  const router = useRouter();
  const rtl = router.locale === 'ar';
  const [url, setUrl] = useState('');

  useEffect(() => {
    setCookie(LocaleCookie, router.locale || defaultLocale);
    if (typeof window !== 'undefined') setUrl(window.location.origin);
  }, [router.locale]);

  if (
    process.env.NEXT_PUBLIC_MAINTENANCE_MODE === 'true'
    && (url === 'https://www.kazawallet.com' || url === 'https://kazawallet.com')
  ) {
    // Render the maintenance page instead of the regular component
    return (
      <Providers>
        <MaintenancePage />
      </Providers>
    );
  }
  return (
    <>
      <Head>
        <meta
          name="viewport"
          content="minimum-scale=1, initial-scale=1, width=device-width"
        />
        <meta
          name="ccpayment-site-verification"
          content="0b823d8b45ee3eaf26e538b6cff6f451"
        />
      </Head>
      <MetaPixel />

      <Providers>
        <Component {...pageProps} rtl={rtl} />
      </Providers>
    </>
  );
}

export default App;
