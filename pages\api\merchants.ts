import { NOTION_DATABASE_ID } from '@/data';
import { notionQuery } from '@/lib';
import { merchantsApiResponseSchema } from '@/store/merchant/response-transformer';

export async function getMerchants(locale: string) {
  try {
    const response = await notionQuery({
      database: NOTION_DATABASE_ID.merchants,
    });

    return merchantsApiResponseSchema.parse({ ...response, locale });
  } catch {
    return [];
  }
}
