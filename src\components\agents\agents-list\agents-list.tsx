import { getSettingQuery, SettingApiResponse } from '@/store/setting';
import {
  Accordion,
  Box,
  Group,
  Loader,
  rem,
  Stack,
  Text,
  TextInput,
} from '@mantine/core';

import { useQuery } from '@tanstack/react-query';
import useTranslation from 'next-translate/useTranslation';
import React, { useState } from 'react';
import AgentItem from './agent-item';
import { IconSearch } from '@tabler/icons-react';
import { EmptyData } from '@/components/common/empty-data';
import { Icon } from '@/components/common/icon';

interface AgentsListProps {
  currencyCode: string | undefined;
  viewAs: 'accordion' | 'list';
  withDescription: boolean;
}
function AgentsList({
  currencyCode,
  viewAs,
  withDescription,
}: AgentsListProps) {
  const { t } = useTranslation();

  const [searchValue, setSearchValue] = useState('');

  const onSearchValueChange = (v: React.ChangeEvent<HTMLInputElement>) => setSearchValue(v?.currentTarget.value);

  const { data, isLoading } = useQuery(getSettingQuery());

  const agentsList: SettingApiResponse['agents'] = currencyCode
    ? data?.agents?.filter(
      (agent: SettingApiResponse['agents'][0]) => agent.currency === currencyCode,
    )
    : data?.agents;

  const returnSearchableData = () => {
    let list = agentsList;
    const search = searchValue?.trim().toLocaleLowerCase() ?? '';
    if (search) {
      list = agentsList.filter(
        (item) => item.name.toLocaleLowerCase().includes(search)
          || item.city.toLocaleLowerCase().includes(search)
          || item.region.toLocaleLowerCase().includes(search)
          || (item?.accountId
            && item.accountId.toLocaleLowerCase().includes(search)),
      );
    } else list = agentsList;
    return list;
  };

  const renderAgents = () => (
    <>
      <TextInput
        onChange={onSearchValueChange}
        value={searchValue}
        my="sm"
        radius="lg"
        placeholder={t('common:search')}
        rightSection={<IconSearch color="gray" size={15} />}
        miw="100%"
      />
      {isLoading ? (
        <Stack justify="center" align="center" mih={200}>
          <Loader />
        </Stack>
      ) : (
        <Stack mt="xs">
          {returnSearchableData()?.map((agent) => (
            <AgentItem
              withDescription={withDescription}
              agent={agent}
              key={agent?.id}
            />
          ))}
        </Stack>
      )}
      {!isLoading && returnSearchableData().length === 0 && (
        <Stack mih={100} justify="end">
          <EmptyData message="" />
        </Stack>
      )}
    </>
  );

  return (
    <Box my="md">
      {viewAs === 'list' ? (
        <>
          <Group spacing={10}>
            <Icon icon="users" size={rem(20)} color="gray" />
            <Text weight={500}>{t('common:agents')}</Text>
          </Group>
          {renderAgents()}
        </>
      ) : (
        <Accordion radius="lg" variant="contained">
          <Accordion.Item value="description">
            <Accordion.Control
              icon={<Icon icon="users" size={rem(20)} color="gray" />}
            >
              <Text weight={500}>{t('common:agents')}</Text>
            </Accordion.Control>
            <Accordion.Panel>{renderAgents()}</Accordion.Panel>
          </Accordion.Item>
        </Accordion>
      )}
    </Box>
  );
}

export default AgentsList;
