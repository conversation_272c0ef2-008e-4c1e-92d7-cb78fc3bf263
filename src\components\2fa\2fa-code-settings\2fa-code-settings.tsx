import { generate2faTokenMutation, get2faTokenQuery } from '@/store/2fa/calls';
import { UserApiResponse, updateUserMutation } from '@/store/user';
import {
  ActionIcon,
  Button,
  Center,
  CopyButton,
  Group,
  Image,
  Loader,
  Stack,
  Text,
  TextInput,
  Title,
  Tooltip,
} from '@mantine/core';
import { IconCheck, IconCopy } from '@tabler/icons-react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { getCookie, setCookie } from 'cookies-next';
import useTranslation from 'next-translate/useTranslation';
import React, { useEffect, useState } from 'react';

import { sentEmailMutation } from '@/store/email';
import ActivateSwitch from './activate-switch';
import { notifications } from '@mantine/notifications';
import { CodeConfirmModal } from '../code-confirm-modal';
import { Icon } from '@/components/common/icon';
import { useRouter } from 'next/router';
import { preserveUserData } from '@/utils/profile-utils';

interface Props {
  data: UserApiResponse | undefined;
}
function Code2faSettings({ data }: Props) {
  const { t } = useTranslation();
  const { query } = useRouter();
  // states
  const [codeError, setCodeError] = useState('');
  const [code, setCode] = useState('');
  const [openedCode, setOpenedCode] = useState(false);
  const [type, setType] = useState('');
  const isEnabled = getCookie('2fa-enabled');
  const [codeData, setCodeData] = useState<{
    token: string;
    qr: string;
  }>();

  const queryClient = useQueryClient();

  // reset code data if tab change
  useEffect(() => {
    setCodeData(undefined);
  }, [query?.page]);

  // update user mutation
  const { mutate } = useMutation({
    ...updateUserMutation(),
    onSuccess(res) {
      queryClient.invalidateQueries(['user']);
      setCookie('2fa-enabled', !!res?.authenticatorEnabled);
      notifications.show({
        message: t('common:addAuthenticatorSettingsSuccess'),
        color: 'blue',
      });
    },
  });
  //   ******************************************************************
  // mutation to get qr code and token authenticator
  const getCodeData = useMutation({
    ...get2faTokenQuery(),
    onSuccess(res: { token: string; qr: string }) {
      setCode('');
      setOpenedCode(false);
      setCodeError('');
      setCodeData(res);
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    onError(error: any) {
      if (error?.message?.key === 'invalidCode') {
        setCodeError('invalidCode');
        setCode('');
      } else setCodeError('');
    },
  });
  // generate new qr code and token authenticator
  const { isLoading: generateNewCodeLoading, mutate: generateNewCode } = useMutation({
    ...generate2faTokenMutation(),
    onSuccess(resData) {
      setOpenedCode(false);
      setCode('');
      setCodeError('');
      setCodeData(resData);
      mutate(preserveUserData({
        authenticatorToken: resData?.token,
        authenticatorEnabled: true,
      }, data));
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    onError(error: any) {
      if (error?.message?.key === 'invalidCode') {
        setCodeError('invalidCode');
        setCode('');
      } else setCodeError('');
    },
  });
  // send email mutation
  const sentEmail = useMutation({
    ...sentEmailMutation(),
    onSuccess: () => {
      setType('generate');
      setOpenedCode(true);
    },
  });
  //   ******************************************************************
  // generate or get qr when code submitting
  const onCodeSubmit = (v: string) => {
    if (type === 'generate') generateNewCode(v);
    else getCodeData.mutate(v);
  };

  // on click show code button
  const onShowCodeBtnClick = () => {
    setType('get');
    setOpenedCode(true);
  };
  // on Generate code button click will mutate send email api
  const generateCodeBtnClick = () => {
    sentEmail.mutate({
      body: {
        to: `${data?.email}`,
        subject: 'Kazawallet Authenticator',
        text: '',
      },
    });
  };
  const confirmModalTitle = type === 'generate' ? t('common:enterCodeSent') : t('common:enterCode');

  return (
    <>
      <ActivateSwitch
        data={data}
        initialLoading={sentEmail.isLoading}
        firstEnableAuthCallbackFn={generateCodeBtnClick}
        onDisableAuthCallbackFn={() => setCodeData(undefined)}
      />

      {isEnabled && data?.hasAuthenticatorToken && (
        <Group position="apart" my="md">
          <Button
            color="secondary.7"
            radius="lg"
            onClick={onShowCodeBtnClick}
            fullWidth
          >
            <Text mx={3}>{t('common:showQrCode')}</Text>
            <Icon icon="eye" size="22" color="dark" />
          </Button>
          <Button
            loading={sentEmail.isLoading}
            radius="lg"
            onClick={generateCodeBtnClick}
            fullWidth
          >
            {t('common:generateCode')}
            <Icon icon="plus" size="22" color="dark" />
          </Button>
        </Group>
      )}

      <Stack>
        {(generateNewCodeLoading || getCodeData.isLoading) && (
          <Stack align="center" justify="center" mih={300}>
            <Loader />
          </Stack>
        )}
        {(codeData && isEnabled) && (
          <Stack>
            <Title order={5} color="dimmed">
              {t('common:scanQrCode')}
            </Title>
            <Center>
              <Image
                src={codeData?.qr}
                alt="2fa-Qr-code"
                width={200}
                height={200}
              />
            </Center>
            <TextInput
              label={t('common:authToken')}
              readOnly
              miw={250}
              radius="lg"
              value={codeData?.token}
              rightSection={(
                <CopyButton value={codeData?.token} timeout={2000}>
                  {({ copied, copy }) => (
                    <Tooltip
                      label={copied ? t('common:copied') : t('common:copy')}
                      withArrow
                      position="right"
                    >
                      <ActionIcon
                        color={copied ? 'teal' : 'gray'}
                        onClick={copy}
                        size="sm"
                      >
                        {copied ? <IconCheck /> : <IconCopy />}
                      </ActionIcon>
                    </Tooltip>
                  )}
                </CopyButton>
              )}
            />
          </Stack>
        )}
      </Stack>

      <CodeConfirmModal
        title={confirmModalTitle}
        codeError={codeError}
        btnText={t('common:confirm')}
        setOpenedCode={setOpenedCode}
        isLoading={getCodeData.isLoading || generateNewCodeLoading}
        onClick={(v) => onCodeSubmit(v)}
        openedDefault={openedCode}
        code={code}
        setCode={setCode}
        closeable={false}
        additionalContent={undefined}
      />
    </>
  );
}

export default Code2faSettings;
