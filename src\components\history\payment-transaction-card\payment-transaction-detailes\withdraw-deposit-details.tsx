import { AdminMessageAlert } from '@/components/admin-message-alert';
import { TranslatedTextValue } from '@/components/common/translated-text-value';
import { Item } from '@/components/pop-up-success/pop-up-item';
import { PaymentsApiResponse } from '@/store/payment';
import { TransfersApiResponse } from '@/store/transfer/types';

import { Text } from '@mantine/core';
import currency from 'currency.js';
import React from 'react';

interface Props {
  amount: number | string;
  actualAmount: PaymentsApiResponse['data'][0]['actualAmount'] | string;
  currencyFrom:
    | PaymentsApiResponse['data'][0]['currency']
    | TransfersApiResponse['data'][0]['currencyFrom']
    | undefined;
  paymentMethod: PaymentsApiResponse['data'][0]['paymentMethod'] | undefined;
  operationType:
    | 'deposit'
    | 'transfer'
    | 'withdraw'
    | 'exchange'
    | 'received'
    | string;
  fees: number | undefined;
  adminMessage: string | null;
  originPaymentAmount:
    | PaymentsApiResponse['data'][0]['originPaymentAmount']
    | undefined;
}
function WithdrawDepositDetails({
  actualAmount,
  paymentMethod,
  operationType,
  amount,
  currencyFrom,
  fees,
  adminMessage,
  originPaymentAmount,
}: Props) {
  const isDepositOperation = operationType === 'deposit';
  const isWithdrawOperation = operationType === 'withdraw';
  // return precision if founded or return 2 as default
  const precision = currencyFrom?.precision ?? 2;
  // return origin amount after format it with precision
  const originPaymentAmountValue = currency(originPaymentAmount ?? 0, {
    symbol: '',
    precision,
  }).format();
  // return color by type payment
  const returnColor = isWithdrawOperation ? 'red' : 'green';
  return (
    <>
      <Item
        align="center"
        name={isWithdrawOperation ? 'toBePaid' : 'thePrice'}
        content={(
          <div
            style={{
              display: 'flex',
              gap: 5,
              color: returnColor,
            }}
          >
            <Text weight={500} span>
              {currency(amount, {
                symbol: '',
                precision,
              }).format()}
            </Text>
            <Text weight={500} span>
              {currencyFrom?.symbol}
            </Text>
          </div>
        )}
      />
      {originPaymentAmount && (
        <Item
          align="center"
          name="originAmount"
          content={(
            <div
              style={{
                display: 'flex',
                gap: 5,
                color: returnColor,
              }}
            >
              <Text weight={500} span>
                {originPaymentAmountValue}
              </Text>
              <Text weight={500} span>
                {currencyFrom?.symbol}
              </Text>
            </div>
          )}
        />
      )}
      <Item
        align="center"
        name="fees"
        content={(
          <div
            style={{
              display: 'flex',
              gap: 5,
            }}
          >
            <Text weight={500}>
              {currency(`${fees}`, {
                symbol: '',
                precision,
              }).format()}
            </Text>
            <Text weight={500} span>
              {currencyFrom?.symbol}
            </Text>
          </div>
        )}
      />
      <Item
        align="center"
        name={isDepositOperation ? 'toBeDeposit' : 'toBeReceived'}
        content={(
          <div
            style={{
              display: 'flex',
              gap: 5,
              color: returnColor,
            }}
          >
            <Text weight={500} span>
              {currency(`${actualAmount}`, {
                symbol: '',
                precision,
              }).format()}
            </Text>
            <Text weight={500} span>
              {currencyFrom?.symbol}
            </Text>
          </div>
        )}
      />
      {paymentMethod && (
        <Item
          align="center"
          name="paymentMethod"
          content={(
            <Text>
              <TranslatedTextValue
                keyEn={paymentMethod?.label}
                keyAr={paymentMethod?.labelAr}
              />
            </Text>
          )}
        />
      )}
      {adminMessage && <AdminMessageAlert adminMessage={adminMessage} />}
    </>
  );
}

export default WithdrawDepositDetails;
