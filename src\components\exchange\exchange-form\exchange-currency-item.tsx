import { UserApiResponse } from '@/store/user';
import {
  Group, Image, NumberInput, Paper, Text,
} from '@mantine/core';
import React from 'react';
import MinMaxBalance from '../min-max-balance';
import useTranslation from 'next-translate/useTranslation';
import { numberInputFormatter } from '@/utils';
import { SelectCurrencyCard } from '@/components/currency/select-currency-card';
import { TranslatedTextValue } from '@/components/common/translated-text-value';

interface Props {
  currencyDataArray: UserApiResponse['currencies'];
  setSelectedItem: (v: UserApiResponse['currencies'][0]) => void;
  currencyItem: UserApiResponse['currencies'][0] | undefined;
  inputValue: number | '' | undefined;
  onChangeInputValue: (v: number | '') => void;
  isMinMaxError: boolean;
  inputType: 'get' | 'give';
}

function ExchangeCurrencyItem({
  currencyDataArray,
  setSelectedItem,
  currencyItem,
  inputValue,
  onChangeInputValue,
  isMinMaxError,
  inputType,
}: Props) {
  const { t } = useTranslation();

  return (
    <>
      <Paper withBorder px="md" radius={23}>
        <SelectCurrencyCard
          type="popup"
          data={currencyDataArray ?? []}
          setSelectedItem={setSelectedItem}
          selectedItem={currencyItem}
        >
          {!currencyItem ? (
            <Group align="center" h="100%">
              <Text color="dimmed" weight={500} tt="capitalize">
                {t(
                  `common:${
                    inputType === 'give'
                      ? 'selectCurrencyGive'
                      : 'selectCurrencyGet'
                  }`,
                )}
              </Text>
            </Group>
          ) : (
            <Group h="100%">
              <Image
                alt="currency"
                src={currencyItem?.image}
                width={35}
                height={35}
                radius={50}
              />
              <div>
                <Text weight={500} size="lg" color="dimmed" tt="capitalize">
                  <TranslatedTextValue keyEn={currencyItem?.label} keyAr={currencyItem?.labelAr} />
                </Text>
              </div>
            </Group>
          )}
        </SelectCurrencyCard>
      </Paper>
      <Paper withBorder p="sm" radius="lg">
        <MinMaxBalance
          hiddenMinMax={inputType === 'get'}
          currencyItem={currencyItem}
        />
        <NumberInput
          precision={currencyItem?.precision ?? 2}
          key={`${currencyItem?.code}-${currencyItem?.precision ?? 2}`}
          radius="lg"
          size="sm"
          label={t(`common:${inputType === 'give' ? 'give' : 'get'}`)}
          hideControls
          value={inputValue}
          onChange={onChangeInputValue}
          formatter={numberInputFormatter}
          step={0}
        />
        {isMinMaxError && inputType === 'give' && (
          <Text mx={2} color="red" size="sm">
            {t('common:minMaxError')}
          </Text>
        )}
      </Paper>
    </>
  );
}

export default ExchangeCurrencyItem;
