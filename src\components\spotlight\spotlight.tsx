import { ROUTES } from '@/data';
import { useLastSearch } from '@/store/search/store';
import { getUserQuery } from '@/store/user';
import { SpotlightProvider, spotlight } from '@mantine/spotlight';
import { useQuery } from '@tanstack/react-query';
import currency from 'currency.js';
import useTranslation from 'next-translate/useTranslation';
import { useRouter } from 'next/router';
import { useState } from 'react';

import { operationIcon } from '@/utils/operations-utils/operation-icons';
import { CustomAction } from './custom-action-item';
import { getPaymentsQuery } from '@/store/payment';
import { getTransfersQuery } from '@/store/transfer';
import { useDebouncedState } from '@mantine/hooks';
import dayjs from 'dayjs';
import { ActionsWrapper } from './actions-wrapper';
import { getTranslatedTextValue } from '@/utils';

type Action = {
  title: string;
  href: string;
  icon: string;
};
// pages actions
const actions: Action[] = [
  {
    title: ROUTES.deposit.key,
    href: ROUTES.deposit.path,
    icon: operationIcon({ operationType: 'deposit' }),
  },
  {
    title: ROUTES.withdraw.key,
    href: ROUTES.withdraw.path,
    icon: operationIcon({ operationType: 'withdraw' }),
  },
  {
    title: ROUTES.transfer.key,
    href: ROUTES.transfer.path,
    icon: operationIcon({ operationType: 'transfer' }),
  },
  {
    title: ROUTES.exchange.key,
    href: ROUTES.exchange.path,
    icon: operationIcon({ operationType: 'exchange' }),
  },
  {
    title: ROUTES.massPayout.key,
    href: ROUTES.massPayout.path,
    icon: operationIcon({ operationType: 'massPayout' }),
  },
  {
    title: 'profile',
    href: ROUTES.myAccount.path,
    icon: 'user-circle',
  },
  {
    title: ROUTES.rates.key,
    href: ROUTES.rates.path,
    icon: 'businessplan',
  },
  {
    title: ROUTES.history.key,
    href: ROUTES.history.path,
    icon: 'history',
  },
];

function Spotlight() {
  const { t } = useTranslation();
  const { replace, locale } = useRouter();
  const [search, setSearch] = useState('');
  const [searchValue, setSearchValue] = useDebouncedState<string | null>(
    null,
    200,
  );
  // search stor ----------------------------------
  const searchResults = useLastSearch((state) => state.searchResult);
  const setSearchResults = useLastSearch((state) => state.setSearchResult);

  // data fetching ---------------------------------
  const { data } = useQuery({
    ...getUserQuery({
      loggedIn: true,
    }),
    enabled: !!searchValue,
  });

  const paymentsDataQuery = useQuery({
    ...getPaymentsQuery({
      pagination: {
        pageSize: 5,
      },
      filters: {
        search: searchValue ? searchValue?.trim() : null,
      },
    }),
    enabled: !!searchValue,
  });
  const transfersDataQuery = useQuery({
    ...getTransfersQuery({
      pagination: {
        pageSize: 5,
      },
      filters: {
        search: searchValue ? searchValue?.trim() : null,
      },
    }),
    enabled: !!searchValue,
  });
  // -------------------------------------------------
  const isLoading = paymentsDataQuery.isLoading || transfersDataQuery.isLoading;
  //  render spotlight actions (actions,wallets)------
  const renderSpotlightActions = () => {
    // return pages actions
    const operationsActions = actions.map((item) => ({
      id: item.title,
      title: t(`common:${item.title}`),
      group: 'actions',
      icon: item.icon,
      onTrigger: () => {
        spotlight.close();
        replace(item.href);
        setSearchResults([
          {
            id: item.title,
            title: t(`common:${item.title}`),
            href: item.href,
            icon: item.icon,
          },
          ...searchResults,
        ]);
      },
    }));
    // return user wallets
    const walletsAction = data?.currencies?.map((curr) => ({
      id: curr?.uid,
      title: getTranslatedTextValue(locale, curr?.label, curr?.labelAr),
      description: `${currency(curr?.amount, {
        symbol: '',
        precision: curr?.precision ?? 2,
      }).format()} ${curr?.symbol ?? ''} `,
      group: 'wallets',
      image: curr?.image,
      onTrigger: () => {
        spotlight.close();
        replace(`${ROUTES.wallets.path}/${curr?.uid}`);
        setSearchResults([
          {
            id: curr?.uid,
            title: getTranslatedTextValue(locale, curr?.label, curr?.labelAr),
            href: `${ROUTES.wallets.path}/${curr?.uid}`,
            image: curr?.image,
          },
          ...searchResults,
        ]);
      },
    }));

    if (walletsAction && walletsAction?.length > 0) {
      return [...operationsActions, ...walletsAction];
    }
    return [...operationsActions];
  };
  // ----------------------------------------------------
  //  render spotlight actions (transfers,payments)------
  const renderHistoryActions = () => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let history: any[] = [];
    if (transfersDataQuery?.data?.data && paymentsDataQuery?.data?.data) {
      const allData = [
        ...transfersDataQuery.data.data,
        ...paymentsDataQuery.data.data,
      ];
      const sortedData = allData
        ? allData.sort((b, a) => +dayjs(a.createdAt) - +dayjs(b.createdAt))
        : null;
      history = sortedData
        ? sortedData.map((historyItem) => ({
          id: historyItem?.transactionId,
          title: historyItem?.transactionId,
          group: 'history',
          data: { ...historyItem, userAccountId: data?.accountId },
          onTrigger: () => {},
        }))
        : [];
      return history;
    }
    return history;
  };
  // ----------------------------------------------------
  //  render spotlight  last search actions (actions,wallets)------
  const renderLastSearchResults = () => searchResults.map((result) => ({
    id: result?.id,
    title: t(result.title),
    description: t(result.description ?? ''),
    // group: "lastSearch",
    image: result.image,
    icon: result.icon,
    data: result.data,
    onTrigger: () => {
      spotlight.close();
      replace(result?.href ?? '');
    },
  }));
  // ----------------------------------------------------
  // render final actions ----------------------
  const renderFinalActions = () => {
    if (renderLastSearchResults()?.length > 0 && search === '') {
      return renderLastSearchResults();
    }
    let finalActions = renderSpotlightActions();
    if (renderHistoryActions().length > 0) {
      finalActions = [...renderSpotlightActions(), ...renderHistoryActions()];
    }
    return search ? finalActions : [];
  };
  // ----------------------------------------------------
  return (
    <SpotlightProvider
      zIndex={99}
      limit={50}
      actions={renderFinalActions()}
      actionComponent={CustomAction}
      searchPlaceholder={t('common:spotlightPlaceholder')}
      shortcut="mod + k"
      nothingFoundMessage={isLoading ? '' : t('common:noThingToShow')}
      onQueryChange={(e) => {
        setSearch(e);
        setSearchValue(e);
      }}
      // eslint-disable-next-line  react/no-unstable-nested-components
      actionsWrapperComponent={ActionsWrapper({
        isLoading,
        withClearHistoryBtn: renderLastSearchResults().length > 0,
        showTitle: !search,
      })}
      closeOnActionTrigger={false}
    >
      <div />
    </SpotlightProvider>
  );
}
export default Spotlight;
