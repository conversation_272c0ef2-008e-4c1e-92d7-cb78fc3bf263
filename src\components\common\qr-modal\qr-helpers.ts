import { BrowserQRCodeReader } from '@zxing/browser';
import { QR_CONFIG } from './qr-constants';

export interface DetectedBarcode {
  rawValue: string;
}

/**
 * Decode a QR code from a user‑supplied image file.
 */
export const processImageForQR = async (
  file: File,
  onSuccess: (codes: DetectedBarcode[]) => void,
  onError: (error: Error) => void,
): Promise<void> => {
  // –– basic validation
  if (!file.type.startsWith('image/')) {
    onError(new Error('invalidImageFile'));
    return;
  }
  if (file.size === 0) {
    onError(new Error('invalidImageFile'));
    return;
  }
  if (file.size > QR_CONFIG.MAX_FILE_SIZE) {
    onError(new Error('file-too-large'));
    return;
  }

  // decode
  const objectUrl = URL.createObjectURL(file);
  const reader = new BrowserQRCodeReader();

  try {
    const result = await reader.decodeFromImageUrl(objectUrl);
    const payload = result?.getText?.();

    if (payload) {
      onSuccess([{ rawValue: payload }]);
    } else {
      onError(new Error('noQrCodeFoundInImage'));
    }
  } catch {
    onError(new Error('failedToProcessImage'));
  } finally {
    URL.revokeObjectURL(objectUrl);
  }
};

export const shareQRCode = async (
  accountId: string,
  qrUrl: string,
): Promise<void> => {
  if (navigator.share && accountId) {
    try {
      await navigator.share({
        title: 'Kazawallet Account',
        text: `Account ID: ${accountId}`,
        url: qrUrl,
      });
    } catch {
      navigator.clipboard?.writeText(qrUrl);
    }
  } else if (navigator.clipboard) {
    navigator.clipboard.writeText(qrUrl);
  }
};
