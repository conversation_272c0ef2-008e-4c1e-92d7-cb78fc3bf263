.wrapper {
  position: relative;
  padding-top: 100px;
  padding-bottom: 80px;
}

.inner {
  position: relative;
  z-index: 1;
}

.dots {
  position: absolute;
  color: var(--mantine-color-gray-1);
}

.dotsLight {
  position: absolute;
  color: var(--mantine-color-dark-5);
}

.dotsLeft {
  left: 0;
  top: 0;
}

.title {
  text-align: center;
  font-weight: 800;
  font-size: 40p;
  letter-spacing: -1px;
  color: var(--mantine-color-white);
  margin-bottom: var(--mantine-spacing-xs);
}
.titleLightColor {
  color: var(--mantine-color-black);
}

.description {
  text-align: center;
  margin-top: 14px;
}

.controls {
  margin-top: var(--mantine-spacing-lg);
  display: flex;
  justify-content: center;
  gap: 20px;
}
