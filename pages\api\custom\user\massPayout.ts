/**
 * This handler to create mass payout transfer.
 */
import { apiEndpoints, httpCode, playCaptcha } from '@/data';
import { BackendClient } from '@/lib';
import {
  createMassPayoutBackendRequestSchema,
  massPayoutApiResponseSchema,
} from '@/store/mass-payout';

import { verifyCode } from '@/utils/2fa/verify-code';
import createApiError from '@/utils/api-utils/create-api-error';
import createApiResponse from '@/utils/api-utils/create-api-response';
import { getJwt } from '@/utils/api-utils/jwt';
import { captchaValidator } from '@/utils/captcha-validator';
import { NextApiRequest, NextApiResponse } from 'next';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token } = await getJwt(req);
  if (req.method === 'POST') {
    try {
      // get token "cha<PERSON><PERSON>" returned from captha and pass it to "captchaValida<PERSON>" to check toke validity
      // if validation success will cal api,else return error.
      const chaKey = req?.query?.chaKey;
      await captchaValidator({
        token: chaKey as string,
        secret: process.env.RECAPTCHA_SECRET_KEY as string,
        active: playCaptcha,
      });
      /**
       * If user enabled 2FA, then he should add pin code get it from google authenticator app to complete mass payout operation
       * If there is a qrCode, then will verify code validity and complete operation if return true
       */
      const qrCode = req?.query?.qrCode;
      await verifyCode(qrCode as string, token, req);

      // create mass payout function
      const { data } = await BackendClient(req).post(
        apiEndpoints.massPayout(),
        createMassPayoutBackendRequestSchema.parse(req.body),
        {
          headers: {
            authorization: token,
          },
        },
      );
      return createApiResponse(res, massPayoutApiResponseSchema, { data });
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }
  const error = createApiError({ error: '' });
  return res.status(httpCode.INTERNAL_SERVER_ERROR).json(error);
}

export default handler;
