import { GlobalCard } from '@/components/common/global-card';
import { Group, Skeleton, Stack } from '@mantine/core';
import React from 'react';

function GiftCardItemSkeleton() {
  return (
    <GlobalCard props={{}} onClick={() => {}}>
      <Group align="center" spacing="xs" noWrap>
        <Skeleton circle height={40} width={40} />
        <Stack w="90%" spacing="xs">
          <Group position="apart">
            <Skeleton height={30} maw={250} />
            <Skeleton circle height={25} width={25} />
          </Group>
          <Group spacing="sm">
            <Skeleton height={20} maw={250} />
            <Skeleton height={20} maw={250} />
          </Group>
        </Stack>
      </Group>
    </GlobalCard>
  );
}

export default GiftCardItemSkeleton;
