/**
 * spotlight store to store las search results in local storage
 */
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface State {
  deferredPrompt: Event | null;
}
interface Actions {
  setDeferredPrompt: (event: Event | null) => void;
}
export const usePwa = create(
  persist<State & Actions>(
    (set) => ({
      deferredPrompt: null,
      setDeferredPrompt(value) {
        set(() => ({ deferredPrompt: value }));
      },
    }),
    { name: 'lastSearch' },
  ),
);
