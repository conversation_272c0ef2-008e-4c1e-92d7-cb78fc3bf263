import {
  Text,
  Container,
  ActionIcon,
  Group,
  Image,
  Anchor,
} from '@mantine/core';
import { useStyles } from './style';

import useTranslation from 'next-translate/useTranslation';
import { assetBaseUrl, websiteName } from '@/data';
import { Icon } from '@/components/common/icon';
import Link from 'next/link';
import { useRouter } from 'next/router';

interface FooterLinksProps {
  data: {
    title: string;
    links: { label: string; link: string, target?:string }[];
  }[];
}

export default function FooterLandingPage({ data }: FooterLinksProps) {
  const { classes } = useStyles();
  const { t } = useTranslation();
  const { locale } = useRouter();
  const groups = data.map((group) => {
    const links = group.links.map((link) => (link?.label !== 'supportEmail' ? (
      <Text
        key={link.label}
        className={classes.link}
        component={Link}
        href={link.link}
        target={link?.target}
      >
        {t(`common:${link.label}`)}
      </Text>
    ) : (
      <Anchor type="email" key={link.label} className={classes.link} href={`mailto:${link.link}`}>
        {t(`common:${link.label}`)}
      </Anchor>
    )));
    return (
      <div className={classes.wrapper} key={group.title}>
        <Text className={classes.title}>{t(`common:${group.title}`)}</Text>
        {links}
      </div>
    );
  });
  return (
    <footer className={classes.footer}>
      <Container
        px={{
          xs: 20,
          sm: 30,
          lg: 40,
          base: 20,
        }}
        size={1200}
        className={classes.inner}
      >
        <div className={classes.logo}>
          <Group>
            <Image
              alt="kaazawallet-logo"
              width={50}
              height={50}
              src={`${assetBaseUrl}/assets/logo/logo-landing.png`}
            />
            <Text size="xl" weight="bold" color="white">
              {t(`common:${websiteName}`)}
            </Text>
          </Group>
          <Group spacing={0} className={classes.social} position="right" noWrap>
            <ActionIcon
              component={Link}
              href="https://www.facebook.com/kazawallet"
              target="_blank"
              size="lg"
              aria-label="facebook"
            >
              <Icon icon="brand-facebook" size="1.5rem" color="dark" />
            </ActionIcon>
            <ActionIcon
              component={Link}
              href="https://www.youtube.com/@Kazawallet"
              target="_blank"
              size="lg"
              aria-label="youtube"
            >
              <Icon icon="brand-youtube" size="1.5rem" color="dark" />
            </ActionIcon>
            <ActionIcon
              component={Link}
              href="https://t.me/Kazawallet"
              target="_blank"
              size="lg"
              aria-label="telegram"
            >
              <Icon icon="brand-telegram" size="1.5rem" color="dark" />
            </ActionIcon>
          </Group>
        </div>
        {groups}
      </Container>

      <Container size={1400} mt="xl" px="md">
        <Text
          align="center"
          size="sm"
          color="dimmed"
          px={{ base: 'xs', sm: 'md', lg: 'xl' }}
          sx={{
            lineHeight: 1.6,
            maxWidth: '1200px',
            margin: '0 auto',
            direction: locale === 'ar' ? 'rtl' : 'ltr',
          }}
        >
          {t('common:kazawalletDisclaimer')}
        </Text>
      </Container>
    </footer>
  );
}
