import {
  Group, Text, Select, Image, Stack, Box,
} from '@mantine/core';
import { ReactNode, forwardRef } from 'react';

import { useStyles } from './style';
import { PaymentMethodsApiResponse } from '@/store/payment-methods';
import useTranslation from 'next-translate/useTranslation';
import { Icon } from '@/components/common/icon';
import { CurrenciesApiResponse } from '@/store/currencies';
import { CurrenciesOrPaymentMethodsPopup } from '@/components/currencies-payment-methods-popup';
import { Fees } from '@/components/currencies-payment-methods-popup/item';
import { getTranslatedTextValue } from '@/utils';
import { useRouter } from 'next/router';

interface ItemProps extends React.ComponentPropsWithoutRef<'div'> {
  image: string;
  label: string;
  currencySymbol: string;
  fees: {
    percent: number;
    fixed: number;
  };
}

const SelectItem = forwardRef<HTMLDivElement, ItemProps>(
  ({
    image, label, fees, currencySymbol, ...others
  }: ItemProps, ref) => (
    <div style={{ borderRadius: 20 }} ref={ref} {...others}>
      <Group noWrap>
        <Image
          src={image}
          height={35}
          width={35}
          radius={50}
          alt="payment-method"
        />
        <Stack spacing={1}>
          <Text weight={500} tt="capitalize" size="sm">
            {label}
          </Text>
          <Fees fees={fees} symbol={currencySymbol} />
        </Stack>
      </Group>
    </div>
  ),
);

interface SelectCardProps {
  setSelectedItem: (
    v: PaymentMethodsApiResponse['data'][0] | undefined
  ) => void;
  children: ReactNode;
  data: PaymentMethodsApiResponse['data'];
  setLoading: (v: boolean) => void;
  selectedItem: PaymentMethodsApiResponse['data'][0] | undefined;
  currencySymbol: CurrenciesApiResponse['data'][0]['symbol'] | undefined;
  paymentType: 'withdraw' | 'deposit';
  type: 'select' | 'popup';
}
export function SelectPaymentMethodCard({
  setSelectedItem,
  children,
  data,
  setLoading,
  selectedItem,
  currencySymbol,
  paymentType,
  type,
}: SelectCardProps) {
  const { classes } = useStyles();
  const { t } = useTranslation();
  const router = useRouter();
  const { locale } = router;
  const handleSelectPaymentMethod = (
    v: PaymentMethodsApiResponse['data'][0] | undefined,
  ) => {
    setSelectedItem(v);
    if (v) router.query.method = v.label;
    else delete router.query.method;
    router.push(router);
  };
  return (
    <div style={{ position: 'relative', height: 'auto' }}>
      {type === 'select' ? (
        <>
          <Select
            radius="xl"
            label=""
            placeholder=""
            itemComponent={SelectItem}
            data={data?.map((i) => ({
              label: getTranslatedTextValue(locale, i?.label, i?.labelAr),
              image: i?.image,
              value: i?.value,
              currencySymbol,
              fees: {
                percent:
                  paymentType === 'deposit'
                    ? i.depositFeesPercentage
                    : i.withdrawFeesPercentage,
                fixed:
                  paymentType === 'deposit'
                    ? i.depositFeesFixed
                    : i.withdrawFeesFixed,
              },
            }))}
            maxDropdownHeight={400}
            miw={{ base: 305, xs: 350 }}
            nothingFound={t('common:noPaymentMethodSupportThisCurrency')}
            onChange={(v) => {
              const item = data?.filter((i) => i.value === v);
              setLoading(true);
              setSelectedItem(undefined);
              setTimeout(() => {
                handleSelectPaymentMethod(item[0]);
                setLoading(false);
              }, 1000);
            }}
            classNames={classes}
            value={selectedItem?.value}
            transitionProps={{
              transition: 'fade',
              duration: 280,
              timingFunction: 'ease',
            }}
            rightSection={<div />}
          />
          <Group position="apart" align="center" noWrap>
            {children}
            <Box
              display="flex"
              sx={{ justifyContent: 'center', alignItems: 'center' }}
              w={25}
              h={25}
            >
              <Icon icon="selector" color="#79CA53" size={26} />
            </Box>
          </Group>
        </>
      ) : (
        <CurrenciesOrPaymentMethodsPopup
          dataType="paymentMethod"
          paymentType={paymentType}
          data={data}
          setSelectedItem={setSelectedItem}
          selectedItem={selectedItem}
          currencySymbol={currencySymbol}
          setLoading={setLoading}
        >
          {children}
        </CurrenciesOrPaymentMethodsPopup>
      )}
    </div>
  );
}
