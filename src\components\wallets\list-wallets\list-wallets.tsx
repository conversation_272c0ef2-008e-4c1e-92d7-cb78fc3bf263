import React from 'react';

import { useMediaQuery } from '@mantine/hooks';

import { SimpleGrid, Stack, Text } from '@mantine/core';
import { UserApiResponse } from '@/store/user';
import { WalletCardSkeleton } from '../wallet-card/wallet-card-skeleton';
import { WalletCardSmallSkeleton } from '../wallet-card-small/wallet-card-small-skeleton';
import { RatesApiResponse } from '@/store/rate';
import useTranslation from 'next-translate/useTranslation';
import Link from 'next/link';
import { ROUTES } from '@/data';
import { WalletCard } from '../wallet-card';
import { WalletCardSmall } from '../wallet-card-small';
import WalletsPopup from '../wallets-popup/wallets-popup';
import { eqBy, prop, unionWith } from 'ramda';

function RenderListWallets({
  data,
  ratesData,
  isMobileScreen,
}: {
  data: UserApiResponse['currencies'] | undefined;
  ratesData: RatesApiResponse;
  isMobileScreen: boolean;
}) {
  return (
    <>
      <WalletsPopup />
      <SimpleGrid
        my="md"
        breakpoints={[
          { maxWidth: 'lg', cols: 3, spacing: 'md' },
          { maxWidth: 'md', cols: 2, spacing: 'md' },
          { maxWidth: 'sm', cols: 2, spacing: 'sm' },
          { maxWidth: 'xs', cols: 1, spacing: 'sm' },
        ]}
        cols={3}
      >
        {data?.map((i) => (!isMobileScreen ? (
          <WalletCard
            precision={i?.precision ?? 2}
            uid={i?.uid}
            key={i?.value}
            title={i?.label}
            titleAr={i?.labelAr}
            icon={i?.image}
            value={+i.amount}
            balanceUsd={
                ratesData?.rates && ratesData?.rates[`${i.code}_FROM`]
                  ? (1 / ratesData.rates[`${i?.code}_FROM`]) * +i.amount
                  : 0
              }
            currencySign={i?.symbol}
          />
        ) : (
          <WalletCardSmall
            precision={i?.precision ?? 2}
            uid={i?.uid}
            withExchangeList={false}
            key={i?.value}
            title={i?.code}
            icon={i?.image}
            value={+i.amount}
            balanceUsd={
                ratesData?.rates && ratesData?.rates[`${i.code}_FROM`]
                  ? (1 / ratesData.rates[`${i?.code}_FROM`]) * +i.amount
                  : 0
              }
            currencySign={i?.label}
            currencySignAr={i?.labelAr}
          />
        )))}
      </SimpleGrid>
    </>
  );
}
export default function ListWallets({
  data: currencies,
  favoriteCurrencies,
  isLoading,
  isLoadingRates,
  ratesData,
}: {
  data: UserApiResponse['currencies'] | undefined;
  favoriteCurrencies: UserApiResponse['favoriteCurrencies'] | undefined;
  isLoading: boolean;
  ratesData: RatesApiResponse;
  isLoadingRates: boolean;
}) {
  const matches = useMediaQuery('(max-width: 35.9em)', true, {
    getInitialValueInEffect: false,
  });
  const { t } = useTranslation();
  // merge favorites currencies with user currencies
  const data = unionWith(
    eqBy(prop('id')),
    currencies ?? [],
    favoriteCurrencies ?? [],
  );
  if (isLoading || isLoadingRates) {
    return (
      <SimpleGrid
        mb="md"
        mt={58}
        breakpoints={[
          { maxWidth: 'lg', cols: 3, spacing: 'md' },
          { maxWidth: 'md', cols: 2, spacing: 'md' },
          { maxWidth: 'sm', cols: 2, spacing: 'sm' },
          { maxWidth: 'xs', cols: 1, spacing: 'sm' },
        ]}
        cols={3}
      >
        {!matches ? (
          <>
            <WalletCardSkeleton />
            <WalletCardSkeleton />
            <WalletCardSkeleton />
          </>
        ) : (
          <>
            <WalletCardSmallSkeleton />
            <WalletCardSmallSkeleton />
            <WalletCardSmallSkeleton />
          </>
        )}
      </SimpleGrid>
    );
  }
  if (data && data?.length > 0 && !isLoading) {
    return (
      <RenderListWallets
        isMobileScreen={matches}
        ratesData={ratesData}
        data={data}
      />
    );
  }

  return (
    <Stack mih={100} justify="center" align="center">
      <Link href={ROUTES.deposit.path} style={{ textDecoration: 'none' }}>
        <Text>{t('common:pleaseCreateDeposit')}</Text>
      </Link>
    </Stack>
  );
}
