import {
  ROUTES, apkDownloadLink, assetBaseUrl, googlePlayAppLink,
} from '@/data';
import styles from './styles.module.scss';
import {
  Box,
  Text,
  Container,
  SimpleGrid,
  Group,
  Stack,
  useMantineTheme,
  Button,
  Image,
} from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';
import Link from 'next/link';
import { useMediaQuery } from '@mantine/hooks';
import { signIn } from 'next-auth/react';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';

export default function HeroSection({ isAuth }: { isAuth: boolean }) {
  const { t } = useTranslation();
  const router = useRouter();
  const theme = useMantineTheme();
  const [loading, setLoading] = useState(false);
  // stop loader on diamond
  useEffect(
    () => () => {
      setLoading(false);
    },
    [],
  );

  // boolean return true if screen siz less than "48em"
  const matches = useMediaQuery('(max-width: 48em)', true, {
    getInitialValueInEffect: false,
  });
  // boolean return true if screen siz less than "26em"
  const matchesXs = useMediaQuery('(max-width: 26em)', true, {
    getInitialValueInEffect: false,
  });
  // handle image size with screen size
  const imageSize = () => {
    let width = 500;
    if (matchesXs) {
      width = 300;
    } else if (matches) {
      width = 400;
    }
    return width;
  };
  return (
    <Box className={styles.root}>
      <Container
        py={40}
        px={{
          xs: 20,
          sm: 30,
          lg: 40,
          base: 20,
        }}
        size={1200}
      >
        <SimpleGrid
          cols={2}
          breakpoints={[
            { maxWidth: 'md', cols: 2, spacing: 'md' },
            { maxWidth: 'sm', cols: 1, spacing: 'sm' },
            { maxWidth: 'xs', cols: 1, spacing: 'sm' },
          ]}
        >
          <Box className={styles.textSection}>
            <Text className={styles.p1}>
              <span>{t('common:heroLandingSubTitle')}</span>
            </Text>
            <Text
              color="white"
              size={50}
              weight="bolder"
              ff={router.locale === 'ar' ? theme.fontFamily : `BauhausC, ${theme.fontFamily}`}
            >
              {t('common:heroLandingTitle')}
            </Text>
            <Text my="xl" color="dimmed" size="lg" weight={500} maw={600}>
              {t('common:heroLandingDescription')}
            </Text>
            {isAuth ? (
              <Link
                href={ROUTES.wallets.path}
                style={{
                  textDecoration: 'none',
                  color: theme.colors.primary[7],
                }}
                className={styles.aHero}
              >
                {t('common:gotoWallets')}
              </Link>
            ) : (
              <Button
                fullWidth
                size="lg"
                loading={loading}
                onClick={() => {
                  setLoading(true);
                  signIn('keycloak', {
                    callbackUrl: ROUTES.wallets.path,
                  });
                }}
                style={{
                  textDecoration: 'none',
                  color: theme.colors.primary[7],
                  fontWeight: 'bold',
                }}
                className={styles.aHero}
              >
                {t('common:heroLandingButton')}
              </Button>
            )}
            <Text mt="lg" size="sm" color="dimmed">
              {t('common:heroLandingComingSoon')}
            </Text>
            <Group align="center" mt="md">
              <Link href={googlePlayAppLink} target="_blank">
                <Image width={136} src={`${assetBaseUrl}/assets/svg/googleplay.svg`} />
              </Link>
              <Link href={apkDownloadLink} target="_blank">
                <Image width={136} src={`${assetBaseUrl}/assets/new-landing/apk.png`} />
              </Link>
            </Group>
          </Box>

          <Stack
            align={matches ? 'center' : 'end'}
            sx={{
              position: 'relative',
            }}
            justify="center"
          >
            <Image
              alt="bg"
              width={imageSize()}
              sx={{ position: 'absolute' }}
              src={`${assetBaseUrl}/assets/new-landing/blur-hero.webp`}
              radius="xs"
            />
            <Image
              width={imageSize()}
              src={`${assetBaseUrl}/assets/new-landing/landing-hero.webp`}
              alt="phone"
            />
          </Stack>
        </SimpleGrid>
      </Container>
    </Box>
  );
}
