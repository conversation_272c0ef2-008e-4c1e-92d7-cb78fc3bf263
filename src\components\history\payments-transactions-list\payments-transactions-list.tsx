import { Stack } from '@mantine/core';
import PaymentTransactionCard from '../payment-transaction-card';
import { TransfersApiResponse } from '@/store/transfer/types';
import { PaymentsApiResponse } from '@/store/payment';
import dayjs from 'dayjs';

interface Props {
  dataTransfer: TransfersApiResponse['data'] | undefined;
  dataPayment: PaymentsApiResponse['data'] | undefined;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  mixData: any[] | null;
  userAccountId: string;
}
export function RenderPaymentsList({
  dataPayment,
  dataTransfer,
  mixData,
  userAccountId,
}: Props) {
  const sortedData = mixData
    ? mixData.sort((b, a) => +dayjs(a.createdAt) - +dayjs(b.createdAt))
    : null;
  // This logic is very important to handle transactions history and customize type of operation
  const renderOperationType = (
    transaction:
      | PaymentsApiResponse['data'][0]
      | TransfersApiResponse['data'][0],
  ) => {
    if (
      transaction.type === 'transfer'
      || transaction.type === 'payment_link'
      || transaction.type === 'gift_card'
    ) {
      if (
        transaction?.userTo
        && transaction?.userTo?.accountId === userAccountId
      ) {
        return 'received';
      }
      if (
        transaction?.userFrom
        && transaction?.userFrom?.accountId === userAccountId
      ) {
        return 'transfer';
      }
      return transaction.type;
    }
    return transaction.type;
  };
  return (
    <Stack mt="md" mx="auto" maw={800}>
      {sortedData
        && sortedData.map((i) => (
          <PaymentTransactionCard
            paymentMethod={i?.paymentMethod ?? undefined}
            currencyFrom={i?.currencyFrom ?? i?.currency}
            currencyTo={i?.currencyTo ?? undefined}
            note={i?.note ?? undefined}
            userFrom={i?.userFrom ?? i?.user}
            userTo={i?.userTo ?? undefined}
            key={i?.transactionId}
            id={i?.transactionId}
            amount={i?.amount}
            actualAmount={i?.actualAmount}
            date={i?.createdAt}
            status={i?.status ?? 'approved'}
            operationType={renderOperationType(i)}
            fees={i?.snapshot?.fees ?? i?.totalFees}
            fields={i?.fields ?? undefined}
            rate={i?.snapshot?.rate ?? 0}
            adminMessage={i?.adminMessage}
            actualType={i?.type}
            gif={i.gif}
            cryptoGetaway={i?.cryptoGetaway}
            originPaymentAmount={i?.originPaymentAmount}
          />
        ))}
      {dataTransfer
        && dataTransfer.map((i) => (
          <PaymentTransactionCard
            paymentMethod={undefined}
            currencyFrom={i?.currencyFrom}
            currencyTo={i?.currencyTo}
            userFrom={i?.userFrom}
            userTo={i?.userTo}
            note={i?.note ?? ''}
            key={i?.transactionId}
            id={i?.transactionId}
            amount={i?.amount}
            actualAmount={i?.actualAmount}
            date={i?.createdAt}
            status="approved"
            operationType={renderOperationType(i)}
            fees={i?.snapshot?.fees}
            fields={undefined}
            rate={i?.snapshot?.rate}
            adminMessage={null}
            actualType={i?.type}
            gif={i.gif}
            cryptoGetaway={undefined}
            originPaymentAmount={undefined}
          />
        ))}
      {dataPayment
        && dataPayment.map((i) => (
          <PaymentTransactionCard
            paymentMethod={i?.paymentMethod}
            currencyFrom={i?.currency}
            currencyTo={undefined}
            userFrom={i?.user}
            userTo={undefined}
            note={i?.note ?? ''}
            key={i?.transactionId}
            id={i?.transactionId}
            amount={i?.amount}
            actualAmount={i?.actualAmount}
            date={i?.createdAt}
            status={i?.status}
            operationType={i?.type}
            fees={i?.totalFees ?? 0}
            fields={i?.fields}
            rate={0}
            adminMessage={i?.adminMessage}
            actualType={i?.type}
            gif={null}
            cryptoGetaway={i?.cryptoGetaway}
            originPaymentAmount={i?.originPaymentAmount}
          />
        ))}
    </Stack>
  );
}
