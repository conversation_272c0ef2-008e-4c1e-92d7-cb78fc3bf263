/**
 * This handler to create feedback in notion database.
 */
import { httpCode, NOTION_DATABASE_ID } from '@/data';
import { notionMutation } from '@/lib';
import { createFeedbackBackendRequestSchema } from '@/store/feedback';
import createApiError from '@/utils/api-utils/create-api-error';
import { NextApiRequest, NextApiResponse } from 'next';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST') {
    try {
      const response = await notionMutation({
        database: NOTION_DATABASE_ID.feedBack,
        body: createFeedbackBackendRequestSchema.parse(req.body),
      });

      return res.status(httpCode.SUCCESS).json({
        message: 'Feedback sent successfully',
        date: (response as { created_time: string })?.created_time,
      });
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }
  const error = createApiError({ error: '' });
  return res.status(httpCode.INTERNAL_SERVER_ERROR).json(error);
}

export default handler;
