import { TOKEN_SECRET } from '@/data';
import { NextApiRequest } from 'next';
import { getToken } from 'next-auth/jwt';
// this function to decode JWT we got it from keycloak and get user email from it
export const getJwt = async (req: NextApiRequest) => {
  const jwt = await getToken({
    req,
    secret: TOKEN_SECRET,
  });
  const authorization = `Bearer ${jwt?.accessToken}`;

  if (jwt?.accessToken) {
    return {
      token: authorization as string,
      email: jwt?.email as string,
    };
  }
  return {
    token: `${authorization}string`,
    email: '',
  };
};
