/**
 * This handler to create merchant request in notion database with captcha.
 */
import { httpCode, NOTION_DATABASE_ID } from '@/data';
import { notionMutation } from '@/lib';
import { createMerchantRequestBackendRequestSchema } from '@/store/merchant';
import createApiError from '@/utils/api-utils/create-api-error';
import { captchaValidator } from '@/utils/captcha-validator';
import { NextApiRequest, NextApiResponse } from 'next';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token } = req.body;
  if (req.method === 'POST') {
    try {
      // get token returned from captcha and pass it to "captchaValidator" to check toke validity
      // if validation success will call create merchant form ,else return error.
      await captchaValidator({
        token,
        secret: process.env.RECAPTCHA_SECRET_KEY as string,
      });
      // api to create merchant request

      const response = await notionMutation({
        database: NOTION_DATABASE_ID.merchant,
        body: createMerchantRequestBackendRequestSchema.parse(req.body),
      });

      return res.status(httpCode.SUCCESS).json({
        message: 'Merchant request sent successfully',
        date: (response as { created_time: string })?.created_time,
      });
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }
  const error = createApiError({ error: '' });
  return res.status(httpCode.INTERNAL_SERVER_ERROR).json(error);
}

export default handler;
