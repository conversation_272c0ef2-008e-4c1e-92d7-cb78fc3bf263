import {
  Container,
  Text,
  Image,
  SimpleGrid,
  Stack,
  Box,
  BackgroundImage,
  useMantineTheme,
} from '@mantine/core';
import styles from './styles.module.scss';
import useTranslation from 'next-translate/useTranslation';
import { assetBaseUrl, ROUTES } from '@/data';
import Link from 'next/link';
import { useRouter } from 'next/router';

export default function Payment() {
  const { t } = useTranslation();
  const theme = useMantineTheme();
  const router = useRouter();
  return (
    <div style={{ paddingTop: 40 }} id="merchants-section">
      <Box className={styles.root}>
        <Container
          pt={40}
          px={{
            xs: 20,
            sm: 30,
            lg: 40,
            base: 20,
          }}
          size={1200}
        >
          <Text
            color="white"
            ff={router.locale === 'ar' ? theme.fontFamily : `BauhausC, ${theme.fontFamily}`}
            size={43}
            weight="bold"
          >
            {t('common:paymentLandingTitle')}
          </Text>
          <SimpleGrid
            cols={2}
            breakpoints={[
              { maxWidth: 'sm', cols: 2, spacing: 'md' },
              // { maxWidth: "sm", cols: 1, spacing: "sm" },
              { maxWidth: 'xs', cols: 1, spacing: 'sm' },
            ]}
          >
            <Stack mt="lg">
              <Text maw={360} lh={1.7} size="lg" mt="lg" color="dimmed">
                {t('common:paymentLandingDescription')}
              </Text>
              <Link
                href={ROUTES.merchant.path}
                style={{ textDecoration: 'none', color: theme.colors.primary[7] }}
                className={styles.aHero}
              >
                {t('common:learnMore')}
              </Link>
            </Stack>
            <Stack
              mt={{ md: -40, base: -20 }}
              ml={{ md: -80, base: 0 }}
              justify="center"
              sx={(th) => ({
                [th.fn.smallerThan('xs')]: {
                  display: 'none',
                },
              })}
            >
              <BackgroundImage
                h={{
                  lg: 550, md: 530, sm: 370, xs: 280, base: 250,
                }}
                w={{ xs: '90%', base: '100%' }}
                src={`${assetBaseUrl}/assets/new-landing/blur-hero.webp`}
                radius="xs"
              >
                <Stack h="100%" justify="center" align="center">
                  <Image
                    width="100%"
                    src={`${assetBaseUrl}/assets/new-landing/payment.png`}
                    alt="security"
                    mx="auto"
                  />
                </Stack>
              </BackgroundImage>
            </Stack>
            <Stack
              justify="center"
              sx={(the) => ({
                display: 'none',
                [the.fn.smallerThan('xs')]: {
                  display: 'block',
                },
              })}
            >
              <BackgroundImage
                pt={30}
                mx="auto"
                h={310}
                w={340}
                src={`${assetBaseUrl}/assets/new-landing/blur-hero.webp`}
                radius="xs"
              >
                <Image
                  width="100%"
                  src={`${assetBaseUrl}/assets/new-landing/payment.png`}
                  alt="security"
                  mx="auto"
                />
              </BackgroundImage>
            </Stack>
          </SimpleGrid>
        </Container>
      </Box>
    </div>
  );
}
