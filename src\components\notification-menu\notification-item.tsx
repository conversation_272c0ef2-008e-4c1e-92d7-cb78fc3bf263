import { SettingApiResponse } from '@/store/setting';
import { NotificationsTypes } from '@/types';
import {
  Box,
  Group,
  Text,
  UnstyledButton,
  useMantineTheme,
} from '@mantine/core';
import {
  IconAlertCircle,
  IconCircleCheck,
  IconCircleX,
  IconInfoCircle,
} from '@tabler/icons-react';
import dayjs from 'dayjs';
import { DATE_FORMAT } from '@/data';
import { useRouter } from 'next/router';

interface NotificationItemProps {
  notificationDetails: Omit<SettingApiResponse['notifications'][0], 'date'> & {
    date: Date;
  };
  isLast: boolean;
}
function NotificationItem(props: NotificationItemProps) {
  const {
    notificationDetails: {
      date, id, link, text, type, sticky,
    },
    isLast,
  } = props;
  const theme = useMantineTheme();

  const color = () => {
    switch (type) {
      case NotificationsTypes.SUCCESS:
        return 'green';
      case NotificationsTypes.ERROR:
        return 'red';
      case NotificationsTypes.INFO:
        return 'blue';
      case NotificationsTypes.WARNINGS:
        return 'orange';
      default:
        return 'gray';
    }
  };
  const icon = () => {
    switch (type) {
      case NotificationsTypes.SUCCESS:
        return <IconCircleCheck color={color()} />;
      case NotificationsTypes.ERROR:
        return <IconCircleX color={color()} />;
      case NotificationsTypes.INFO:
        return <IconInfoCircle color={color()} />;
      case NotificationsTypes.WARNINGS:
        return <IconAlertCircle color={color()} />;

      default:
        return '';
    }
  };
  const router = useRouter();
  const handleNotificationClick = () => {
    if (link) {
      router.push(link);
    }
  };

  const stickyBgColor = () => {
    let bgColor = '';
    if (sticky) {
      if (theme.colorScheme === 'light') {
        bgColor = 'rgba(0,0,0,0.03)';
      }
      if (theme.colorScheme === 'dark') {
        bgColor = 'rgba(255,255,255,0.05)';
      }
    }
    return bgColor;
  };
  return (
    <UnstyledButton
      key={id}
      onClick={handleNotificationClick}
      w="100%"
      py="md"
      sx={{
        borderInlineStart: '2px solid',
        borderBlockEnd: !isLast ? '0.5px solid ' : '',
        borderBlockEndColor: 'rgba(0,0,0,0.05)',
        borderInlineStartColor: color(),
        paddingInlineStart: '8px',
        minHeight: 60,
        backgroundColor: stickyBgColor(),
      }}
    >
      <Group spacing="xs" pr="8px">
        <Box>{icon()}</Box>
        <Text color="dimmed" size="xs">
          {dayjs(date).format(DATE_FORMAT)}
        </Text>
      </Group>
      <Text size="sm" px="4px">
        {text}
      </Text>
    </UnstyledButton>
  );
}

export default NotificationItem;
