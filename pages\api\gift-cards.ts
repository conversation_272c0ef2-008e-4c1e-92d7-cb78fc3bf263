/**
 * There are two handlers: get user gifts data ,redeem gift card and create gift card.
 */

import { apiEndpoints, apiMethods, httpCode } from '@/data';
import { BackendClient } from '@/lib';
import {
  createGiftCardBackendRequestSchema,
  giftCardApiResponseSchema,
  giftCardsApiResponseSchema,
  returnGiftsParams,
} from '@/store/gift-card';

import createApiError from '@/utils/api-utils/create-api-error';
import createApiResponse from '@/utils/api-utils/create-api-response';
import { getJwt } from '@/utils/api-utils/jwt';
import { captchaValidator } from '@/utils/captcha-validator';

import { NextApiRequest, NextApiResponse } from 'next';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token, email } = await getJwt(req);
  if (req.method === apiMethods.GET) {
    try {
      const { data } = await BackendClient(req).get(apiEndpoints.gifts(), {
        headers: {
          authorization: token,
        },
        params: { ...returnGiftsParams(req, email) },
      });

      return createApiResponse(res, giftCardsApiResponseSchema, data);
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
    // this function to create gift card
    // return created gift card details
  } else if (req.method === apiMethods.POST) {
    try {
      const chaKey = req?.query?.chaKey;

      await captchaValidator({
        token: chaKey as string,
        secret: process.env.RECAPTCHA_SECRET_KEY as string,
      });
      // create gift card function
      const { data } = await BackendClient(req).post(
        apiEndpoints.gifts(),
        createGiftCardBackendRequestSchema.parse(req.body),
        {
          headers: {
            authorization: token,
          },
          params: { 'populate[currency][populate]': '*' },
        },
      );

      return res
        .status(httpCode.SUCCESS)
        .json(giftCardApiResponseSchema(data?.data));
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }

  const error = createApiError({ error: '' });
  return res.status(httpCode.INTERNAL_SERVER_ERROR).json(error);
}

export default handler;
