/* eslint-disable sonarjs/no-redundant-boolean */
import { CustomCopyButton } from '@/components/common/custom-copy-button';
import { GlobalCard } from '@/components/common/global-card';
import { SettingApiResponse } from '@/store/setting';
import {
  ActionIcon, Group, Text, useMantineTheme,
} from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import {
  IconBrandTelegram,
  IconBrandWhatsapp,
  IconMapPin,
  IconPhone,
} from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';
import Link from 'next/link';
import React from 'react';

interface AgentItemProps {
  agent: SettingApiResponse['agents'][0];
  withDescription: boolean;
}
function AgentItem({ agent, withDescription }: AgentItemProps) {
  const { t } = useTranslation();
  const theme = useMantineTheme();
  const match800 = useMediaQuery('(max-width: 635px)');

  return (
    <GlobalCard props={{}} onClick={() => {}} key={agent?.id}>
      <Group align="center" spacing="xs" position="apart">
        <Group align="center" spacing="xs">
          <Text weight={500}>{agent?.name}</Text>
          <Text size="sm" color="dimmed">
            {`${agent?.city}, ${agent?.region}`}
          </Text>
        </Group>
        {withDescription && false && (
          <Group position="left" spacing="4px" sx={{ order: match800 ? 3 : 2 }}>
            <Text>{t('common:confirmAgentAccountNumber')}</Text>
            <Text
              color={
                theme.colorScheme === 'light' ? 'primary.7' : 'secondary.2'
              }
            >
              {agent?.accountId}
            </Text>
            <CustomCopyButton value={agent?.accountId ?? ''} />
          </Group>
        )}
        <Group sx={{ order: match800 ? 2 : 3 }}>
          {agent?.mobile && (
            <Group spacing="4px">
              <ActionIcon component={Link} href={`tel:${agent?.mobile}`}>
                <IconPhone color="gray" />
              </ActionIcon>
            </Group>
          )}
          {agent?.whatsapp && (
            <Group spacing="4px">
              <ActionIcon
                component={Link}
                href={`https://wa.me/${agent.whatsapp}`}
                target="_blank"
              >
                <IconBrandWhatsapp color="gray" />
              </ActionIcon>
            </Group>
          )}
          {agent?.telegram && (
            <Group spacing="4px">
              <ActionIcon
                component={Link}
                href={agent.telegram}
                target="_blank"
              >
                <IconBrandTelegram color="gray" />
              </ActionIcon>
            </Group>
          )}
          {agent?.location && (
            <Group spacing="4px">
              <ActionIcon
                component={Link}
                href={agent.location}
                target="_blank"
              >
                <IconMapPin color="gray" />
              </ActionIcon>
            </Group>
          )}
        </Group>
      </Group>
    </GlobalCard>
  );
}

export default AgentItem;
