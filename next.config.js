/** @type {import('next').NextConfig} */
const withPwa = require('next-pwa');
const runtimeCaching = require('next-pwa/cache');
// eslint-disable-next-line import/no-extraneous-dependencies
const nextTranslate = require('next-translate-plugin');

const nextDataIndex = runtimeCaching.findIndex(
  (entry) => entry.options.cacheName === 'next-data',
);

if (nextDataIndex !== -1) {
  runtimeCaching[nextDataIndex].handler = 'NetworkFirst';
} else {
  throw new Error('Failed to find next-data object in runtime caching');
}

const securityHeaders = [
  {
    key: 'X-XSS-Protection',
    value: '1; mode=block',
  },
  {
    key: 'X-Frame-Options',
    value: 'SAMEORIGIN',
  },
];

const iframeHeaders = [
  {
    key: 'X-Frame-Options',
    value: 'ALLOWALL',
  },
];

const nextConfig = nextTranslate({
  reactStrictMode: true,
  images: {
    unoptimized: true,
  },
  api: {
    bodyParser: {
      sizeLimit: '15mb',
    },
  },
  async headers() {
    return [
      {
        // Apply these headers to all routes in your application.
        source: '/:path*',
        headers: securityHeaders,
      },
      {
        // Apply these headers to all iframe routes in your application.
        source: '/iframe/:path*',
        headers: iframeHeaders,
      },
    ];
  },
});

module.exports = withPwa({
  dest: 'public',
  register: true,
  skipWaiting: true,
  runtimeCaching,
  disable: process.env.NODE_ENV === 'development',
})(nextConfig);
