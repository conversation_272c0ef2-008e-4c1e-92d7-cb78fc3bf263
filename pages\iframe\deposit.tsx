/**
 * This component is same deposit page but this route we can use it in iframe
 *
 */
import MetaTags from '@/components/common/meta-tags';

import { Container, Stack, Text } from '@mantine/core';
import { useQuery } from '@tanstack/react-query';
import useTranslation from 'next-translate/useTranslation';
import { useRouter } from 'next/router';

import React from 'react';
import { getPaymentMethodsQuery } from '@/store/payment-methods';

import { getRatesQuery } from '@/store/rate';
import { CurrenciesApiResponse, getCurrenciesQuery } from '@/store/currencies';
import CurrencyTitleSkeleton from '@/components/balance/currency-title-skeleton';
import { PageTitle } from '@/components/common/page-title';
import { EmptyData } from '@/components/common/empty-data';
import PaymentMethodsSelectSkeleton from '@/components/payment-method/select-payment-methods-skeleton';
import PaymentMethodsSelect from '@/components/payment-method/select-payment-methods';
import { TranslatedTextValue } from '@/components/common/translated-text-value';

function Deposit() {
  const { query } = useRouter();
  const { t } = useTranslation();
  const { data: ratesData } = useQuery(getRatesQuery());
  const { data, isLoading } = useQuery(getCurrenciesQuery({}));

  const initCurrency = data?.data?.filter(
    (i: CurrenciesApiResponse['data'][0]) => i.uid === query?.currency,
  );
  const item = initCurrency && initCurrency[0];
  const paymentMethodsData = useQuery(
    getPaymentMethodsQuery({
      populate: {
        depositCurrencies: true,
        depositCustomFields: true,
      },

      filters: {
        depositCurrencyId: item?.uid,
      },
    }),
  );
  if (!item && !isLoading) {
    return (
      <Stack h="100vh" justify="center">
        <EmptyData message="" />
      </Stack>
    );
  }
  return (
    <div>
      <MetaTags title={t('common:deposit')} />
      <Container pt="xl" pb="md" px="sm" size="lg">
        <PageTitle title="deposit" />
        <Stack align="center">
          {(item?.label || item?.labelAr) ? (
            <Text tt="uppercase" size="lg" weight={500}>
              <TranslatedTextValue keyEn={item?.label} keyAr={item?.labelAr} />
            </Text>
          ) : (
            <CurrencyTitleSkeleton />
          )}
        </Stack>
        {item && (
          <Stack mt="md" mx="auto" maw={800}>
            <Text color="dimmed" tt="capitalize" weight={500} size="lg">
              {t('common:chosePaymentSystem')}
            </Text>
            {paymentMethodsData?.isFetching ? (
              <PaymentMethodsSelectSkeleton />
            ) : (
              <PaymentMethodsSelect
                rates={ratesData?.rates}
                selectedCurrency={item}
                data={paymentMethodsData?.data?.data}
                transactionType="deposit"
                dataLoading={paymentMethodsData?.isFetching}
              />
            )}
          </Stack>
        )}
      </Container>
    </div>
  );
}

export default Deposit;

export async function getServerSideProps() {
  return { props: {} };
}
