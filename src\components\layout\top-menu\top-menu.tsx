import {
  <PERSON><PERSON>,
  <PERSON>,
  Burger,
  Skeleton,
  Image,
  Container,
  UnstyledButton,
  MediaQuery,
  Button,
} from '@mantine/core';

import Link from 'next/link';
import { ROUTES, assetBaseUrl, googlePlayAppLink } from '@/data';

import { useStyles } from './style';
import useTranslation from 'next-translate/useTranslation';
import { useQuery } from '@tanstack/react-query';
import { getUserQuery } from '@/store/user';
import { setCookie } from 'cookies-next';
import { useEffect, useState } from 'react';
import CreatePaymentLinkModal from '@/components/payment-link/create-payment-link-modal/create-payment-link-modal';
import { signIn, useSession } from 'next-auth/react';
import { useDisclosure } from '@mantine/hooks';
import { FeedbackPopup } from '@/components/feedback-popup';

import UserDataAndNotificationsSection from './user-data-notifications-section';

interface HeaderSearchProps {
  links: { link: string; label: string; icon: string }[];
  open: () => void;
  opened: boolean;
  isAuth: boolean;
}

export function TopMenu({
  links, open, opened, isAuth,
}: HeaderSearchProps) {
  const [openedFeedback, { open: openFeedback, close }] = useDisclosure(false);
  const [openCreatePaymentLink, setOpenCreatePaymentLink] = useState(false);
  const { t } = useTranslation();
  const { classes } = useStyles();
  const [loading, setLoading] = useState(false);
  const { status } = useSession();
  const { data, isInitialLoading } = useQuery({
    ...getUserQuery({
      loggedIn: isAuth === true,
    }),
    onSuccess(res) {
      setCookie('2fa-enabled', !!res?.authenticatorEnabled);
    },
    retry: 1,
  });
  const items = links.map(
    (link) => link?.link !== ROUTES.wallets.path && (
    <Link
      href={link.link}
      key={link.label}
      className={classes.link}
      target={link?.label === 'helpCenter' ? '_blank' : ''}
    >
      {t(`common:${link.label}`)}
    </Link>
    ),
  );

  useEffect(
    () => () => {
      setLoading(false);
    },
    [],
  );

  return (
    <Header px="md" height={56} mb={120}>
      <Container p={0} size="lg">
        <div className={classes.inner}>
          <Group w="100%" position="apart">
            <Group>
              <Burger
                className={classes.burger}
                opened={opened}
                onClick={open}
                size="sm"
                aria-label="open navbar"
              />
              <Link
                style={{ textDecoration: 'none', color: 'black' }}
                href={isAuth ? ROUTES.wallets.path : ROUTES.root.path}
              >
                <Image
                  alt="kazawallet-logo"
                  src={`${assetBaseUrl}/assets/logo/logo.png`}
                  width={40}
                  height={40}
                />
              </Link>
            </Group>

            <Group spacing={5} className={classes.links}>
              {items}

              <Link
                className={classes.link}
                href={googlePlayAppLink}
                target="_blank"
              >
                {t('common:installApp')}
              </Link>

              {/* <SpotlightOpenButton isAuth={isAuth} /> */}
            </Group>
            {(isInitialLoading || status === 'loading') && (
              <UnstyledButton aria-label="switch-lang">
                <MediaQuery smallerThan="xs" styles={{ width: '7rem' }}>
                  <Skeleton radius={50} height="2rem" width="7rem" />
                </MediaQuery>
              </UnstyledButton>
            )}
            {isAuth && !isInitialLoading && (
              <UserDataAndNotificationsSection
                isLandingHeader={false}
                data={data}
                openFeedback={openFeedback}
                setOpenCreatePaymentLink={setOpenCreatePaymentLink}
              />
            )}
            {isAuth === false && !isInitialLoading && status !== 'loading' && (
              <Group>
                <Button
                  loading={loading}
                  radius="lg"
                  onClick={() => {
                    setLoading(true);
                    signIn('keycloak', {
                      callbackUrl: ROUTES.wallets.path,
                    });
                  }}
                >
                  {t('common:loginOrRegister')}
                </Button>
              </Group>
            )}
          </Group>
          {data && (
            <CreatePaymentLinkModal
              userData={data}
              openCreatePaymentLink={openCreatePaymentLink}
              setOpenCreatePaymentLink={setOpenCreatePaymentLink}
            />
          )}
          {data && (
            <FeedbackPopup
              close={close}
              opened={openedFeedback}
              userEmail={data?.email ?? ''}
              userFullName={`${data?.firstName ?? ''} ${data?.lastName ?? ''}`}
            />
          )}
        </div>
      </Container>
    </Header>
  );
}
