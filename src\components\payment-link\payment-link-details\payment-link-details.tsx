/* eslint-disable max-lines */
import { CodeConfirmModal } from '@/components/2fa/code-confirm-modal';
import { ConfirmModal } from '@/components/common/confirm-modal';
import { playCaptcha, ROUTES } from '@/data';
import { PaymentLinksApiResponse } from '@/store/payment-links';
import { createTransferMutation } from '@/store/transfer';
import { getUserQuery } from '@/store/user';
import { ErrorFrontendType } from '@/types';
import {
  Button,
  Group, LoadingOverlay, Skeleton, Stack, Text,
} from '@mantine/core';
import { useDisclosure, useMediaQuery } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import { useMutation, useQuery } from '@tanstack/react-query';
import { getCookie } from 'cookies-next';
import currency from 'currency.js';
import dayjs from 'dayjs';
import useTranslation from 'next-translate/useTranslation';
import Link from 'next/link';
import { useState } from 'react';
import { ErrorPopup } from '@/components/common/errorr-poup';
import {
  CTitle1,
  CountDown,
  RButton,
  RGroup,
  RTitle,
  ReturnLogo,
} from './payment-link-details-components';
import { AvatarCustom } from '@/components/common/avatar';
import { IconHourglassHigh } from '@tabler/icons-react';
import { Captcha } from '@/components/common/captcha';
import { useCaptcha } from '@/hooks';
import { LimitsErrorKeys } from '@/types/error.type';
// eslint-disable-next-line complexity
function PaymentLinkDetails(props: {
  onPaySuccess: () => void;
  data: PaymentLinksApiResponse | undefined;
  isLoading: boolean;
  isAuth: boolean;
}) {
  const {
    onPaySuccess, data, isLoading, isAuth,
  } = props;
  const [opened, { open, close }] = useDisclosure(false);
  const [openedCode, setOpenedCode] = useState(false);
  const [code, setCode] = useState('');
  const [error, setIsError] = useState('');
  const [codeError, setCodeError] = useState('');
  const { t } = useTranslation();
  const match991 = useMediaQuery('(max-width: 991px)');
  const [openLimitPopup, setLimitPopup] = useState(false);

  //* ********************Variables************************************** */
  const paymentLinkDetails = data?.data[0];
  const receiverDetails = paymentLinkDetails?.userTo;
  const currencyData = paymentLinkDetails?.currency;
  const isExpired = dayjs(paymentLinkDetails?.expiryDate).isBefore(Date.now())
    || paymentLinkDetails?.status === 'timed_out';
  const isFulfilled = paymentLinkDetails?.status === 'fulfilled';

  const { execute, reCaptchaRef, reset } = useCaptcha();
  //* ********************Mutations************************************** */
  const { mutate, isLoading: mutationLoading } = useMutation({
    ...createTransferMutation(),
    onSuccess: () => {
      close();
      setCodeError('');
      setOpenedCode(false);
      setCode('');
      notifications.show({
        message: t('common:transferSuccess'),
        color: 'blue',
      });
      onPaySuccess();
      reset();
    },
    onError(e: ErrorFrontendType) {
      setCode('');
      close();
      if (e?.response?.data?.message?.key === 'invalidCode') {
        setCodeError('invalidCode');
      } else setCodeError('');
      if (
        e?.response?.data?.message?.key
        === LimitsErrorKeys.ERROR_PASS_THE_LIMIT
      ) {
        setLimitPopup(true);
      }
      reset();
    },
  });
  //* ********************Utilities************************************** */
  const userData = useQuery({
    ...getUserQuery({
      loggedIn: isAuth === true,
    }),
  });
  const submitTransfer = (qrCode: string | undefined) => {
    const body = {
      body: {
        currencyFrom: currencyData?.id as number,
        currencyTo: currencyData?.id as number,
        type: 'payment_link',
        userTo: receiverDetails?.accountId as string,
        amount: paymentLinkDetails?.amount,
        notes: '',
        paymentLink: paymentLinkDetails?.id,
      },
      qrCode,
    };
    if (reCaptchaRef.current) {
      execute((token) => mutate({
        ...body,
        chaKey: token,
      }));
    } else {
      mutate(body);
    }
  };
  const precision = currencyData?.precision ?? 2;
  const symbol = currencyData?.symbol ?? '';
  const actualAmount = paymentLinkDetails
    && currency(paymentLinkDetails?.actualAmount, {
      symbol,
      precision,
    }).format();

  const fees = paymentLinkDetails?.fees
    && currency(paymentLinkDetails.fees, {
      symbol,
      precision,
    }).format();
  const amount = paymentLinkDetails
    && currency(paymentLinkDetails?.amount, {
      symbol,
      precision,
    }).format();
  const fullName = `${receiverDetails?.firstName || ''} ${
    receiverDetails?.lastName || ''
  }`;

  const onPayButtonClick = () => {
    if (!isAuth) {
      const errorMessage = t('common:loginPlease');
      setIsError(errorMessage);
    } else if (getCookie('2fa-enabled')) setOpenedCode(true);
    else open();
  };
  const currencyBalance = () => {
    if (userData?.isLoading && isAuth) {
      return (
        <Group position="center">
          <Skeleton width={200} height={40} />
        </Group>
      );
    }
    if (!isAuth) {
      return <div />;
    }
    const selectedCyurrency = userData?.data?.currencies?.filter(
      (i) => i?.code === data?.data[0]?.currency?.code,
    );

    return (
      <Group position="center">
        <Text size={22}>
          {t('common:available')}
          :
        </Text>
        {selectedCyurrency && selectedCyurrency?.length > 0 ? (
          <Text size={22}>
            {` ${currency(selectedCyurrency[0]?.amount, {
              precision: selectedCyurrency[0]?.precision ?? 2,
              symbol: '',
            })} ${selectedCyurrency[0]?.symbol ?? ''} `}
          </Text>
        ) : (
          <Text size={22}>
            {` ${currency(0, {
              precision: data?.data[0]?.currency?.precision ?? 2,
              symbol: '',
            })} ${data?.data[0]?.currency?.symbol ?? ''} `}
          </Text>
        )}
      </Group>
    );
  };

  return (
    <>
      <LoadingOverlay visible={!!isLoading} />
      <Stack spacing="md">
        <Group spacing={5}>
          <RTitle>{t('common:youGetInvoiceFrom')}</RTitle>
          <RTitle sharedProps={{ tt: 'capitalize', color: 'blue' }}>
            {fullName}
          </RTitle>
        </Group>
        <Stack align="center" mt={5} spacing={0}>
          <AvatarCustom name={fullName} size={100} src={receiverDetails?.url} />
          <Group position="center">
            <Text size={match991 ? 40 : 50}>{actualAmount}</Text>
          </Group>
          {currencyBalance()}
        </Stack>

        <Group w="65%" mx="auto" position="apart">
          <Group spacing={5}>
            <Text color="dimmed" weight={500}>
              {t('common:fees')}
              :
            </Text>
            <Text color="dimmed">{fees}</Text>
          </Group>
          <Group spacing={5}>
            <Text color="dimmed" weight={500}>
              {t('common:amount')}
              :
            </Text>
            <Text color="dimmed">{amount}</Text>
          </Group>
        </Group>
        <Stack align="center">
          {isExpired && (
            <Text color="red" weight={500}>
              {t('common:linkExpired')}
            </Text>
          )}
          {isFulfilled && !isExpired && (
            <Text size="lg" color="green" weight={700}>
              {t('common:paid')}
            </Text>
          )}

          {!isFulfilled && !isExpired && (
            <>
              <RButton
                sharedProps={{
                  disabled: !paymentLinkDetails?.amount,
                  onClick: onPayButtonClick,
                }}
              >
                {t('common:pay')}
              </RButton>
              <Text color="red" weight={500}>
                {error}
              </Text>
              <Stack spacing={5} align="center">
                <Text>{t('common:thisLinkActiveUntil')}</Text>
                {paymentLinkDetails?.expiryDate && (
                  <CountDown
                    date={paymentLinkDetails?.expiryDate}
                    autoStart
                    renderer={(p) => (
                      <Group spacing={2}>
                        <Text size={26} weight="bold" color="dimmed">
                          {p.formatted.minutes}
                          :
                          {p.formatted.seconds}
                        </Text>
                        <IconHourglassHigh size={22} color="gray" />
                      </Group>
                    )}
                  />
                )}
              </Stack>
            </>
          )}
          <Group>
            <CTitle1>
              {t('common:doNotHave')}
              {' '}
              {currencyData?.code}
              {' '}
              ?
            </CTitle1>
            <Link
              href={`${ROUTES.exchange.path}?to=${currencyData?.uid}`}
              style={{ textDecoration: 'none' }}
            >
              <CTitle1>{t('common:exchangeNow')}</CTitle1>
            </Link>
          </Group>
        </Stack>
      </Stack>
      <RGroup>
        <Stack spacing={0}>
          <CTitle1>{t('common:store')}</CTitle1>
          <CTitle1>
            {' '}
            {fullName}
          </CTitle1>
        </Stack>
        <Stack spacing={0}>
          <CTitle1>{t('common:orderId')}</CTitle1>
          <CTitle1>{paymentLinkDetails?.uid}</CTitle1>
        </Stack>
      </RGroup>
      <Group mt={20} position="center" spacing="sm">
        <CTitle1>{t('common:paymentFrom')}</CTitle1>
        <ReturnLogo />
      </Group>
      <Group position="center">
        <Link
          style={{
            textDecoration: 'none',
            color: 'inherit',
            opacity: 0.7,
            fontSize: '15px',
          }}
          href={ROUTES.merchant.path}
        >
          {t('common:startSellingYourProduct')}
        </Link>
      </Group>
      <ConfirmModal
        close={close}
        openedDefault={opened}
        isLoading={mutationLoading}
        onClick={() => submitTransfer(undefined)}
        message={<div>{t('common:completeOperation')}</div>}
      />
      <CodeConfirmModal
        codeError={codeError}
        btnText={t('common:transfer')}
        setOpenedCode={setOpenedCode}
        isLoading={mutationLoading}
        onClick={submitTransfer}
        openedDefault={openedCode}
        code={code}
        setCode={setCode}
        title={t('common:enterCode')}
        closeable={false}
        additionalContent={undefined}
      />
      <ErrorPopup
        open={openLimitPopup}
        setOpen={setLimitPopup}
        message={t('errors:limit-error')}
        actionButton={(
          <Button
            fullWidth
            radius="lg"
            component={Link}
            href={`${ROUTES.myAccount.path}?page=limits`}
          >
            {t('common:goToLimits')}
          </Button>
          )}
      />
      <Captcha reCaptchaRef={reCaptchaRef} active={playCaptcha} />
    </>
  );
}

export default PaymentLinkDetails;
