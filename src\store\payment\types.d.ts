import { z } from 'zod';
import {
  createPaymentApiRequestSchema,
  createPaymentBackendRequestSchema,
} from './request-transformer';
import { paymentsApiResponseSchema } from './responses-transformers';
import { Pagination } from '@/types';
import { QueryFunctionContext } from '@tanstack/react-query';

export type Filter = {
  type?: 'deposit' | 'withdraw' | null;
  status?: string | null;
  currencyId?: number | string | null;
  paymentMethod?: number | string | null;
  currencyUid?: number | string | string[] | null;
  search?: number | string | null;
  createdFrom?: string | null;
  createdTo?: string | null;
};
export interface getPaymentsQueryProps {
  populate?: {};
  pagination?: Pagination;
  filters?: Filter;
  params?: QueryFunctionContext;
}

export type CreatePaymentBackendRequest = z.infer<
  typeof createPaymentBackendRequestSchema
>;
export type CreatePaymentApiRequest = z.infer<
  typeof createPaymentApiRequestSchema
>;

export type PaymentsApiResponse = z.infer<typeof paymentsApiResponseSchema>;
