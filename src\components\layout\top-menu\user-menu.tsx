import { AvatarCustom } from '@/components/common/avatar';
import { CustomCopyButton } from '@/components/common/custom-copy-button';
import { Icon } from '@/components/common/icon';
import { LanguagesMenu } from '@/components/common/languages-menu';
import { InstallPWA } from '@/components/pwa/pwa-install-button';

import { LocaleCookie, ROUTES } from '@/data';
import { usePwa } from '@/store/pwa/store';
import { getUserQuery, UserApiResponse } from '@/store/user';
import { getTranslatedTextValue } from '@/utils';
import {
  Menu,
  Text,
  Stack,
  useMantineTheme,
  useMantineColorScheme,
  UnstyledButton,
  Avatar,
  Image,
  Tooltip,
  Group,
} from '@mantine/core';
import { useLocalStorage } from '@mantine/hooks';
import { useQuery } from '@tanstack/react-query';
import { setCookie } from 'cookies-next';
import useTranslation from 'next-translate/useTranslation';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useState } from 'react';

export function UserMenu({
  user,
  setOpenCreatePaymentLink,
  openFeedback,
}: {
  user: UserApiResponse | undefined;
  setOpenCreatePaymentLink: (v: boolean) => void;
  openFeedback: () => void;
}) {
  const [, setValue] = useLocalStorage<string>({
    key: 'walkthrough-opened',
    defaultValue: 'false',
    getInitialValueInEffect: false,
  });
  const { t } = useTranslation();
  const router = useRouter();
  const {
    pathname, asPath, query, push, locale,
  } = router;
  const { data } = useQuery({ ...getUserQuery({}), enabled: false });
  const isAgent = data?.role === 'agent';
  const theme = useMantineTheme();
  const themeColor = useMantineColorScheme();
  const deferredPrompt = usePwa((state) => state.deferredPrompt);
  const [opened, setOpened] = useState(false);
  const returnSlicedString = (
    string: string | undefined,
    maxLength: number,
  ) => {
    let stringText = string ?? '';
    if (stringText !== '' && stringText?.length > maxLength) {
      stringText = `${stringText.slice(0, maxLength)}...`;
    }
    return stringText;
  };
  return (
    <Menu opened={opened} onChange={setOpened} shadow="md" width={260}>
      <Menu.Target>
        <UnstyledButton aria-label="open user menu">
          <AvatarCustom
            src={user?.avatar?.url ?? ''}
            name={`${user?.firstName} ${user?.lastName}`}
            size={30}
          />
        </UnstyledButton>
      </Menu.Target>
      <Menu.Dropdown>
        <Stack py="xs" align="center" spacing={1}>
          <AvatarCustom
            src={user?.avatar?.url ?? ''}
            name={`${user?.firstName} ${user?.lastName}`}
            size={40}
          />

          <Text size="md" color="dimmed" weight={500}>
            {`${returnSlicedString(user?.firstName, 12)} ${returnSlicedString(
              user?.lastName,
              12,
            )}`}
          </Text>
          <div
            dir="ltr"
            style={{ display: 'flex', gap: 0, alignItems: 'center' }}
          >
            <Text>{returnSlicedString(user?.email, 22)}</Text>
            <CustomCopyButton value={user?.email ?? ''} />
          </div>
        </Stack>

        <Menu.Divider />
        {user?.badges && (
          <Group my={8} spacing="5px" position="center">
            {user.badges?.slice(0, 5).map((badge) => (
              <Tooltip
                label={getTranslatedTextValue(
                  locale,
                  badge?.title,
                  badge?.titleAr,
                )}
                key={badge?.title}
              >
                <Image
                  sx={{ objectFit: 'contain' }}
                  alt="badge"
                  width={35}
                  height={35}
                  radius={35}
                  src={badge?.image}
                />
              </Tooltip>
            ))}

            {user.badges.length > 6 && (
              <Avatar
                sx={{
                  border: '2px solid gray',
                  color: '#569245',
                }}
                size={35}
                radius={35}
              >
                +
                {user.badges.length - 6}
              </Avatar>
            )}
          </Group>
        )}
        <Link
          style={{ textDecoration: 'none' }}
          href={`${ROUTES.myAccount.path}?page=profile`}
        >
          <Menu.Item icon={<Icon icon="settings" size={14} color="dark" />}>
            {t(`common:${ROUTES.myAccount.key}`)}
          </Menu.Item>
        </Link>
        <Link style={{ textDecoration: 'none' }} href={ROUTES.massPayout.path}>
          <Menu.Item icon={<Icon icon="arrows-move" size={14} color="dark" />}>
            {t(`common:${ROUTES.massPayout.key}`)}
          </Menu.Item>
        </Link>
        <Link style={{ textDecoration: 'none' }} href={ROUTES.merchants.path}>
          <Menu.Item
            icon={<Icon icon="building-store" size={14} color="dark" />}
          >
            {t(`common:${ROUTES.merchants.key}`)}
          </Menu.Item>
        </Link>
        {isAgent && (
          <Link
            style={{ textDecoration: 'none' }}
            href={ROUTES.agentDeposit.path}
          >
            <Menu.Item icon={<Icon icon="tournament" size={14} color="dark" />}>
              {t(`common:${ROUTES.agentDeposit.key}`)}
            </Menu.Item>
          </Link>
        )}
        <Link style={{ textDecoration: 'none' }} href={ROUTES.help.path}>
          <Menu.Item icon={<Icon icon="help" size={14} color="dark" />}>
            {t('common:helpCenter')}
          </Menu.Item>
        </Link>
        <Menu.Item
          onClick={() => setValue('false')}
          icon={<Icon icon="info-square-rounded" size={14} color="dark" />}
        >
          {t('common:showWalkthrough')}
        </Menu.Item>
        <Menu.Item
          icon={<Icon icon="message" size={14} color="dark" />}
          onClick={openFeedback}
        >
          {t('common:feedback')}
        </Menu.Item>

        <Menu.Item
          icon={<Icon icon="link" size={14} color="dark" />}
          onClick={() => setOpenCreatePaymentLink(true)}
        >
          {t('common:createPaymentLink')}
        </Menu.Item>
        {deferredPrompt && (
          <Menu.Item
            icon={<Icon icon="device-mobile" size={14} color="dark" />}
          >
            <InstallPWA />
          </Menu.Item>
        )}

        <Menu.Item
          closeMenuOnClick={false}
          onClick={() => themeColor.toggleColorScheme()}
          icon={(
            <Icon
              icon={theme.colorScheme === 'light' ? 'moon-stars' : 'sun'}
              size={14}
              color="dark"
            />
          )}
        >
          {theme.colorScheme === 'light'
            ? t('common:darkMode')
            : t('common:lightMode')}
        </Menu.Item>
        <Menu.Item
          closeMenuOnClick={false}
          icon={<Icon icon="world" size={14} color="dark" />}
        >
          <Menu>
            <Menu.Target>
              <LanguagesMenu
                setMenuOpened={setOpened}
                button={<Text tt="capitalize">{t('common:language')}</Text>}
              />
            </Menu.Target>
            <Menu.Dropdown>
              <Menu.Item
                onClick={() => {
                  setCookie(LocaleCookie, 'en');
                  router.push({ pathname, query }, asPath, {
                    locale: 'en',
                  });
                  setOpened(false);
                }}
              >
                {t('common:english')}
              </Menu.Item>
              <Menu.Item
                onClick={() => {
                  setCookie(LocaleCookie, 'ar');
                  router.push({ pathname, query }, asPath, {
                    locale: 'ar',
                  });
                  setOpened(false);
                }}
              >
                {t('common:arabic')}
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </Menu.Item>

        <Menu.Item
          onClick={() => push(ROUTES.logout)}
          color="red"
          icon={<Icon icon="logout" size={14} color="dark" />}
        >
          {t('common:logout')}
        </Menu.Item>
      </Menu.Dropdown>
    </Menu>
  );
}
