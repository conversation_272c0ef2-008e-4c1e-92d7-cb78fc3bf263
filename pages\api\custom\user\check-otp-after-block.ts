/**
 * This handler to unblock user account.
 */
import { apiEndpoints, httpCode } from '@/data';
import { BackendClient } from '@/lib';
import createApiError from '@/utils/api-utils/create-api-error';
import { getJwt } from '@/utils/api-utils/jwt';
import { captchaValidator } from '@/utils/captcha-validator';
import { NextApiRequest, NextApiResponse } from 'next';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token } = await getJwt(req);
  if (req.method === 'POST') {
    try {
      const chaKey = req?.query?.chaKey;
      await captchaValidator({
        token: chaKey as string,
        secret: process.env.RECAPTCHA_SECRET_KEY as string,
      });
      const { data } = await BackendClient(req).post(
        apiEndpoints.unblockUserAccount(),
        {
          otp: req.body?.code ? +req.body.code : '',
        },
        {
          headers: {
            Authorization: token,
          },
        },
      );
      return res.status(httpCode.SUCCESS).json(data);
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }
  const error = createApiError({ error: '' });
  return res.status(httpCode.INTERNAL_SERVER_ERROR).json(error);
}

export default handler;
