import { SegmentedControl, Text } from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';
import React from 'react';

interface Props {
  disabled: boolean;
  value: string;
  label: string;
  width: number|string;
  onChange: (v: 'true' | 'false') => void;
}
function CustomSwitch({
  disabled, value, onChange, label, width,
}: Props) {
  const { t } = useTranslation();
  return (
    <div>
      <Text weight={500} size="sm">
        {label}
      </Text>
      <SegmentedControl
        disabled={disabled}
        value={value}
        onChange={onChange}
        w={width}
        data={[
          {
            value: 'true',
            label: t('common:enabled'),
          },
          {
            value: 'false',
            label: t('common:disabled'),
          },
        ]}
      />
    </div>
  );
}

export default CustomSwitch;
