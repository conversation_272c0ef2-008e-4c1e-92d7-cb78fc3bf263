import { ROUTES } from '@/data';
import {
  Group, Text, rem, Button, Image,
} from '@mantine/core';
import currency from 'currency.js';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useStyles } from './style';
import useTranslation from 'next-translate/useTranslation';
import { GlobalCard } from '@/components/common/global-card';
import { TranslatedTextValue } from '@/components/common/translated-text-value';

interface MainStatProps {
  title: string;
  titleAr: string | null;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  icon: string | any;
  value: number;
  balanceUsd: number;
  currencySign: string | null;
  uid: string;
  precision: number;
}

export default function WalletCard({
  icon,
  title,
  titleAr,
  value,
  balanceUsd,
  currencySign,
  uid,
  precision,
}: MainStatProps) {
  const { t } = useTranslation();
  const { classes } = useStyles();
  const { push } = useRouter();
  return (
    <GlobalCard
      props={{ style: { cursor: 'pointer' } }}
      onClick={() => push(`${ROUTES.walletHistory.path}/${uid}`)}
      key={title}
    >
      <Group position="apart">
        <Text size="sm" weight={500} tt="uppercase">
          <TranslatedTextValue keyEn={title} keyAr={titleAr} />
        </Text>
        <Text size="md" weight={500}>
          $
          {' '}
          {currency(balanceUsd, { symbol: '' }).format()}
        </Text>
        <Image src={icon} width={40} radius={50} alt="currency" />
      </Group>
      <Group position="center">
        <Text weight="bold" size={rem(25)}>
          {currencySign}
        </Text>
        <Text className={classes.value}>
          {currency(value, { symbol: '', precision }).format()}
        </Text>
      </Group>
      <Group position="center" align="flex-end" spacing="xs" mt={25}>
        <Link href={`${ROUTES.deposit.path}?currency=${uid}`}>
          <Button
            onClick={(e) => {
              e.stopPropagation();
            }}
            h={30}
            w={150}
            tt="capitalize"
            radius={50}
            variant="outline"
          >
            {t('common:deposit')}
          </Button>
        </Link>
        <Link href={`${ROUTES.withdraw.path}?currency=${uid}`}>
          <Button
            onClick={(e) => {
              e.stopPropagation();
            }}
            h={30}
            w={150}
            tt="capitalize"
            radius={50}
            variant="outline"
          >
            {t('common:withdraw')}
          </Button>
        </Link>
      </Group>
    </GlobalCard>
  );
}
