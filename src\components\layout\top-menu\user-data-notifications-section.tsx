import { CustomCopyButton } from '@/components/common/custom-copy-button';
import { NotificationsMenu } from '@/components/notification-menu';
import {
  Group,
  Indicator,
  Text,
  Tooltip,
  UnstyledButton,
  useMantineTheme,
} from '@mantine/core';
import { IconBell } from '@tabler/icons-react';
import React from 'react';
import { UserMenu } from './user-menu';
import { UserApiResponse } from '@/store/user';
import useTranslation from 'next-translate/useTranslation';
import { useQuery } from '@tanstack/react-query';
import { getSettingQuery, SettingApiResponse } from '@/store/setting';

import { useLocalStorage } from '@mantine/hooks';

interface Props {
  data: UserApiResponse | undefined;
  setOpenCreatePaymentLink: (v: boolean) => void;
  openFeedback: () => void;
  isLandingHeader: boolean;
}

function UserDataAndNotificationsSection({
  data,
  openFeedback,
  setOpenCreatePaymentLink,
  isLandingHeader,
}: Props) {
  const theme = useMantineTheme();
  const { t } = useTranslation();

  const [value, setValue] = useLocalStorage({
    key: 'notifications',
    defaultValue: '',
  });

  // to show notifications menu should set enabled =true
  const enabled = true;

  const { data: settingsData } = useQuery({
    ...getSettingQuery(),
    enabled,
  });
  // get max items date to save the last notification date in local storage to check if there was a new notifications
  const getMaxDateItem = (items: SettingApiResponse['notifications']) => {
    if (items?.length === 0) {
      return null; // Return null if the array is empty
    }

    return items?.reduce((maxItem, currentItem) => {
      if (currentItem?.date && maxItem?.date) {
        return currentItem.date > maxItem.date ? currentItem : maxItem;
      }
      return currentItem;
    });
  };
  // set last notification date in local storage when open notifications menu
  const handleOpenNotificationsMenu = () => {
    // Get the item with the maximum date
    const maxDateItem = getMaxDateItem(settingsData?.notifications);
    setValue(maxDateItem?.date ?? '');
  };

  const getNewNotificationsCount = () => {
    const newNotifications = settingsData?.notifications?.filter(
      (notification: SettingApiResponse['notifications'][0]) => notification?.date && notification.date > value,
    );
    return newNotifications?.length ?? 0;
  };

  return (
    <Group spacing={5}>
      <div dir="ltr" style={{ display: 'flex', gap: 3, alignItems: 'center' }}>
        {isLandingHeader ? (
          <Text size="sm" weight={500} color="secondary.2">
            {data?.accountId}
          </Text>
        ) : (
          <Text
            size="sm"
            weight={500}
            color={theme.colorScheme === 'light' ? 'primary.7' : 'secondary.2'}
          >
            {data?.accountId}
          </Text>
        )}
        <CustomCopyButton value={data?.accountId ?? ''} />
      </div>
      <UserMenu
        user={data}
        setOpenCreatePaymentLink={setOpenCreatePaymentLink}
        openFeedback={openFeedback}
      />
      {enabled && (
        <NotificationsMenu
          handleOpen={handleOpenNotificationsMenu}
          target={(
            <Tooltip
              style={{ fontSize: 12 }}
              withArrow
              label={t('common:notification')}
            >
              <UnstyledButton mt={5}>
                <Indicator
                  color="red"
                  label={getNewNotificationsCount()}
                  inline
                  size={20}
                  disabled={getNewNotificationsCount() === 0}
                >
                  <IconBell color={theme.colorScheme === 'light' ? 'gray' : '#E9ECEF'} />
                </Indicator>
              </UnstyledButton>
            </Tooltip>
          )}
        />
      )}
    </Group>
  );
}

export default UserDataAndNotificationsSection;
