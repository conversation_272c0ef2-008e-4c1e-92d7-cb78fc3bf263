# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- **Development server**: `npm run dev` or `yarn dev`
- **Build production**: `npm run build` or `yarn build`
- **Start production**: `npm run start` or `yarn start`
- **Linting**: `npm run lint` or `yarn lint`
- **Type checking**: `npm run type-check` or `yarn type-check`

## Project Architecture

### Tech Stack
- **Framework**: Next.js 13.4.2 with TypeScript
- **UI Library**: Mantine v6 (components, hooks, forms, notifications)
- **State Management**: Zustand for global state, React Query for server state
- **Authentication**: NextAuth.js with Keycloak
- **Internationalization**: next-translate (Arabic/English support with RTL)
- **Styling**: SCSS modules + Mantine theming
- **PWA**: next-pwa for Progressive Web App features

### Key Directory Structure

#### `/pages` - Next.js App Router
- API routes in `/pages/api/` - middleware layer between frontend and backend
- Page components with SCSS modules for styling
- Special pages: `_app.tsx` (providers), `_document.tsx` (HTML structure)

#### `/src/components` - React Components
- **`/common`**: Reusable UI components (buttons, modals, forms)
- **`/layout`**: App shell (header, sidebar, footer) and landing page layout
- **Feature-specific folders**: `wallets/`, `transfer/`, `exchange/`, `deposit/`, etc.
- Component structure: `component.tsx`, `index.tsx`, optional `style.ts`/`.module.scss`

#### `/src/store` - State Management
- Feature-based Zustand stores with React Query integration
- Each store has: `calls.ts` (API), `types.d.ts`, transformers for request/response
- Pattern: API calls → transformers → Zustand store → React Query cache

#### `/src/lib` - Utilities
- `axios.ts`: API client configuration (frontend + backend instances)
- `emotion-cache.ts`: Mantine emotion configuration
- `notion.ts`: Notion API integration

#### `/src/data` - Constants & Configuration
- `api-endpoints.ts`: Backend API endpoint definitions
- `routes.ts`: Frontend route constants
- `constants.ts`: App-wide constants

### Architecture Patterns

#### API Layer Architecture
1. **Frontend** → `/pages/api/` (Next.js API routes)
2. **API Routes** → Backend via `BackendClient` (axios)
3. **Store calls** → Frontend API via `ApiClient`
4. Request/response transformers handle data mapping

#### Component Organization
- Feature-based component grouping
- Shared components in `/common`
- Each component exports through `index.tsx`
- SCSS modules for component-specific styling

#### Authentication Flow
- Keycloak integration via NextAuth.js
- Protected routes defined in `authPages` array
- Session management with automatic redirect for unauthenticated users

#### Internationalization
- Arabic (RTL) and English (LTR) support
- Translation files in `/locales/[lang]/`
- RTL layout handling in main layout component

### Development Notes

#### State Management Pattern
- Use React Query for server state caching and synchronization
- Zustand stores for client-side state that persists across components
- Each feature has its own store module with typed interfaces

#### Styling Approach
- Mantine theme system for consistent design
- SCSS modules for component-specific styles
- Global styles in `styles.css`
- Responsive design with Mantine's breakpoint system

#### PWA Features
- Service worker for offline functionality
- App shell caching strategy
- Install prompt handling in layout component

#### Security Headers
- CSP and security headers configured in `next.config.js`
- Special iframe handling for embedded wallet features

### Environment Variables Required
- `BACKEND_URL`: Backend API base URL
- `APP_ID`: Application identifier for backend
- `NEXT_PUBLIC_MAINTENANCE_MODE`: Maintenance mode toggle