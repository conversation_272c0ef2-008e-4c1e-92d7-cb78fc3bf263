import MetaTags from '@/components/common/meta-tags';
import {
  ActionIcon,
  Button,
  Container,
  Group,
  Image,
  PinInput,
  Skeleton,
  Stack,
  Title,
  useMantineTheme,
} from '@mantine/core';
import React, { useEffect, useState } from 'react';
import classes from './style.module.scss';
import { useRouter } from 'next/router';
import { signOut, useSession } from 'next-auth/react';
import { assetBaseUrl, ROUTES } from '@/data';
import { useMutation, useQuery } from '@tanstack/react-query';
import {
  getUserQuery,
  resendOtpCodeMutation,
  unblockUserAccountMutation,
} from '@/store/user';
import { notifications } from '@mantine/notifications';
import { Captcha } from '@/components/common/captcha';
import { useCaptcha } from '@/hooks';
import { getCookie } from 'cookies-next';
import { BlockedErrorKeys } from '@/types/error.type';
import Link from 'next/link';
import SubmitButton from '@/components/common/submit-button';
import useTranslation from 'next-translate/useTranslation';
import { useForm } from '@mantine/form';
import { LanguagesMenu } from '@/components/common/languages-menu';
import { IconWorld } from '@tabler/icons-react';

const maskEmail = (email: string | undefined | null) => {
  if (email) {
    const [localPart, domain] = email.split('@');
    const maskedLocalPart = localPart.length > 4
      ? localPart[0] + localPart[1] + '*'.repeat(8) + localPart.slice(-2)
      : localPart;

    return `${maskedLocalPart}@${domain}`;
  }
  return '';
};

function AccountBlockedChangeIP() {
  const { t } = useTranslation();
  const theme = useMantineTheme();
  const { replace, locale } = useRouter();
  const BlockedError = getCookie('ACCOUNT_BLOCK');
  const [loading, setLoading] = useState(false);
  const [codeError, setCodeError] = useState('');
  const [code, setCode] = useState('');
  const { execute, reCaptchaRef, reset } = useCaptcha();
  const { refetch } = useQuery(getUserQuery({}));
  const { status, data: user } = useSession();
  const form = useForm();

  useEffect(() => {
    refetch();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (
      !BlockedError
      || BlockedError !== BlockedErrorKeys.USER_BLOCKED_CHANGE_IP
    ) {
      replace(ROUTES.root.path);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [BlockedError]);

  const { mutate: unBlockAccount } = useMutation({
    ...unblockUserAccountMutation(),
    onSuccess(data) {
      setLoading(false);
      reset();
      setCode('');
      notifications.show({
        message: data?.message,
        color: 'blue',
      });
      replace(ROUTES.root.path);
    },
    onError: () => {
      setLoading(false);
      setCodeError('invalidCode');
      setCode('');
      reset();
    },
  });
  const { mutate: resendOtp, isLoading: isLoadingResendOtp } = useMutation({
    ...resendOtpCodeMutation(),
    onSuccess(data) {
      notifications.show({
        message: data?.message,
        color: 'blue',
      });
    },
  });

  const handleUnblockAccount = () => {
    setCodeError('');
    setLoading(true);
    if (reCaptchaRef.current) {
      execute((token) => unBlockAccount({
        code,
        chaKey: token,
      }));
    } else {
      unBlockAccount({ code });
    }
  };

  return (
    <div className={classes.root}>
      <MetaTags />
      <Group className={classes.language} p="xl" position="right">
        <LanguagesMenu
          setMenuOpened={undefined}
          button={(
            <ActionIcon
              variant="outline"
              size="xl"
              color="gray"
              aria-label="switch-lang"
            >
              <IconWorld color="gray" size={32} />
            </ActionIcon>
          )}
        />
      </Group>
      <Container className={classes.content}>
        <Image
          mx="auto"
          width={350}
          src={
            locale === 'ar'
              ? `${assetBaseUrl}/assets/logo/logo-full-ar-dark.png`
              : `${assetBaseUrl}/assets/logo/logo-full-en-dark.png`
          }
        />

        <Title
          order={3}
          ta="center"
          color="white"
          sx={{
            fontFamily: `Greycliff CF, ${theme.fontFamily}`,
          }}
        >
          {t('common:accountBlocked')}
          <Group position="center" align="center">
            {t('common:accountBlockedChangeIPErrorFirstSection')}
            {' '}
            {status === 'loading' ? (
              <Skeleton width={100} height={20} />
            ) : (
              `"${maskEmail(user?.user?.email)}"`
            )}
            {' '}
            {t('common:accountBlockedChangeIPErrorSecondSection')}
          </Group>
        </Title>

        <form onSubmit={form.onSubmit(handleUnblockAccount)}>
          <Stack align="center">
            <PinInput
              mt={30}
              dir="ltr"
              error={!!codeError}
              type="number"
              size="lg"
              length={6}
              value={code}
              onChange={setCode}
            />
            <SubmitButton
              type="submit"
              radius="sm"
              loading={loading}
              fullWidth
              maw={300}
              disabled={!code || code?.length < 6}
            >
              {t('common:continue')}
            </SubmitButton>
            <Button
              mt="xs"
              color="red"
              size="xs"
              loading={isLoadingResendOtp}
              variant="outline"
              fullWidth
              maw={300}
              onClick={() => resendOtp()}
            >
              {t('common:resendCode')}
            </Button>
            <Group w={300} position="apart">
              <Button
                w={130}
                onClick={() => signOut({ redirect: true, callbackUrl: ROUTES.root.path })}
              >
                {t('common:logout')}
              </Button>

              <Button
                w={130}
                variant="outline"
                component={Link}
                href="https://kazawallet.trengohelp.com"
                target="_blank"
              >
                {t('common:helpCenter')}
              </Button>
            </Group>
          </Stack>
        </form>
      </Container>
      <Captcha reCaptchaRef={reCaptchaRef} />
    </div>
  );
}

export default AccountBlockedChangeIP;
