/**
 * APIs routes endpoints
 */
export const apiEndpoints = {
  register: '/auth/local/register',
  transfers: (id?: string | undefined) => (id ? `/transfers/${id}` : '/transfers'),
  payments: (id?: string | undefined) => (id ? `/payments/${id}` : '/payments'),
  paymentByIdAsParam: (id: string) => `/payments?id=${id}`,
  currencies: (id?: string | undefined) => (id ? `/currencies/${id}` : '/currencies'),
  uploadFile: (id?: string) => (id ? `/upload/${id}` : '/upload'),
  users: (id?: string | undefined) => (id ? `/users/${id}` : '/users'),
  usersByIdAsParams: (id?: string) => `/users?id=${id}`,
  rate: () => '/rate',
  totalBalance: () => '/custom/user/wallettotal',
  regenerateApiKey: () => '/custom/user/regenrateApiKey',
  paymentMethods: () => '/payment-methods',
  paymentLinks: () => '/payment-links',
  massPayout: () => '/custom/user/massPayout',
  massPayoutCheck: () => '/custom/user/checkMassPayoutAllErrors',
  feedback: () => '/feedback',
  merchant: () => '/merchant',
  notifications: (id?: string | undefined) => (id ? `/notifications/${id}` : '/notifications'),
  notificationsByIdAsParams: (id?: number) => `/notifications?id=${id}`,
  gifts: (id?: string | undefined) => (id ? `/gift-cards/${id}` : '/gift-cards'),
  giftCard: '/custom/gift-card/get',
  giftCardRedeem: '/custom/gift-card/redeem',
  unblockUserAccount: () => '/custom/user/check-otp-after-block',
  resendUnblockAccountOtpCode: () => '/custom/user/resend-otp-email',
  setting: () => '/setting',
  giveaway: () => '/giveaway',
  agent: '/custom/agent',
  paymentAgentByIdAsParam: (id: string) => `/custom/agent?id=${id}`,
  agentRequest: '/agent',

  debitCards: (id?: string) => (id ? `/flexi-cards/${id}` : '/flexi-cards'),
  debitCardApplication: () => '/flexi-cards/apply',
  debitCardTopUp: (id: string) => `/flexi-cards/${id}/top-up`,
  debitCardFreeze: (id: string) => `/flexi-cards/${id}/freeze`,
  debitCardUnfreeze: (id: string) => `/flexi-cards/${id}/unfreeze`,
  debitCardTransactions: (id: string) => `/flexi-cards/${id}/transactions`,
  debitCardLimits: (id: string) => `/flexi-cards/${id}/limits`,
};
