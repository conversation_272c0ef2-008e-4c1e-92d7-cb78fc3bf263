import {
  Group,
  Text,
  MantineColor,
  Stack,
  Divider,
  Image,
  Grid,
  Loader,
} from '@mantine/core';
import currency from 'currency.js';
import useTranslation from 'next-translate/useTranslation';
import { PaymentMethodsApiResponse } from '@/store/payment-methods';
import { RatesApiResponse } from '@/store/rate';
import { FeesRatesCalculate } from '@/utils/fees-functions/fees-rate';
import { TranslatedTextValue } from '@/components/common/translated-text-value';

interface RatesProps {
  textColor: MantineColor;
  data: PaymentMethodsApiResponse['data'][0]['depositCurrencies'];
  rates: RatesApiResponse;
  isLoading: boolean;
}

export default function RatesList({
  textColor,
  data,
  rates,
  isLoading,
}: RatesProps) {
  const { t } = useTranslation();

  const rows = data?.map((item) => (
    <div key={item?.id}>
      <Divider mb="md" w="100%" />
      <Grid>
        <Grid.Col span={6}>
          <Group position="left" spacing="xs">
            <Image
              src={item?.image}
              height={35}
              width={35}
              radius={35}
              alt="currency"
            />
            <Text
              color={textColor}
              ta="left"
              miw={50}
              tt="capitalize"
              weight={500}
            >
              <TranslatedTextValue keyEn={item?.label} keyAr={item?.labelAr} />
            </Text>
          </Group>
        </Grid.Col>
        <Grid.Col span={6}>
          <Group position="right" spacing={2}>
            <Text color={textColor} mr={3} weight={500}>
              {rates?.rates && item.code in rates.rates
                ? currency(
                  FeesRatesCalculate(
                    item?.PaymentMethodFeesPercentage,
                    rates?.rates[item.code ? `${item.code}_FROM` : ''],
                  ),
                  { symbol: '', precision: item?.precision ?? 2 },
                ).format()
                : ''}
            </Text>
            <Text weight={500} tt="uppercase" color="dimmed">
              {item?.code}
            </Text>
          </Group>
        </Grid.Col>
      </Grid>
    </div>
  ));

  return (
    <Stack mx="auto">
      <Grid>
        <Grid.Col ta="start" span={6}>
          <Text color={textColor} weight={700}>
            {t('common:currency')}
          </Text>
        </Grid.Col>
        <Grid.Col ta="end" span={6}>
          <Text color={textColor} weight={700}>
            {t('common:price')}
          </Text>
        </Grid.Col>
      </Grid>

      {isLoading ? <Loader my={25} mx="auto" /> : rows}
    </Stack>
  );
}
