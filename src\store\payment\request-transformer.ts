/**
 * create payment request schema
 */
import { z } from 'zod';

export const createPaymentApiRequestSchema = z.object({
  amount: z.number().or(z.string()),
  status: z
    .enum(['pending', 'processing', 'approved', 'rejected'])
    .or(z.string()),
  note: z.string().optional(),
  currencyId: z.string().or(z.number()),
  paymentMethodId: z.string().or(z.number()),
  type: z.enum(['withdraw', 'deposit']).or(z.string()),
  fields: z
    .array(
      z.object({
        type: z.string().nullable().optional(),
        value: z
          .string()
          .or(z.date())
          .or(z.number())
          .or(z.array(z.string()))
          .or(z.object({ id: z.number(), url: z.string(), name: z.string() }))
          .nullable(),
        name: z.string().nullable().optional(),
        nameAr: z.string().nullable().optional(),
      }),
    )
    .optional(),
});

export const createPaymentBackendRequestSchema = createPaymentApiRequestSchema.transform((data) => ({
  data: {
    amount: `${data.amount}`,
    status: data.status,
    note: data?.note?.trim(),
    currency: data.currencyId,
    payment_method: data.paymentMethodId,
    type: data.type,
    custom_fields: data?.fields?.map((i) => {
      if (typeof i?.value === 'string') {
        return {
          ...i,
          value: i.value.trim(),
        };
      }
      if (Array.isArray(i?.value)) {
        return {
          ...i,
          value: i.value.map((item) => item.trim()),
        };
      }
      return i;
    }),
  },
}));
