import SubmitButton from '@/components/common/submit-button';
import {
  Group,
  Modal,
  PinInput,
  Stack,
  Title,
  UnstyledButton,
} from '@mantine/core';
import { IconX } from '@tabler/icons-react';
import React, { ReactNode } from 'react';

interface Props {
  btnText: string;
  title: string;
  codeError: string;
  isLoading: boolean;
  onClick: (v: string) => void;
  openedDefault: boolean;
  setOpenedCode: (v: boolean) => void;
  code: string;
  setCode: (v: string) => void;
  closeable: boolean;
  additionalContent: ReactNode | undefined;
}
function CodeConfirmModal({
  isLoading,
  onClick,
  openedDefault,
  setOpenedCode,
  btnText,
  codeError,
  title,
  code,
  setCode,
  closeable,
  additionalContent,
}: Props) {
  const onCloseModal = () => {
    if (!isLoading && closeable) setOpenedCode(false);
  };
  const onSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    onClick(code);
  };
  return (
    <Modal
      radius="lg"
      opened={openedDefault}
      onClose={onCloseModal}
      withCloseButton={false}
      centered
    >
      <form onSubmit={onSubmit}>
        <Stack>
          <Group position="apart">
            <Title order={5} color="dimmed">
              {title}
            </Title>
            <UnstyledButton onClick={() => setOpenedCode(false)}>
              <IconX color="gray" size="22px" />
            </UnstyledButton>
          </Group>
          <PinInput
            dir="ltr"
            error={!!codeError}
            type="number"
            mx="auto"
            length={6}
            value={code}
            onChange={setCode}
          />
          <SubmitButton
            disabled={!code || code?.length < 6}
            type="submit"
            loading={isLoading}
          >
            {btnText}
          </SubmitButton>
          {additionalContent}
        </Stack>
      </form>
    </Modal>
  );
}

export default CodeConfirmModal;
