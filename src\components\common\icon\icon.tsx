import { toCapitalize } from '@/utils';
import { MantineColor } from '@mantine/core';
import
* as TablerIcons
  from '@tabler/icons-react';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export default function Icon({ icon, size, color }: { icon: string|any, size:number|string, color:MantineColor}) {
  if (typeof (icon) === 'function') {
    const Icons = icon;
    return <Icons style={{ color }} />;
  }
  const keys: string[] = Object.keys(TablerIcons);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let Item: any = TablerIcons.IconApps;
  const dashedIcon = icon && icon.split('-');
  let item = '';
  if (dashedIcon && dashedIcon.length >= 1) {
    dashedIcon.map((i: string) => {
      item += `${toCapitalize(i)}`;
      return item;
    });
    item = `Icon${item}`;
    keys.forEach((key) => {
      if (key === item || key === icon) {
        Item = TablerIcons[key as keyof typeof TablerIcons];
      }
    });
  }

  return <Item style={{ color }} stroke={1.5} size={size} />;
}
