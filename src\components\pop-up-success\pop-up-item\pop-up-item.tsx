import { Group, Text } from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';
import { ReactNode } from 'react';

interface Props {
  name: string;
  content: ReactNode;
  align: 'center' | 'start';
}

export default function Item({ name, content, align }: Props) {
  const { t } = useTranslation();
  return (
    <Group align={align}>
      <Text tt="capitalize" color="dimmed" weight={500} miw={147}>
        {t(`common:${name}`)}
      </Text>
      {content}
    </Group>
  );
}
