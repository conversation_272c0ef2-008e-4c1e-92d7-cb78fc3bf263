const localLocales = ["common", "errors"];
module.exports = {
  locales: ["en", "ar"],
  defaultLocale: "en",
  pages: {
    "*": ["common", "errors", "currency", "badge"],
    "/wallets": ["payment-method"],
    "/wallets/[slug]": ["payment-method"],
    "/withdraw": ["payment-method"],
    "/deposit": ["payment-method"],
    "/mass-payout": ["payment-method"],
    "/rates": ["payment-method"],
    "/history": ["payment-method"],
    "/agent-deposit": ["payment-method"],
    "/iframe/deposit": ["payment-method"],
    "/iframe/history": ["payment-method"],
  },
  loadLocaleFrom: async (lang, ns) => {
    if (localLocales.includes(ns)) {
      const locale = import(`./locales/${lang}/${ns}`).then((v) => v.default);
      return locale;
    }
  },
};
