/**
 * This component renders merchant page.
 */
import MetaTags from '@/components/common/meta-tags';

import { Layout } from '@/components/layout/layout';
import { ApiIntegration } from '@/components/merchant-agent-page/api-integration';
import { FaqSimple } from '@/components/merchant-agent-page/faq';
import { Features } from '@/components/merchant-agent-page/features';
import { MerchantForm } from '@/components/merchant-agent-page/merchant-form';
import { Integration } from '@/components/merchant-agent-page/wallet-integration';

import { WhyWallet } from '@/components/merchant-agent-page/why-wallet';
import { assetBaseUrl } from '@/data';
import { List, Text } from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';
import { useRouter } from 'next/router';

export default function Merchant() {
  const { t } = useTranslation();
  const { locale } = useRouter();
  return (
    <div
      style={{
        backgroundColor: 'rgb(0, 13, 35)',
      }}
    >
      <MetaTags />
      <Layout>
        <WhyWallet
          withImageBackground
          actionLink="#merchant-form"
          title={t('common:whyWalletMerchantTitle')}
          actionTitle={t('common:becomeMerchant')}
          image={`${assetBaseUrl}/assets/merchant/why-wallet.png`}
          description={(
            <>
              <Text color="dimmed" size="xl" weight={500} my="sm">
                {t('common:whyWalletMerchantDescription')}
              </Text>
              <div>
                <Text color="dimmed" size="xl" weight={500} mb="xs">
                  {t('common:whyWalletMerchantListTitle')}
                </Text>
                <List size="lg" spacing="xs" mb="xs">
                  <List.Item c="dimmed">
                    {' '}
                    {t('common:whyWalletMerchantListItem1')}
                  </List.Item>
                  <List.Item c="dimmed">
                    {' '}
                    {t('common:whyWalletMerchantListItem2')}
                  </List.Item>
                  <List.Item c="dimmed">
                    {t('common:whyWalletMerchantListItem3')}
                  </List.Item>
                </List>
              </div>
            </>
          )}
        />
        <Integration
          withImageBackground
          title={t('common:integrationMerchantTitle')}
          image={`${assetBaseUrl}/assets/merchant/wallet-integration.png`}
          description={t('common:integrationMerchantDescription')}
          actions={[
            {
              title: t('common:howToInstall'),
              isBlank: true,
              link: `https://kazawallet.trengohelp.com/${
                locale === 'ar' ? 'ar' : 'en'
              }/articles/417944-how-to-install-kazawallet-wordpress-plugin`,
            },
            {
              title: t('common:downloadPlugin'),
              isBlank: true,
              link: 'https://www.kazawallet.com/wordpress/kazawallet.zip',
            },
          ]}
        />
        <Features
          benefits={[
            {
              icon: 'file-invoice',
              title: t('common:featuresMerchantFeat1Title'),
              description: t('common:featuresMerchantFeat1Description'),
            },
            {
              icon: 'credit-card',
              title: t('common:featuresMerchantFeat2Title'),
              description: t('common:featuresMerchantFeat2Description'),
            },
            {
              icon: 'arrows-exchange',
              title: t('common:featuresMerchantFeat3Title'),
              description: t('common:featuresMerchantFeat3Description'),
            },
            {
              icon: 'cash-banknote',
              title: t('common:featuresMerchantFeat4Title'),
              description: t('common:featuresMerchantFeat4Description'),
            },
          ]}
        />
        <ApiIntegration />
        <MerchantForm />
        <FaqSimple
          list={[
            {
              question: t('common:faq1'),
              answer: t('common:answer1'),
            },
            {
              question: t('common:faq2'),
              answer: t('common:answer2'),
            },
            {
              question: t('common:faq3'),
              answer: t('common:answer3'),
            },
            {
              question: t('common:faq4'),
              answer: t('common:answer4'),
            },
            {
              question: t('common:faq5'),
              answer: t('common:answer5'),
            },
            {
              question: t('common:faq6'),
              answer: t('common:answer6'),
            },
          ]}
        />
      </Layout>
    </div>
  );
}
