/* eslint-disable complexity */
/* eslint-disable max-lines */
/* eslint-disable sonarjs/cognitive-complexity */
import {
  UpdateUserApiRequest,
  updateUserKYCFormSchema,
  updateUserMutation,
  UserApiResponse,
} from '@/store/user';
import {
  Alert,
  Box,
  Button,
  Checkbox,
  Group,
  Image,
  Loader,
  Paper,
  Stack,
  Text,
  TextInput,
  useMantineTheme,
  SegmentedControl,
  Center,
  Anchor,
} from '@mantine/core';
import { useForm, zodResolver } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import useTranslation from 'next-translate/useTranslation';
import React, { useState } from 'react';
import { useRouter } from 'next/router';
import SelectWithFlags from '../select-with-flags/select-with-flags';
import { countries } from 'countries-list';
import { NewDropzoneWithMutate } from '@/components/new-dropzone';
import { FileType } from '@/components/new-dropzone/new-dropzone';
import { ReturnKYCBadge } from './return-kyc-tag';
import UploadNoteModal from './upload-note-modal';
import {
  IconAlertCircle, IconBook, IconCircleCheck, IconCircleOff, IconVideo,
} from '@tabler/icons-react';
import Link from 'next/link';
import {
  assetBaseUrl, kycBlogArUrl, kycBlogEnUrl, ROUTES, kycVideoArUrl, kycVideoEnUrl,
} from '@/data';
import { KYC_STATUS } from '@/store/user/response-transformer';
import { preserveUserData } from '@/utils/profile-utils';

interface Props {
  data: UserApiResponse | undefined;
}

function KycTab({ data }: Props) {
  const { t } = useTranslation();
  const theme = useMantineTheme();
  const queryClient = useQueryClient();
  const { locale } = useRouter();
  const [id1, setId1] = useState<FileType>();
  const [id2, setId2] = useState<FileType>();
  const [selfie, setSelfie] = useState<FileType>();

  const id1Key = 'kyc.id1';
  const id2Key = 'kyc.id2';
  const selfieKey = 'kyc.selfie';
  const idFrontString = 'common:idFront';
  const idBackString = 'common:idBack';
  const selfieString = 'common:selfie';
  const form = useForm<UpdateUserApiRequest>({
    initialValues: {
      kyc: {
        status: KYC_STATUS.Pending,
        agreetc: data?.kyc?.agreetc ?? false,
        country: data?.kyc?.country ?? '',
        id1: data?.kyc?.id1 ?? '',
        id2: data?.kyc?.id2 ?? '',
        mobile: data?.kyc?.mobile ?? '',
        preferredCommunicationMethod: (data?.kyc?.preferredCommunicationMethod as 'telegram' | 'whatsapp') ?? 'telegram',
        selfie: data?.kyc?.selfie ?? '',
      },
    },
    validate: zodResolver(updateUserKYCFormSchema(t)),
  });

  const { mutate, isLoading } = useMutation(updateUserMutation().mutationFn, {
    onSuccess() {
      queryClient.invalidateQueries(['user']);
      notifications.show({
        message: t('common:kycUpdateSuccess'),
        color: 'blue',
      });
    },
  });

  const submit = (values: UpdateUserApiRequest) => {
    if (values.kyc) {
      mutate(preserveUserData({
        kyc: {
          ...values.kyc,
          mobile:
            countries[form.values?.kyc?.country as 'AD'].phone
            + values.kyc.mobile,
        },
      }, data));
    }
  };
  return (
    <Paper p="md" mt="lg" pb="xl" withBorder radius="lg">
      <Group>
        <Text fw={500}>{t('common:KYC')}</Text>
        <ReturnKYCBadge kyc={data?.kyc} />
        <Group spacing="xl" position="center">
          <Anchor
            href={locale === 'ar' ? kycVideoArUrl : kycVideoEnUrl}
            target="_blank"
            rel="noopener noreferrer"
            sx={() => ({
              display: 'flex',
              alignItems: 'center',
              gap: theme.spacing.xs,
              fontWeight: 500,
              textDecoration: 'none',
              color: theme.colorScheme === 'dark'
                ? theme.colors.blue[4]
                : theme.colors.blue[6],
              '&:hover': {
                textDecoration: 'underline',
              },
            })}
          >
            <IconVideo size={18} />
            <Text size="sm" weight={500}>
              {t('common:kycWatchVideo')}
            </Text>
          </Anchor>

          <Anchor
            href={locale === 'ar' ? kycBlogArUrl : kycBlogEnUrl}
            target="_blank"
            rel="noopener noreferrer"
            sx={() => ({
              display: 'flex',
              alignItems: 'center',
              gap: theme.spacing.xs,
              fontWeight: 500,
              textDecoration: 'none',
              color: theme.colorScheme === 'dark'
                ? theme.colors.blue[4]
                : theme.colors.blue[6],
              '&:hover': {
                textDecoration: 'underline',
              },
            })}
          >
            <IconBook size={18} />
            <Text size="sm" weight={500}>
              {t('common:kycStepByStepGuide')}
            </Text>
          </Anchor>
        </Group>
      </Group>
      {data?.kyc?.adminNote && (
        <Alert
          mt="xl"
          icon={<IconAlertCircle size="1rem" />}
          title={t('common:attention')}
          color="red"
        >
          <Text color="error">{data.kyc.adminNote}</Text>
        </Alert>
      )}
      {data?.kyc?.status === KYC_STATUS.Approved && (
        <Stack mih={300} align="center" justify="center">
          <IconCircleCheck size={80} color="gray" />
          <Text size="lg">{t('common:approvedNote')}</Text>
        </Stack>
      )}
      {data?.kyc?.status === KYC_STATUS.Disabled && (
        <Stack mih={300} align="center" justify="center">
          <IconCircleOff size={80} color="gray" />
          <Text size="lg">{t('common:disabledNote')}</Text>
        </Stack>
      )}
      <form onSubmit={form.onSubmit(submit)}>
        {data?.kyc?.status !== KYC_STATUS.Approved
          && data?.kyc?.status !== KYC_STATUS.Disabled && (
            <>
              <Group mb={20} position="apart" mt="lg">
                <SelectWithFlags
                  readOnly={data?.kyc?.status === KYC_STATUS.Pending}
                  clearable
                  miw={250}
                  w={{ base: '100%', sm: '48%' }}
                  radius="lg"
                  data={countries}
                  label={<Text tt="capitalize">{t('common:country')}</Text>}
                  placeholder={t('common:pickOne') as string}
                  priorityList={['US', 'CA', 'GB', 'DE', 'RU']}
                  rightSection={!countries && <Loader size="xs" />}
                  disabled={isLoading}
                  {...form.getInputProps('kyc.country')}
                />
                <TextInput
                  readOnly={data?.kyc?.status === KYC_STATUS.Pending}
                  disabled={isLoading || !form.values?.kyc?.country}
                  miw={250}
                  w={{ base: '100%', sm: '48%' }}
                  radius="lg"
                  icon={(
                    <Text
                      size="sm"
                      c={theme.colorScheme === 'dark' ? '#C1C2C5' : 'dark'}
                    >
                      {form.values?.kyc?.country
                        ? `+${
                          countries[form.values?.kyc?.country as 'AD'].phone
                        }`
                        : ''}
                    </Text>
                  )}
                  iconWidth={40}
                  label={<Text tt="capitalize">{t('common:mobile')}</Text>}
                  placeholder={`${t('common:mobile')}`}
                  {...form.getInputProps('kyc.mobile')}
                />
              </Group>
              <Group mb={5} w="100%" align="end" position="apart">
                <Box w="100%">
                  <Text size="sm" weight={500} mb="xs">
                    {t('common:preferredCommunicationMethod')}
                  </Text>
                  <SegmentedControl
                    value={form.values.kyc?.preferredCommunicationMethod || 'telegram'}
                    onChange={(value) => form.setFieldValue('kyc.preferredCommunicationMethod', value)}
                    data={[
                      { label: 'Telegram', value: 'telegram' },
                      { label: 'WhatsApp', value: 'whatsapp' },
                    ]}
                    fullWidth
                    disabled={data?.kyc?.status === KYC_STATUS.Pending || isLoading}
                  />
                </Box>
              </Group>
            </>
        )}
        {data?.kyc?.status === KYC_STATUS.Pending && (
          <Stack mt="xl">
            <Box>
              <Text mb="md">{t(idFrontString)}</Text>
              <Center>
                <Image maw={400} src={data?.kyc?.id1} />
              </Center>
            </Box>
            <Box>
              <Text mb="md">{t(idBackString)}</Text>
              <Center>
                <Image maw={400} src={data?.kyc?.id2} />
              </Center>
            </Box>
            <Box>
              <Text mb="md">{t(selfieString)}</Text>
              <Center>
                <Image maw={400} src={data?.kyc?.selfie} />
              </Center>
            </Box>
          </Stack>
        )}
        {(!data?.kyc?.status || data?.kyc?.status === KYC_STATUS.Rejected) && (
          <>
            <Stack mt="xl">
              <Box>
                <Group mb="xs">
                  <Text>{t(idFrontString)}</Text>
                  <UploadNoteModal
                    image={`${assetBaseUrl}/assets/icons/front.png`}
                    message={t('common:idFrontNote')}
                  />
                </Group>
                <NewDropzoneWithMutate
                  iconSrc={`${assetBaseUrl}/assets/icons/front.png`}
                  iconSize={150}
                  dropzoneProps={{
                    multiple: false,
                    sx: { cursor: 'pointer' },
                  }}
                  onUploadFinish={(file) => {
                    setId1(file[0]);
                    form.setFieldValue(id1Key, file[0].url);
                  }}
                  title={t(idFrontString)}
                  onDelete={() => {
                    form.setFieldValue(id1Key, '');
                    setId1(undefined);
                  }}
                  error={form?.errors[id1Key] as string}
                  deleteAllSelectedFiles={form.values?.kyc?.id1 === ''}
                  description={['']}
                  files={
                    form.values?.kyc?.id1 !== '' && id1 ? [id1] : undefined
                  }
                />
                <UploadNoteModal
                  image={`${assetBaseUrl}/assets/icons/front.png`}
                  message={t('common:idFrontNote')}
                  showAsModal={false}
                />
              </Box>
              <Box>
                <Group mb="xs">
                  <Text>{t(idBackString)}</Text>
                  <UploadNoteModal
                    image={`${assetBaseUrl}/assets/icons/back.png`}
                    message={t('common:idBackNote')}
                  />
                </Group>
                <NewDropzoneWithMutate
                  iconSrc={`${assetBaseUrl}/assets/icons/back.png`}
                  iconSize={150}
                  dropzoneProps={{
                    multiple: false,
                    sx: { cursor: 'pointer' },
                  }}
                  onUploadFinish={(file) => {
                    setId2(file[0]);
                    form.setFieldValue(id2Key, file[0].url);
                  }}
                  title={t(idBackString)}
                  onDelete={() => {
                    form.setFieldValue(id2Key, '');
                    setId2(undefined);
                  }}
                  error={form?.errors[id2Key] as string}
                  deleteAllSelectedFiles={form.values?.kyc?.id2 === ''}
                  description={['']}
                  files={
                    form.values?.kyc?.id2 !== '' && id2 ? [id2] : undefined
                  }
                />
                <UploadNoteModal
                  image={`${assetBaseUrl}/assets/icons/back.png`}
                  message={t('common:idBackNote')}
                  showAsModal={false}
                />
              </Box>
              <Box>
                <Group mb="xs">
                  <Text>{t(selfieString)}</Text>
                  <UploadNoteModal
                    image={`${assetBaseUrl}/assets/icons/selfie.png`}
                    message={t('common:idSelfieNote')}
                  />
                </Group>
                <NewDropzoneWithMutate
                  iconSrc={`${assetBaseUrl}/assets/icons/selfie.png`}
                  iconSize={150}
                  dropzoneProps={{
                    multiple: false,
                    sx: { cursor: 'pointer' },
                  }}
                  onUploadFinish={(file) => {
                    setSelfie(file[0]);
                    form.setFieldValue(selfieKey, file[0].url);
                  }}
                  title={t(selfieString)}
                  onDelete={() => {
                    form.setFieldValue(selfieKey, '');
                    setSelfie(undefined);
                  }}
                  error={form?.errors[selfieKey] as string}
                  deleteAllSelectedFiles={form.values?.kyc?.selfie === ''}
                  description={['']}
                  files={
                    form.values?.kyc?.selfie !== '' && selfie
                      ? [selfie]
                      : undefined
                  }
                />
                <UploadNoteModal
                  image={`${assetBaseUrl}/assets/icons/selfie.png`}
                  message={t('common:idSelfieNote')}
                  showAsModal={false}
                />
              </Box>
            </Stack>

            <Checkbox
              mt="lg"
              label={(
                <Group spacing={2}>
                  <Text>{t('common:agreeOn')}</Text>
                  <Text
                    weight="bold"
                    component={Link}
                    href={ROUTES.termsOfService.path}
                    color={theme.colorScheme === 'dark' ? 'dimmed' : 'primary'}
                  >
                    {` ${t('common:termsAndConditions')}`}
                  </Text>
                </Group>
              )}
              {...form.getInputProps('kyc.agreetc')}
            />
            <Text mt="lg" color="red">
              {t('common:note')}
              :
            </Text>
            <Text maw={800} mt="xs">
              {t('common:kycNote')}
            </Text>
            <Button
              loading={isLoading}
              mt={20}
              type="submit"
              fullWidth
              radius="lg"
            >
              {t('common:save')}
            </Button>
          </>
        )}
      </form>
    </Paper>
  );
}

export default KycTab;
