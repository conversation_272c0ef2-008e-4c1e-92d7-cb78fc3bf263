/**
 * There are two handlers: get user data and update user data.
 */
import { apiEndpoints, apiMethods, httpCode } from '@/data';
import { BackendClient } from '@/lib';
import { returnUserParams, userApiResponseSchema } from '@/store/user';
import { updateUserBackendRequestSchema } from '@/store/user/request-transformer';
import createApiError from '@/utils/api-utils/create-api-error';
import createApiResponse from '@/utils/api-utils/create-api-response';
import { NextApiRequest, NextApiResponse } from 'next';
import { getJwt } from '@/utils/api-utils/jwt';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token } = await getJwt(req);
  // this function to get user data
  if (req.method === apiMethods.GET) {
    try {
      const { data } = await BackendClient(req).get(
        `${apiEndpoints.users()}/me`,
        {
          headers: {
            authorization: token,
          },
          params: {
            ...returnUserParams(),
          },
        },
      );

      return createApiResponse(res, userApiResponseSchema, { data });
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
    // this function to update user data
    // return updated user data
  } else if (req.method === apiMethods.PUT) {
    try {
      const { data } = await BackendClient(req).put(
        apiEndpoints.users(),
        updateUserBackendRequestSchema.parse(req.body),
        {
          headers: {
            authorization: token,
          },
        },
      );
      return res
        .status(httpCode.SUCCESS)
        .json(userApiResponseSchema.parse({ data }));
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }
  const error = createApiError({ error: '' });
  return res.status(httpCode.INTERNAL_SERVER_ERROR).json(error);
}

export default handler;
