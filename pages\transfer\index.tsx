/**
 * This component renders a Transfer page.
 *
 * @description
 * This page used to create transfer type "transfer".
 * There is a select to choose currency and complete transfer form data.
 * The select data currencies are user currencies.
 * When success transfer will display a popup to show transfer details.
 * If we have a "curr" query in page route, then the default currency select item must be same the currency in the page route.
 */
import MetaTags from '@/components/common/meta-tags';
import { Layout } from '@/components/layout/layout';
import {
  Text, Stack, Loader, Paper,
} from '@mantine/core';
import { useRouter } from 'next/router';
import React, { useState } from 'react';
import useTranslation from 'next-translate/useTranslation';

import { useQuery } from '@tanstack/react-query';
import { UserApiResponse, getUserQuery } from '@/store/user';
import MyBalanceSkeleton from '@/components/balance/my-balance/my-balance-skeleton';

import CurrencyTitleSkeleton from '@/components/balance/currency-title-skeleton';
import { MyBalance } from '@/components/balance/my-balance';
import { PageTitle } from '@/components/common/page-title';

import { TransferForm } from '@/components/transfer/transfer-form';
import { getRatesQuery } from '@/store/rate';
import { SelectCurrencyCard } from '@/components/currency/select-currency-card';

function Transfer() {
  const router = useRouter();
  const { query } = router;
  const { t } = useTranslation();
  const { data: ratesData } = useQuery(getRatesQuery());

  const [selectedItem, setSelectedItem] = useState<UserApiResponse['currencies'][0]>();

  const handleSelectCurrency = (v: UserApiResponse['currencies'][0]) => {
    setSelectedItem(v);
    router.query.currency = v.uid;
    router.push(router);
  };
  // Call user data to get user currencies
  const { data, isLoading } = useQuery({
    ...getUserQuery({}),
    refetchOnWindowFocus: false,
    onSuccess(res) {
      // get user currencies.
      let currenciesItems = res?.currencies;
      // set "currenciesItems" is selected currency if user select another currency after filter user currencies
      //  by selectedItem.
      if (selectedItem) {
        currenciesItems = res?.currencies?.filter(
          (i) => i?.value === selectedItem?.value,
        );
        // set selected item after get it from user currencies response.
        handleSelectCurrency(currenciesItems[0]);
      }
    },
  });
  // Initial currency if there is a query in page route.
  const initCurrency = query?.currency
    ? data?.currencies?.filter((i) => i.uid === query?.currency)
    : data?.currencies;
  const item = selectedItem ?? (initCurrency && initCurrency[0]);
  // Currency precision
  const precision = item?.precision ?? 2;
  // determine if currency is crypto
  const isCrypto = ['crypto', 'crypto_stable'].includes(item?.type as string);
  let rateKey = '';
  if (item?.code) {
    rateKey = isCrypto ? `${item.code}_TO` : `${item.code}_FROM`;
  }
  // get currency rate from rates list by currency code
  const currencyRate = ratesData?.rates ? ratesData?.rates[rateKey] : '';

  return (
    <div>
      <MetaTags title={t('common:transfer')} />
      <Layout>
        <PageTitle title="transfer" />
        <Stack align="center">
          {item?.label ? (
            <Text tt="capitalize" size="lg" weight={500}>
              {t('common:selectCurrency')}
            </Text>
          ) : (
            <CurrencyTitleSkeleton />
          )}
          <Paper withBorder py="xs" px="md" radius={20}>
            <SelectCurrencyCard
              type="popup"
              data={data?.currencies ?? []}
              setSelectedItem={handleSelectCurrency}
              selectedItem={item}
            >
              {isLoading ? (
                <MyBalanceSkeleton />
              ) : (
                <MyBalance
                  precision={precision}
                  icon={item?.image}
                  balance={item?.amount ? +item.amount : 0}
                  symbol={undefined}
                  symbolCurrency={item?.symbol}
                  currency={item?.id}
                />
              )}
            </SelectCurrencyCard>
          </Paper>
          <Text tt="capitalize" weight={500} color="dimmed" size="lg">
            {t('common:myBalance')}
          </Text>
        </Stack>
        <Stack mt="md" mx="auto" maw={800}>
          {isLoading ? (
            <Loader mx="auto" mt={100} />
          ) : (
            <TransferForm
              currencyPrecision={precision}
              rate={currencyRate}
              balanceAmount={item?.amount ? +item.amount : 0}
              currencyId={item?.value ?? ''}
              feesProps={{
                feesFixed: item?.transferFeesFixed,
                feesPercentage: item?.transferFeesPercent,
                feesMin: item?.transferFeesMin,
                feesMax: item?.transferFeesMax,
              }}
              transferMin={item?.transferMin}
              transferMax={item?.transferMax}
              currencyItem={item}
            />
          )}
        </Stack>
      </Layout>
    </div>
  );
}

export default Transfer;

export async function getServerSideProps() {
  return { props: {} };
}
