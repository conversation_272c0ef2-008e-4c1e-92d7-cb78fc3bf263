import {
  Container,
  Group,
  Tooltip,
  ActionIcon,
  Stack,
  Text,
  useMantineTheme,
  Flex,
} from '@mantine/core';
import { ROUTES } from '@/data';
import { useRouter } from 'next/router';

import { useStyles } from './style';
import useTranslation from 'next-translate/useTranslation';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { getTotalBalanceQuery } from '@/store/user';
import MyBalanceSkeleton from '@/components/balance/my-balance/my-balance-skeleton';
import { operationIcon } from '@/utils/operations-utils/operation-icons';
import { MyBalance } from '../balance/my-balance';
import { Icon } from '../common/icon';
// import { useDisclosure } from '@mantine/hooks';
// import { QRModal } from '@/components/common/qr-modal';

enum queryKeys {
  balance = 'balance',
  user = 'user',
}

// type WalletsHeroProps = {
//   accountId: string | undefined;
// }

export default function WalletsHero() {
  // const { accountId } = props;
  const { classes } = useStyles();
  const { t } = useTranslation();
  const { push, locale } = useRouter();
  const isRTL = locale === 'ar';
  // const [opened, { open: openQrModal, close: closeQrModal }] = useDisclosure(false);
  const totalBalance = useQuery(getTotalBalanceQuery());
  const theme = useMantineTheme();
  const queryClient = useQueryClient();
  const onRefreshClick = () => {
    queryClient.invalidateQueries({
      queryKey: [queryKeys.balance],
    });
    queryClient.invalidateQueries({
      queryKey: [queryKeys.user],
    });
  };
  return (
    <Container className={classes.wrapper} size={1400}>
      <Flex
        w="100%"
        align="center"
        gap="xs"
        direction="row"
        justify={isRTL ? 'flex-start' : 'flex-end'}
        sx={() => ({
          [theme.fn.smallerThan('sm')]: {
            justifyContent: 'space-between',
          },
        })}
      >
        <Group spacing="xs">
          <Tooltip label={t('common:refresh')}>
            <ActionIcon loading={totalBalance.isFetching} onClick={onRefreshClick}>
              <Icon icon="reload" size="1.8rem" color="dark" />
            </ActionIcon>
          </Tooltip>

          <Tooltip label={t('common:history')}>
            <ActionIcon onClick={() => push(ROUTES.history.path)}>
              <Icon icon="history" size="1.8rem" color="dark" />
            </ActionIcon>
          </Tooltip>
        </Group>

        {/* <Tooltip label={t('common:qrScanner')}>
          <ActionIcon
            onClick={openQrModal}
            sx={{ order: isRTL ? -1 : 1, flexShrink: 0 }}
          >
            <Icon icon={operationIcon({ operationType: 'scan' })} size="1.8rem" color="dark" />
          </ActionIcon>
        </Tooltip> */}
      </Flex>

      <Stack align="center" spacing={2}>
        {totalBalance?.isLoading ? (
          <MyBalanceSkeleton />
        ) : (
          <MyBalance
            precision={2}
            icon={undefined}
            balance={totalBalance?.data?.total}
            symbol="$"
            symbolCurrency={undefined}
            currency={1}
          />
        )}
        <Text size="lg" weight={500}>
          {t('common:totalBalance')}
          {' '}
          ≈
          {' '}
        </Text>

        <Group mt="md" my="sm" spacing="xl" position="center">
          <Tooltip label={t('common:withdraw')} withArrow>
            <ActionIcon
              onClick={() => push(ROUTES.withdraw.path)}
              color="primary"
              w={45}
              h={45}
              radius={50}
              variant="subtle"
            >
              <Icon
                icon={operationIcon({ operationType: 'withdraw' })}
                size="45"
                color={theme.colorScheme === 'dark' ? 'gray' : ''}
              />
            </ActionIcon>
          </Tooltip>
          <Tooltip label={t('common:transfer')} withArrow>
            <ActionIcon
              onClick={() => push(ROUTES.transfer.path)}
              color="primary"
              w={55}
              h={55}
              radius={55}
              variant="subtle"
            >
              <Icon
                icon={operationIcon({ operationType: 'transfer' })}
                size="55"
                color={theme.colorScheme === 'dark' ? 'gray' : ''}
              />
            </ActionIcon>
          </Tooltip>
          <Tooltip label={t('common:deposit')} withArrow>
            <ActionIcon
              onClick={() => push(ROUTES.deposit.path)}
              color="primary"
              w={45}
              h={45}
              radius={50}
              variant="subtle"
            >
              <Icon
                icon={operationIcon({ operationType: 'deposit' })}
                size="45"
                color={theme.colorScheme === 'dark' ? 'gray' : ''}
              />
            </ActionIcon>
          </Tooltip>
        </Group>
      </Stack>
      {/* <QRModal
        opened={opened}
        onClose={closeQrModal}
        accountId={accountId}
        mode="both"
        showGenerateButton
        showCloseButton
      /> */}
    </Container>
  );
}
