import React from 'react';

import currency from 'currency.js';
import { Group, Text } from '@mantine/core';
import { Item } from './pop-up-item';

interface Props {
  currencyCode: string | undefined;
  currencyToCode: string | undefined;
  currencySymbol: string | null | undefined;
  currencyToSymbol: string | null | undefined;
  amount: number;
  actualAmount: number;
  precision: number;
  currencyToPrecision: number;
  fees: number;
  rate: number;
}

function ExchangeDetails({
  currencyCode,
  currencyToCode,
  currencySymbol,
  currencyToSymbol,
  amount,
  actualAmount,
  precision,
  currencyToPrecision,
  fees,
  rate,
}: Props) {
  const amountValue = currency(amount, {
    symbol: '',
    precision,
  }).format();
  const actualAmountValue = currency(actualAmount, {
    symbol: '',
    precision: currencyToPrecision,
  }).format();
  const feesValue = currency(fees, {
    symbol: '',
    precision: currencyToPrecision,
  }).format();
  return (
    <>
      <Item
        align="center"
        name="from"
        content={(
          <Group miw={100} position="apart">
            <Text weight={500} color="red" span>
              {amountValue}
            </Text>
            <Text color="red" weight={500} span>
              {currencyCode}
            </Text>
          </Group>
        )}
      />
      <Item
        align="center"
        name="to"
        content={(
          <Group miw={100} position="apart">
            <Text weight={500} color="green" span>
              {actualAmountValue}
            </Text>
            <Text color="green" weight={500} span>
              {currencyToCode}
            </Text>
          </Group>
        )}
      />
      <Item
        align="center"
        name="fees"
        content={(
          <Group miw={100} position="apart">
            <Text weight={500} span>
              {feesValue}
            </Text>
            <Text color="dimmed" weight={500} span>
              {currencyToCode}
            </Text>
          </Group>
        )}
      />
      <Item
        align="center"
        name="rate"
        content={(
          <Group spacing={2}>
            <div>
              <Text weight={500} size="sm" span>
                1
              </Text>
              <Text weight={500} size="sm" mx={2} span>
                {currencySymbol}
              </Text>
              =
            </div>
            <div>
              <Text weight={500} size="sm" span>
                {rate}
              </Text>
              <Text weight={500} size="sm" mx={2} span>
                {currencyToSymbol}
              </Text>
            </div>
          </Group>
        )}
      />
    </>
  );
}

export default ExchangeDetails;
