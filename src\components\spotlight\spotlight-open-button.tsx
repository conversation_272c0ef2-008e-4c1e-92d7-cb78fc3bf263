import { Group, Kbd, UnstyledButton } from '@mantine/core';
import React from 'react';
import { Icon } from '../common/icon';
import { spotlight } from '@mantine/spotlight';

function SpotlightOpenButton({ isAuth }: { isAuth: boolean }) {
  return isAuth ? (
    <UnstyledButton onClick={() => spotlight.open()}>
      <Group align="center" position="center" spacing="xs">
        <Icon icon="search" size={20} color="gray" />
        <div>
          <Kbd>Ctrl+K</Kbd>
        </div>
      </Group>
    </UnstyledButton>
  ) : (
    <div />
  );
}

export default SpotlightOpenButton;
