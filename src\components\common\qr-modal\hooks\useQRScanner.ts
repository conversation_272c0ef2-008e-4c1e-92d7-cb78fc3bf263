/* eslint-disable prefer-destructuring */
import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import useTranslation from 'next-translate/useTranslation';
import { DetectedBarcode, processImageForQR } from '../qr-helpers';
import { QR_CONFIG } from '../qr-constants';
import { validateQRData, getQRValidationErrorMessage } from '@/utils/qr-validation';
import { ROUTES } from '@/data';

export const useQRScanner = (opened: boolean, closeQrModal: () => void) => {
  const [isScanning, setIsScanning] = useState(true);
  const [showScanner, setShowScanner] = useState(false);
  const [scanError, setScanError] = useState<Error | null>(null);

  const router = useRouter();
  const { t } = useTranslation();

  useEffect(() => {
    let timer: ReturnType<typeof setTimeout> | null = null;
    if (opened && isScanning) {
      timer = setTimeout(() => {
        setShowScanner(true);
        setScanError(null);
      }, QR_CONFIG.SCANNER_DELAY);
    } else {
      setShowScanner(false);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [opened, isScanning]);

  const onScanComplete = async (detectedCodes: DetectedBarcode[]) => {
    setScanError(null);

    if (!detectedCodes?.length || !detectedCodes[0].rawValue) {
      setScanError(new Error(t('errors:invalidScanResult')));
      return;
    }

    const scannedData = detectedCodes[0].rawValue;

    // Use the unified validation function
    const validationResult = validateQRData(scannedData);

    if (!validationResult.isValid) {
      const errorMsg = getQRValidationErrorMessage(
        validationResult.errorKey || 'errors:invalidQrCodeFormatInvalidUrl',
        t,
      );
      setScanError(new Error(errorMsg));
      return;
    }

    // Navigate to transfer page with the validated account ID
    const accountId = validationResult.accountId!;
    setScanError(null);
    setShowScanner(false);
    closeQrModal();
    router.push(`${ROUTES.transfer.path}?accountId=${accountId}`);
  };

  const mapCameraErrorKey = (name: string, msg: string): string => {
    const n = name.toLowerCase();
    const m = msg.toLowerCase();

    if (n === 'notallowederror' || /permission denied|not allowed/.test(m)) return 'errors:cameraPermissionDenied';

    if (n === 'notfounderror' || /no camera|not found/.test(m)) return 'errors:cameraNotFound';

    if (n === 'notreadableerror' || /in use|being used/.test(m)) return 'errors:cameraInUse';

    if (n === 'overconstrainederror') return 'errors:cameraNotAvailable';

    if (/camera|video|media/.test(m)) return 'errors:cameraAccessError';

    return 'errors:unknownScannerError';
  };

  const onScanError = (err: unknown) => {
    if (!(err instanceof Error)) {
      setScanError(new Error(t('errors:unknownScannerError')));
      return;
    }

    const key = mapCameraErrorKey(err.name ?? '', err.message ?? '');
    setScanError(new Error(t(key)));
  };

  const handleImageUpload = async (file: File | null) => {
    if (!file) return;

    setScanError(null);

    await processImageForQR(
      file,
      async (codes) => {
        await onScanComplete(codes);
      },
      (error) => {
        const errorKey = error.message.startsWith('errors:') ? error.message : `errors:${error.message}`;
        const translatedMessage = t(errorKey);
        const finalMessage = translatedMessage === errorKey ? t('errors:failedToProcessImage') : translatedMessage;
        setScanError(new Error(finalMessage));
      },
    );
  };

  const resetToScanner = () => {
    setIsScanning(true);
    setScanError(null);
  };

  return {
    isScanning,
    showScanner,
    scanError,
    onScanComplete,
    onScanError,
    handleImageUpload,
    resetToScanner,
    setIsScanning,
    setScanError,
  };
};
