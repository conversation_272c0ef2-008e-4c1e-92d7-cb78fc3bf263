/**
 * agent payment response schema
 */

import { z } from 'zod';
import { imageBackedSchema } from '../currencies/responses-transformers';
import {
  customFieldsApiSchema,
  customFieldsSchema,
} from '../payment-methods/response-transformer';
/** ******************************************** */
// custom fields schema
const paymentCustomFieldsSchema = z
  .object({
    type: z.string().nullable().optional(),
    value: z
      .string()
      .or(z.date())
      .or(z.number())
      .or(z.object({ id: z.number(), url: z.string(), name: z.string() }))
      .or(z.any())
      .nullable(),
    name: z.string().nullable().optional(),
    name_ar: z.string().nullable().optional(),
  })
  .optional();
const paymentCustomFieldsApiSchema = (
  i: z.infer<typeof paymentCustomFieldsSchema>,
) => ({
  name: i?.name,
  nameAr: i?.name_ar,
  type: i?.type,
  value: i?.value,
});
/** ******************************************** */
// agent payment backend schema
export const paymentAgentBackendResponseSchema = z.object({
  id: z.number(),
  transaction_id: z.string(),
  actual_amount: z.string(),
  custom_fields: z.array(paymentCustomFieldsSchema).nullable(),
  status: z.enum(['approved', 'rejected']),
  type: z.enum(['withdraw']),
  currency: z.object({
    id: z.number().optional(),
    name: z.string(),
    name_ar: z.string().nullable(),
    symbol: z.string().nullable(),
    code: z.string(),
    precision: z.number().nullable(),
    icon: imageBackedSchema,
  }),
  payment_method: z.object({
    deposit_fees_fixed: z.string(),
    deposit_fees_percentage: z.string(),
    deposit_fees_min: z.string(),
    deposit_fees_max: z.string().nullable(),
    deposit_custom_fields: z.array(customFieldsSchema).optional(),
  }),
});

// agent payment front end schema
export const paymentAgentApiResponseSchema = (
  item: z.infer<typeof paymentAgentBackendResponseSchema>,
) => ({
  id: item.id,
  transactionId: item.transaction_id,
  actualAmount: +item.actual_amount,
  fields: item?.custom_fields?.map((i) => paymentCustomFieldsApiSchema(i)),
  currency: {
    label: item.currency.name,
    labelAr: item.currency?.name_ar,
    symbol: item.currency.symbol,
    code: item.currency.code,
    precision: item.currency.precision,
    image:
      item.currency.icon?.formats?.thumbnail.url ?? item.currency.icon?.url,
    type: item.type,
  },
  paymentMethod: {
    depositFeesFixed: +item.payment_method.deposit_fees_fixed,
    depositFeesPercentage: +item.payment_method.deposit_fees_percentage,
    depositFeesMin: +item.payment_method.deposit_fees_min,
    depositFeesMax: item.payment_method.deposit_fees_max
      ? +item.payment_method.deposit_fees_max
      : null,
    depositCustomFields: item.payment_method.deposit_custom_fields?.map((i) => customFieldsApiSchema(i)),
  },
});

export const agentPaymentApiResponseSchema = z
  .object({
    data: paymentAgentBackendResponseSchema,
  })
  .transform(({ data }) => paymentAgentApiResponseSchema(data));
