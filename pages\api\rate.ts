/**
 * This handler to get rates.
 */
import { apiEndpoints, httpCode } from '@/data';
import { BackendClient } from '@/lib';
import { ratesApiResponseSchema } from '@/store/rate';
import createApiError from '@/utils/api-utils/create-api-error';
import createApiResponse from '@/utils/api-utils/create-api-response';

import { NextApiRequest, NextApiResponse } from 'next';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'GET') {
    try {
      const { data } = await BackendClient(req).get(apiEndpoints.rate());

      return createApiResponse(res, ratesApiResponseSchema, { data });
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }
  const error = createApiError({ error: '' });
  return res.status(httpCode.INTERNAL_SERVER_ERROR).json(error);
}

export default handler;
