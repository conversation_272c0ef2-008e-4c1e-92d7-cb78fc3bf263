/**
 * mass payout response schema
 */
import { z } from 'zod';

export const massPayoutBackendResponseSchema = z.array(
  z.object({
    account: z.string(),
    success: z.boolean(),
    amount: z.string(),
    notes: z.string(),
    error: z.string().optional(),
  }),
);

export const massPayoutApiResponseSchema = z
  .object({
    data: massPayoutBackendResponseSchema,
  })
  .transform(({ data }) => ({
    data: data?.map((i) => ({
      account: i.account,
      success: i.success,
      amount: +i.amount,
      notes: i.notes,
      error: i?.error,
    })),
  }));

export const checkMassPayoutApiResponseSchema = z
  .object({
    transferResults: massPayoutBackendResponseSchema,
    generalError: z.string(),
  })
  .transform(({ transferResults, generalError }) => ({
    data: transferResults?.map((i) => ({
      account: i.account,
      success: i.success,
      amount: +i.amount,
      notes: i.notes,
      error: i?.error,
    })),
    generalError,
  }));
