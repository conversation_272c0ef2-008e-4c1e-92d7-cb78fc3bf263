import { ROUTES } from '@/data';
import { operationIcon } from '@/utils/operations-utils/operation-icons';

export const topMenuLinks = [
  { label: ROUTES.root.key, link: ROUTES.root.path, icon: 'home' },
  { label: ROUTES.root.key, link: ROUTES.wallets.path, icon: 'wallet' },
  {
    label: ROUTES.transfer.key,
    link: ROUTES.transfer.path,
    icon: operationIcon({ operationType: 'transfer' }),
  },
  {
    label: ROUTES.exchange.key,
    link: ROUTES.exchange.path,
    icon: operationIcon({ operationType: 'exchange' }),
  },
  {
    label: ROUTES.withdraw.key,
    link: ROUTES.withdraw.path,
    icon: operationIcon({ operationType: 'withdraw' }),
  },
  {
    label: ROUTES.deposit.key,
    link: ROUTES.deposit.path,
    icon: operationIcon({ operationType: 'deposit' }),
  },
  { label: ROUTES.flexiCard.key, link: ROUTES.flexiCard.path, icon: 'credit-card' },
  { label: ROUTES.rates.key, link: ROUTES.rates.path, icon: 'businessplan' },
  { label: ROUTES.history.key, link: ROUTES.history.path, icon: 'history' },
  // { label: ROUTES.merchants.key, link: ROUTES.merchants.path, icon: 'history' },
];
export const footerNavbarMenuLinks = [
  { label: ROUTES.root.key, link: ROUTES.root.path, icon: 'home' },
  { label: ROUTES.wallets.key, link: ROUTES.wallets.path, icon: 'wallet' },
  {
    label: ROUTES.transfer.key,
    link: ROUTES.transfer.path,
    icon: operationIcon({ operationType: 'transfer' }),
  },
  {
    label: ROUTES.exchange.key,
    link: ROUTES.exchange.path,
    icon: operationIcon({ operationType: 'exchange' }),
  },
  {
    label: ROUTES.withdraw.key,
    link: ROUTES.withdraw.path,
    icon: operationIcon({ operationType: 'withdraw' }),
  },
  {
    label: ROUTES.deposit.key,
    link: ROUTES.deposit.path,
    icon: operationIcon({ operationType: 'deposit' }),
  },
  { label: ROUTES.flexiCard.key, link: ROUTES.flexiCard.path, icon: 'credit-card' },
  {
    label: ROUTES.massPayout.key,
    link: ROUTES.massPayout.path,
    icon: 'arrows-move',
  },
  { label: ROUTES.rates.key, link: ROUTES.rates.path, icon: 'businessplan' },
  { label: ROUTES.history.key, link: ROUTES.history.path, icon: 'history' },
  { label: ROUTES.help.key, link: ROUTES.help.path, icon: 'help' },
  {
    label: 'blog',
    link: 'https://blog.kazawallet.com',
    icon: 'article',
    target: '_blank',
  },
];

export const footerLandingItems = (locale: string | undefined) => [
  {
    title: 'information',
    links: [
      {
        label: 'termsOfUse',
        link: ROUTES.termsOfService.path,
      },
      {
        label: 'privacyNotice',
        link: ROUTES.privacyPolicy.path,
      },
      // {
      //   label: 'amlPolicy',
      //   link: '',
      // },
    ],
  },
  {
    title: 'support',
    links: [
      {
        label: ROUTES.help.key,
        link: ROUTES.help.path,
      },
      {
        label: 'faqs',
        link: `https://kazawallet.trengohelp.com/${
          locale === 'ar' ? 'ar' : 'en'
        }`,
        target: '_blank',
      },
      {
        label: 'blog',
        link: 'https://blog.kazawallet.com',
        target: '_blank',
      },
    ],
  },
  {
    title: 'contactUs',
    links: [
      {
        label: 'supportEmail',
        link: '<EMAIL>',
      },
    ],
  },
];
