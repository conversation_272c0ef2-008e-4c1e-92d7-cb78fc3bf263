/**
 * This component renders giveaway page
 * @description
 * Render markdown text in one languages "ar".
 */

import MetaTags from '@/components/common/meta-tags';
import { Layout } from '@/components/layout/layout';
import { assetBaseUrl } from '@/data';
import { Carousel } from '@mantine/carousel';
import {
  Image, Modal, Stack, Title,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import Autoplay from 'embla-carousel-autoplay';

import React, { useRef } from 'react';
import MarkdownView from 'react-showdown';

function Giveaway(props: {
  title: string;
  description: string;
  winnersText: string;
}) {
  const { description, title, winnersText } = props;
  const [opened, { close }] = useDisclosure(false);
  const autoplay = useRef(Autoplay({ delay: 5000 }));
  const slides = [`${assetBaseUrl}/assets/giveaway/giveaway.webp`];

  return (
    <>
      <MetaTags title={title} />
      <Layout>
        <Carousel
          // my={40}
          slideSize="100%"
          slideGap="md"
          loop
          align="start"
          plugins={[autoplay.current]}
          onMouseEnter={autoplay.current.stop}
          onMouseLeave={autoplay.current.reset}
        >
          {slides?.map((slide) => (
            <Carousel.Slide key={slide}>
              <Stack h="100%" justify="center">
                <Image src={slide} />
              </Stack>
            </Carousel.Slide>
          ))}
        </Carousel>

        <div dir="rtl">
          <Title mt={20} ta="center">
            {title}
          </Title>
          <Stack mt={40}>
            <MarkdownView
              markdown={description}
              options={{ tables: true, emoji: true }}
            />
            {/* <Group>
              <Text>نموذج عن البوست: </Text>
              <Group position="center">
              <Button px={0} variant="white" onClick={open}>
              اضغط هنا
              </Button>
              </Group>
              </Group>
              <GiveawayForm /> */}
            <Image src={`${assetBaseUrl}/assets/giveaway/winners.webp`} />
            <MarkdownView
              markdown={winnersText}
              options={{ tables: true, emoji: true }}
            />
          </Stack>
        </div>
        <Modal
          padding={0}
          withCloseButton={false}
          opened={opened}
          onClose={close}
          centered
        >
          <Image src={`${assetBaseUrl}/assets/giveaway/example.webp`} />
        </Modal>
      </Layout>
    </>
  );
}

export default Giveaway;
export async function getStaticProps() {
  return {
    props: {
      title: 'صور. شارك. اربح',
      description: `شروط المسابقة:

🔻صورة من منطقتك تذكر فيها اسم المنطقة (وين ما كنت) سواء فيديو، بوست، ستوري.. بشرط تظهر كذاواليت بالصورة ومنشن لكذاواليت

🔻كيف رح تظهر كذاواليت بالصورة؟ افتح عالتطبيق أو صفحة الفيسبوك أو حتى حساب الانستغرام وصوّر الموبايل مع المنطقة يلي قدامك واذكر اسم المنطقة

الجوائز:

1 جائزة بقيمة 5 مليون ل.س 

4 جوائز قيمة كل جائزة مليون ونص ل.س

10 جوائز قيمة كل جائزة 700 ألف ل.س 

20 جوائز قيمة كل جائزة 350 ألف ل.س

السحب:

بث مباشر على صفحتنا على فيسبوك يوم السبت 21/12/2024 الساعة 16:00  (UTC +03:00)


[رابط صفحة الفيسبوك](https://www.facebook.com/kazawallet/)


 انتهى وقت الاشتراك سيتم عرض اسماء الرابحين بعد عملية السحب المباشر 

`,
      winnersText: `
قائمة الرابحين بمسابقة "صورة من منطقتك مع كذاواليت" 💚🎁

توزيع الجوائز كان بالترتيب حسب السحب العشوائي على البث المباشر 🎉

🔻رح يتم إرسال الجوائز تلقائياً خلال 24 ساعة على حسابك بمحفظة كذاواليت

مبروك لل 35 رابح 🎁🔥

`,
    },
  };
}
