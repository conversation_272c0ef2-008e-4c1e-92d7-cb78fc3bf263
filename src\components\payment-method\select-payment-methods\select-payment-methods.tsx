import { useEffect, useState } from 'react';
import {
  Paper, Group, Text, Image,
} from '@mantine/core';

import useTranslation from 'next-translate/useTranslation';
import { PaymentMethodsApiResponse } from '@/store/payment-methods';
import { SelectPaymentMethodCard } from './select-payment-method';
import { UserApiResponse } from '@/store/user';
import { RatesApiResponse } from '@/store/rate';

import { DepositForm } from '@/components/deposit/deposit-form';
import { WithdrawForm } from '@/components/withdraw/withdraw-form';
import MyBalanceSkeleton from '@/components/balance/my-balance/my-balance-skeleton';
import { TranslatedTextValue } from '@/components/common/translated-text-value';
import { useRouter } from 'next/router';

interface Props {
  transactionType: 'withdraw' | 'deposit';
  selectedCurrency: UserApiResponse['currencies'][0] | undefined;
  data: PaymentMethodsApiResponse['data'];
  rates: RatesApiResponse['rates'];
  dataLoading: boolean;
}
// eslint-disable-next-line complexity
export default function PaymentMethodsSelect({
  transactionType,
  data,
  selectedCurrency,
  rates,
  dataLoading,
}: Props) {
  const router = useRouter();
  const [selectedItem, setSelectedItem] = useState<PaymentMethodsApiResponse['data'][0]>();
  const [loading, setLoading] = useState(false);
  const { t } = useTranslation();
  useEffect(() => {
    setSelectedItem(undefined);
  }, [selectedCurrency]);

  const initialPaymentMethod = data?.find((p) => p.label === router.query?.method);
  const selectedPaymentMethod = selectedItem ?? initialPaymentMethod;

  const nullishPaymentMethodsOrder = data?.filter((paymentMethod) => paymentMethod.order === null) ?? [];
  const unNullishPaymentMethodsOrder = data?.filter((paymentMethod) => paymentMethod.order !== null) ?? [];
  const PaymentMethodsSortedByOrder = unNullishPaymentMethodsOrder.concat(
    nullishPaymentMethodsOrder,
  );

  return (
    <div>
      <Paper withBorder py="xs" px="md" radius={20}>
        <SelectPaymentMethodCard
          currencySymbol={selectedCurrency?.symbol}
          setLoading={setLoading}
          data={PaymentMethodsSortedByOrder ?? []}
          setSelectedItem={setSelectedItem}
          selectedItem={selectedPaymentMethod}
          paymentType={transactionType}
          type="popup"
        >
          {(loading || dataLoading) && <MyBalanceSkeleton />}
          {!selectedPaymentMethod && !loading && !dataLoading && (
            <Group align="center" mih="50px" h="100%" noWrap>
              <Text color="dimmed" weight={500} tt="capitalize">
                {t(
                  `common:${
                    data?.length > 0
                      ? 'selectPaymentSystem'
                      : 'noPaymentMethodSupportThisCurrency'
                  }`,
                )}
              </Text>
            </Group>
          )}
          {selectedPaymentMethod && !loading && !dataLoading && (
            <Group h="100%" mih="50px" noWrap>
              <Image
                src={selectedPaymentMethod?.image}
                height={45}
                width={45}
                radius={50}
                alt="payment-method"
              />
              <div>
                <Text weight={500} size="lg" color="dimmed" tt="capitalize">
                  <TranslatedTextValue
                    keyEn={selectedPaymentMethod?.label}
                    keyAr={selectedPaymentMethod?.labelAr}
                  />
                </Text>
              </div>
            </Group>
          )}
        </SelectPaymentMethodCard>
      </Paper>
      {selectedPaymentMethod
        && selectedCurrency
        && rates
        && transactionType === 'deposit' && (
          <DepositForm
            rates={rates}
            selectedCurrency={selectedCurrency}
            selectedPaymentMethod={selectedPaymentMethod}
          />
      )}
      {selectedPaymentMethod
        && selectedCurrency
        && rates
        && transactionType === 'withdraw' && (
          <WithdrawForm
            rates={rates}
            selectedCurrency={selectedCurrency}
            selectedPaymentMethod={selectedPaymentMethod}
          />
      )}
    </div>
  );
  // );
}
