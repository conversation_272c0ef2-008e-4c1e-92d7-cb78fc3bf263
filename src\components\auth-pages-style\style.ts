import { createStyles } from '@mantine/core';
// shared styles for register and logout pages
export const useStyles = createStyles((theme) => ({
  submitButton: {
    '&[data-disabled]': {
      color: theme.colorScheme === 'light' ? 'gray' : 'white',
    },
  },
  loginVideoSection: {
    width: '58%',
    [theme.fn.smallerThan('md')]: {
      display: 'none',
    },
  },
  loginForm: {
    borderRight: `1px solid ${theme.colors.gray[3]} `,
    paddingTop: '80px',
    width: '40%',
    [theme.fn.smallerThan('md')]: {
      width: '70%',
      margin: 'auto',
    },
    [theme.fn.smallerThan('xs')]: {
      width: '100%',
    },
  },
  wrapper: {
    height: '100vh',
    backgroundSize: 'cover',
  },
  registerTextInput: {
    width: '48%',
    [theme.fn.smallerThan('xs')]: {
      width: '100%',
    },
  },
}));
