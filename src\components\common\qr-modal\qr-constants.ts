export const QR_CONFIG = {
  LOGO_URL: '/assets/logo/logo.png',
  SCAN_DELAY: 0,
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  SCANNER_DELAY: 50,
  SCAN_REGION_PCT: 0.8,
} as const;

export const RESPONSIVE_BREAKPOINTS = {
  MOBILE: 480,
  TABLET: 768,
} as const;

export const QR_SIZES = {
  MOBILE: {
    qrSize: 220,
    qrWrapperSize: 260,
    scannerFinderWidth: '80vw',
    scannerFinderHeight: '80vh',
    logoSize: 55,
    modalPadding: 'xs',
  },
  TABLET: {
    qrSize: 280,
    qrWrapperSize: 320,
    scannerFinderWidth: '380px',
    scannerFinderHeight: '480px',
    logoSize: 65,
    modalPadding: 'sm',
  },
  DESKTOP: {
    qrSize: 320,
    qrWrapperSize: 360,
    scannerFinderWidth: '420px',
    scannerFinderHeight: '520px',
    logoSize: 75,
    modalPadding: 'md',
  },
} as const;

export type QRSizeConfig = typeof QR_SIZES.MOBILE | typeof QR_SIZES.TABLET | typeof QR_SIZES.DESKTOP;
