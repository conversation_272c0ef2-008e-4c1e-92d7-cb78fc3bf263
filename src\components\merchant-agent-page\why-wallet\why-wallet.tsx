import {
  Container,
  Image,
  SimpleGrid,
  Stack,
  Text,
  useMantineTheme,
} from '@mantine/core';

import styles from '../styles.module.scss';
import Link from 'next/link';
import { useMediaQuery } from '@mantine/hooks';
import { ReactNode } from 'react';
import { assetBaseUrl } from '@/data';

interface WhyWalletProps {
  title: string;
  image: string;
  description: ReactNode;
  actionTitle: string;
  actionLink: string;
  withImageBackground: boolean;
}
export default function WhyWallet({
  actionLink,
  actionTitle,
  description,
  image,
  title,
  withImageBackground,
}: WhyWalletProps) {
  const theme = useMantineTheme();
  const matches = useMediaQuery(`(max-width: ${theme.breakpoints.lg})`, true, {
    getInitialValueInEffect: false,
  });
  const matchesMd = useMediaQuery(
    `(max-width: ${theme.breakpoints.md})`,
    true,
    {
      getInitialValueInEffect: false,
    },
  );
  const matchesSm = useMediaQuery(
    `(max-width: ${theme.breakpoints.sm})`,
    true,
    {
      getInitialValueInEffect: false,
    },
  );
  const imageSize = () => {
    let width = 600;
    if (matchesSm) {
      width = 305;
    } else if (matchesMd) {
      width = 415;
    } else if (matches) {
      width = 525;
    }
    return width;
  };

  return (
    <Container
      mt={20}
      py={40}
      px={{
        xs: 20,
        sm: 30,
        lg: 40,
        base: 20,
      }}
      size={1200}
    >
      <Text
        ff="BauhausC,__Cairo_0685b6,__Cairo_Fallback_0685b6"
        size={43}
        weight="bold"
        color="white"
      >
        {title}
      </Text>
      <SimpleGrid
        cols={2}
        breakpoints={[
          { maxWidth: 'sm', cols: 2, spacing: 'md' },
          { maxWidth: 'xs', cols: 1, spacing: 'sm' },
        ]}
      >
        <Stack pb={50} pt={50} justify="center">
          {description}
          <Link
            href={actionLink}
            style={{
              textDecoration: 'none',
              color: theme.colors.primary[7],
              width: 191,
            }}
            className={styles.aHero}
          >
            {actionTitle}
          </Link>
        </Stack>
        <Stack
          h={500}
          justify="end"
          align="center"
          sx={() => ({
            [theme.fn.smallerThan('xs')]: {
              display: 'none',
            },
            position: 'relative',
          })}
        >
          {withImageBackground && (
            <Image
              width={matchesSm ? 250 : 320}
              src={`${assetBaseUrl}/assets/new-landing/blur-hero.webp`}
              alt="security"
            />
          )}
          <Image
            top={{ md: '5%', sm: '24%', base: '42%' }}
            sx={{
              position: 'absolute',
            }}
            width={imageSize()}
            src={image}
            alt="security"
          />
        </Stack>
        <Stack
          justify="center"
          mx="auto"
          sx={() => ({
            display: 'none',
            [theme.fn.smallerThan('xs')]: {
              display: 'block',
            },
            position: 'relative',
          })}
        >
          {withImageBackground && (
            <Image
              width={280}
              src={`${assetBaseUrl}/assets/new-landing/blur-hero.webp`}
              alt="security"
            />
          )}
          <Image
            top={-40}
            sx={{
              position: 'absolute',
            }}
            width={350}
            src={image}
            alt="security"
          />
        </Stack>
      </SimpleGrid>
    </Container>
  );
}
