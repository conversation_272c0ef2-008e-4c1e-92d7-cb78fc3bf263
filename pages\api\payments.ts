/**
 * There are two handlers: get user payments data and create payment.
 */

import { apiEndpoints, httpCode, playCaptcha } from '@/data';
import { BackendClient } from '@/lib';
import {
  createPaymentBackendRequestSchema,
  paymentApiResponseSchema,
  paymentsApiResponseSchema,
  returnPaymentsParams,
} from '@/store/payment';
import { verifyCode } from '@/utils/2fa/verify-code';
import createApiError from '@/utils/api-utils/create-api-error';
import createApiResponse from '@/utils/api-utils/create-api-response';
import { getJwt } from '@/utils/api-utils/jwt';
import { captchaValidator } from '@/utils/captcha-validator';
import { NextApiRequest, NextApiResponse } from 'next';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token, email } = await getJwt(req);
  const { id: itemId, type } = req.query;
  // this function to get payment by id with filter by type
  if (itemId && itemId !== 'undefined' && req.method === 'GET') {
    try {
      const { data } = await BackendClient(req).get(
        apiEndpoints.payments(`${itemId}`),
        {
          headers: {
            authorization: token,
          },
          params: {
            'populate[currency][populate]': '*',
            'populate[payment_method][populate]': '*',
            'populate[custom_fields][populate]': '*',
            'filters[type][$eq]': type,
          },
        },
      );
      return res
        .status(httpCode.SUCCESS)
        .json(paymentApiResponseSchema(data.data));
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
    // this function to get user payment
    // git filters and pagination as params
    // return list of payments with pagination
  }
  if (req.method === 'GET') {
    try {
      const { data } = await BackendClient(req).get(apiEndpoints.payments(), {
        headers: {
          authorization: token,
        },
        params: { ...returnPaymentsParams(req, email) },
      });

      return createApiResponse(res, paymentsApiResponseSchema, data);
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
    // this function to create payment
    // return created payment details
  } else if (req.method === 'POST') {
    try {
      // get token "chaKey" returned from captha and pass it to "captchaValidator" to check toke validity
      // if validation success will call  api,else return error.
      const chaKey = req?.query?.chaKey;
      await captchaValidator({
        token: chaKey as string,
        secret: process.env.RECAPTCHA_SECRET_KEY as string,
        active: playCaptcha,
      });
      /**
       * If user enabled 2FA and type of payment is "withdraw", then he should add pin code get it from google authenticator app to complete payment operation
       * If there is a qrCode, then will verify code validity and complete operation if return true
       */
      const qrCode = req?.query?.qrCode;
      if (req.body?.type === 'withdraw') {
        await verifyCode(qrCode as string, token, req);
      }
      // create payment function
      const { data } = await BackendClient(req).post(
        apiEndpoints.payments(),
        createPaymentBackendRequestSchema.parse(req.body),
        {
          headers: {
            authorization: token,
          },
          params: {
            'populate[currency][populate]': '*',
            'populate[payment_method][populate]': '*',
            'populate[custom_fields][populate]': '*',
          },
        },
      );
      return res
        .status(httpCode.SUCCESS)
        .json(paymentApiResponseSchema(data?.data));
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }
  const error = createApiError({ error: '' });
  return res.status(httpCode.INTERNAL_SERVER_ERROR).json(error);
}

export default handler;
