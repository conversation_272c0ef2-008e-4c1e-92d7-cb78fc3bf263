/**
 * This component renders a wallet details page.
 *
 * @description
 * This page used to display currency details and transactions history for this currency.
 * There are actions button to navigate to operation page like (transfer,exchange,deposit,withdraw).
 * Display list of payments and transfers with filter by payment type in payments and transfer type in transfers.
 * List items ordered by "createdAt" with pagination to load more items.
 * There is text input to search by transaction id.
 */
import MetaTags from '@/components/common/meta-tags';
import { Layout } from '@/components/layout/layout';

import {
  ActionIcon,
  Container,
  Group,
  Loader,
  Menu,
  Stack,
  Text,
  TextInput,
} from '@mantine/core';

import { useRouter } from 'next/router';

import React, { useState } from 'react';

import { IconSearch } from '@tabler/icons-react';
import {
  UseInfiniteQueryResult,
  useInfiniteQuery,
  useQuery,
} from '@tanstack/react-query';
import { getUserQuery } from '@/store/user';
import MyBalanceSkeleton from '@/components/balance/my-balance/my-balance-skeleton';
import TabDataContents from '@/components/history/tab-data-contents/tab-data-contents';
import { getPaymentsQuery } from '@/store/payment';
import { getTransfersQuery } from '@/store/transfer';
import { useDebouncedState } from '@mantine/hooks';
import { operationIcon } from '@/utils/operations-utils/operation-icons';
import useTranslation from 'next-translate/useTranslation';
import CurrencyTitleSkeleton from '@/components/balance/currency-title-skeleton';
import { ActionsCard } from '@/components/actions-card';
import { MyBalance } from '@/components/balance/my-balance';
import { Icon } from '@/components/common/icon';
import { getCurrenciesQuery } from '@/store/currencies';
import { prop, unionWith, eqBy } from 'ramda';
import { TranslatedTextValue } from '@/components/common/translated-text-value';

interface ContentProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  paymentsDataQuery: UseInfiniteQueryResult<any, unknown>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  transfersDataQuery: UseInfiniteQueryResult<any, unknown>;
  accountId: string | undefined;
  dataType: 'payment' | 'transfer' | 'all';
}

// This component render Loader if data fetching ,List of items if data returned
function Content({
  paymentsDataQuery,
  transfersDataQuery,
  accountId,
  dataType,
}: ContentProps) {
  // Render loader if data fetching
  return paymentsDataQuery?.isLoading || transfersDataQuery?.isLoading ? (
    <Stack align="center">
      <Loader />
    </Stack>
  ) : (
    <TabDataContents
      userAccountId={accountId ?? ''}
      mixData={
        // if selected "all" menu item return payments list and transfers list
        dataType === 'all'
          ? [
            ...(paymentsDataQuery?.data?.pages[0]?.data ?? []),
            ...(transfersDataQuery?.data?.pages[0]?.data ?? []),
          ]
          : []
      }
      dataType={dataType}
      dataQueryProps={
        // if selected "payment" menu item return payments list else transfers list
        dataType === 'payment' ? paymentsDataQuery : transfersDataQuery
      }
    />
  );
}
// This component is custom menu item
function MenuItem({
  handleTabsChange,
  value,
  icon,
}: {
  handleTabsChange: (v: string) => void;
  value: string;
  icon: string;
}) {
  const { t } = useTranslation();
  return (
    <Menu.Item
      onClick={() => handleTabsChange(value)}
      icon={<Icon icon={icon} size={14} color="dark" />}
    >
      {t(`common:${value}`)}
    </Menu.Item>
  );
}
// eslint-disable-next-line complexity
function WalletHistory() {
  const { t } = useTranslation();
  const { query } = useRouter();
  const { data, isLoading } = useQuery(getUserQuery({}));
  // call get all currencies api to get all currencies
  const allCurrencies = useQuery(getCurrenciesQuery({}));
  const isLoadingCurrencies = isLoading || allCurrencies.isLoading;
  // merge all currencies with user currencies to get "amount" props from user currencies
  const mergedCurrencies = unionWith(
    eqBy(prop('id')),
    data?.currencies ?? [],
    allCurrencies?.data?.data ?? [],
  );
  // Initial current currency
  const balanceDetails = mergedCurrencies
    ? mergedCurrencies?.filter((i) => i.uid === query.slug)[0]
    : undefined;
  const [value, setValue] = useState('');
  const [searchValue, setSearchValue] = useDebouncedState<string | null>(
    null,
    200,
  );
  const [translation, setTranslation] = useState('recentTransactions');
  // this state to filter and set pagination to transfers and payment by menu item selected
  // when select "all" menu item will get 20 payment and 20 transfer and merge it by "createdAt" param.
  const [dataType, setDataType] = useState<'transfer' | 'payment' | 'all'>(
    'all',
  );
  const [paymentTypeFilter, setPaymentTypeFilter] = useState<
    'deposit' | 'withdraw' | null
  >(null);
  const [transferTypeFilter, setTransferTypeFilter] = useState<
    'exchange' | 'transfer' | 'received' | null
  >(null);
  // Call payments Api with filter by type, currency and search
  const paymentsDataQuery = useInfiniteQuery(
    getPaymentsQuery({
      pagination: {
        // if menu item selected is "all" checked then will set pagination to payments 20 else 10.
        pageSize: dataType === 'all' ? 20 : 10,
      },
      filters: {
        type: paymentTypeFilter,
        currencyId: balanceDetails?.id,
        search: dataType !== 'transfer' ? searchValue?.trim() : null,
      },
    }),
  );
  // Call transfers Api with filter by type, currency, userFrom, userTo and search
  const transfersDataQuery = useInfiniteQuery(
    getTransfersQuery({
      pagination: {
        // if menu item selected is "all" checked then will set pagination to transfers 20 else 10.
        pageSize: dataType === 'all' ? 20 : 10,
      },
      filters: {
        // if transferTypeFilter is "received" will return transfers them "userTo" equal user account id
        received: transferTypeFilter === 'received' ? data?.accountId : null,
        // if transferTypeFilter is "transfer" will return transfers them "userFrom" equal user account id
        sent: transferTypeFilter === 'transfer' ? data?.accountId : null,
        // filter type if equal "type" param
        eqType: transferTypeFilter === 'exchange' ? transferTypeFilter : null,
        // filter type if not equal "type" param
        nEqType:
          transferTypeFilter !== 'exchange' && dataType !== 'all'
            ? 'exchange'
            : null,
        currencyId: balanceDetails?.id,
        // filter by "transactionId" param
        search: dataType !== 'payment' ? searchValue?.trim() : null,
      },
    }),
  );
  // Trigger to handle menu item change and set new filters
  const handleTabsChange = (v: string) => {
    if (v === 'exchange') {
      setTransferTypeFilter('exchange');
      setDataType('transfer');
      setTranslation('recentExchangeTransactions');
      setSearchValue(null);
      setValue('');
    } else if (v === 'transfer') {
      setTransferTypeFilter('transfer');
      setDataType('transfer');
      setTranslation('recentTransferTransactions');
      setSearchValue(null);
      setValue('');
    } else if (v === 'received') {
      setTransferTypeFilter('received');
      setDataType('transfer');
      setTranslation('recentReceivedTransactions');
      setSearchValue(null);
      setValue('');
    } else if (v === 'deposit') {
      setPaymentTypeFilter('deposit');
      setDataType('payment');
      setTranslation('recentDepositTransactions');
      setSearchValue(null);
      setValue('');
    } else if (v === 'withdraw') {
      setPaymentTypeFilter('withdraw');
      setDataType('payment');
      setTranslation('recentWithdrawTransactions');
      setSearchValue(null);
      setValue('');
    } else if (v === 'all') {
      setPaymentTypeFilter(null);
      setTransferTypeFilter(null);
      setDataType('all');
      setTranslation('recentTransactions');
      setSearchValue(null);
      setValue('');
    }
  };
  return (
    <div>
      <MetaTags title={t('common:wallets')} />
      <Layout>
        <Container px={0} maw={800}>
          <Stack align="center">
            {balanceDetails?.label && (
              <Text tt="uppercase" size="lg" weight={500}>
                <TranslatedTextValue keyEn={balanceDetails?.label} keyAr={balanceDetails?.labelAr} />
              </Text>
            )}
            {isLoadingCurrencies && <CurrencyTitleSkeleton />}
            {balanceDetails && (
              <MyBalance
                precision={balanceDetails?.precision ?? 2}
                icon={balanceDetails?.image}
                balance={+balanceDetails.amount}
                symbol={undefined}
                symbolCurrency={balanceDetails?.symbol}
                currency={balanceDetails?.id}
              />
            )}
            {isLoadingCurrencies && <MyBalanceSkeleton />}
            <Text tt="capitalize" weight={500} color="dimmed" size="lg">
              {t('common:myBalance')}
            </Text>
            <ActionsCard />
          </Stack>
          <Stack my="md" mx="auto" maw={800}>
            <Group maw={800} position="apart">
              <Text tt="capitalize">{t(`common:${translation}`)}</Text>
              <Menu position="bottom-end" shadow="md" width={200} radius={18}>
                <Menu.Target>
                  <ActionIcon>
                    <Icon size={20} icon="adjustments" color="dark" />
                  </ActionIcon>
                </Menu.Target>
                <Menu.Dropdown>
                  <MenuItem
                    handleTabsChange={handleTabsChange}
                    icon="baseline-density-small"
                    value="all"
                  />
                  <MenuItem
                    handleTabsChange={handleTabsChange}
                    icon={operationIcon({ operationType: 'deposit' })}
                    value="deposit"
                  />
                  <MenuItem
                    handleTabsChange={handleTabsChange}
                    icon={operationIcon({ operationType: 'withdraw' })}
                    value="withdraw"
                  />
                  <MenuItem
                    handleTabsChange={handleTabsChange}
                    icon={operationIcon({ operationType: 'transfer' })}
                    value="transfer"
                  />
                  <MenuItem
                    handleTabsChange={handleTabsChange}
                    icon={operationIcon({ operationType: 'received' })}
                    value="received"
                  />
                  <MenuItem
                    handleTabsChange={handleTabsChange}
                    icon={operationIcon({ operationType: 'exchange' })}
                    value="exchange"
                  />
                </Menu.Dropdown>
              </Menu>
            </Group>
            <TextInput
              onChange={(v) => {
                setValue(v?.target?.value);
                setSearchValue(
                  v?.target?.value === '' ? null : v?.target?.value,
                );
              }}
              radius={13}
              placeholder="TxID"
              rightSection={<IconSearch color="gray" size={15} />}
              miw="100%"
              value={value}
            />
          </Stack>
          <Content
            accountId={data?.accountId}
            dataType={dataType}
            paymentsDataQuery={paymentsDataQuery}
            transfersDataQuery={transfersDataQuery}
          />
        </Container>
      </Layout>
    </div>
  );
}

export default WalletHistory;

export async function getServerSideProps() {
  return { props: {} };
}
