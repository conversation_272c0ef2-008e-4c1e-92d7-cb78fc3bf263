/**
 * This page to generate registration link and convert to it
 * * @description
 */

import MetaTags from '@/components/common/meta-tags';
import { Container, Stack, Text } from '@mantine/core';
import classes from './style.module.scss';

import { useRouter } from 'next/router';
import React, { useEffect } from 'react';
import { generateRegistrationLink } from '@/utils/generate-registration-url';
import { GetServerSidePropsContext } from 'next';

function RegisterRedirect() {
  const { pathname } = useRouter();

  useEffect(() => {
    const chatElement = document.getElementById('trengo-web-widget');
    if (chatElement) chatElement.style.display = 'block';
    return () => {
      const newChatElement = document.getElementById('trengo-web-widget');
      if (newChatElement) newChatElement.style.display = 'none';
    };
  }, [pathname]);
  return (
    <>
      <MetaTags />
      <Container>
        <Stack h="100vh" justify="center" align="center">
          <Text
            color="dimmed"
            size="lg"
            align="center"
            className={classes.description}
          >
            redirecting to register page….
          </Text>
        </Stack>
      </Container>
    </>
  );
}

export default RegisterRedirect;

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const ref = context?.query?.ref;
    const url = await generateRegistrationLink(ref);
    return {
      redirect: {
        destination: url,
        permanent: false,
      },
    };
  } catch (error) {
    return {
      notFound: true,
    };
  }
}
