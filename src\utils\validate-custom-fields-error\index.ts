import { PaymentMethodsApiResponse } from '@/store/payment-methods';
import { CUSTOM_FIELDS_TYPE } from '@/types/custom-fields-type.type';
import { UseFormReturnType } from '@mantine/form';

interface FormValues {
  amount: number | string;
  note: string;
  status: string;
  currencyId: string;
  paymentMethodId: string;
  type: string;
  fields:
    | {
        type: CUSTOM_FIELDS_TYPE | null | undefined;
        name: string | undefined | null;
        nameAr: string | undefined | null;
        value: string | string[] | null;
      }[]
    | undefined;
}
// this function to validate types and regx to custom fields
export const customFieldsErrorValidate = (
  form: UseFormReturnType<FormValues>,
  customFields: PaymentMethodsApiResponse['data'][0]['depositCustomFields'],
  open: () => void,
  // eslint-disable-next-line sonarjs/cognitive-complexity
) => {
  form.clearErrors();
  let isError = false;
  form.values?.fields?.map((i, index) => {
    if (i?.value === '' || i?.value === null) {
      isError = true;
      form.setFieldError(`fields.${index}`, 'required');
      return isError;
    }
    if (
      i?.type === CUSTOM_FIELDS_TYPE.multiple
      && typeof i?.value === 'object'
    ) {
      if (i?.value?.length < 1) {
        isError = true;
        form.setFieldError(`fields.${index}`, 'multipeFeilsdMinimum');
        return isError;
      }
      i?.value?.map((item) => {
        if (item === '') {
          isError = true;
          form.setFieldError(`fields.${index}`, 'multipeFeilsdRequired');
          return isError;
        }
        return isError;
      });
    }
    if (
      i?.type === CUSTOM_FIELDS_TYPE.string
      && customFields
      && customFields[index]?.regex
    ) {
      const re = new RegExp(`${customFields[index]?.regex}`);
      if (re.test(`${i?.value}`)) {
        isError = isError === true;
      } else {
        form.setFieldError(`fields.${index}`, 'invalidValue');
        isError = true;
      }
    }
    return isError;
  });
  if (!isError) open();
  return isError;
};
