// to check amount if it between min and max amount and if it less the balance for exchange opertaions
// return true if condition return true
// return false conditions return false

import { UserApiResponse } from '@/store/user';

export const minMaxError = (
  currencyGiveItem: UserApiResponse['currencies'][0] | undefined,
  giveAmount: number | undefined | '',
  getAmount: number | undefined | '',
  calculatedValue: number | undefined,
) => {
  let isError = false;
  let amount = 0;
  if (giveAmount) amount = giveAmount;
  else if (getAmount && calculatedValue) amount = calculatedValue;

  if (currencyGiveItem && amount > 0) {
    if (
      (currencyGiveItem?.exchangeMin
        && amount < currencyGiveItem?.exchangeMin)
      || amount < 0
      || (currencyGiveItem?.exchangeMax
        && amount > currencyGiveItem?.exchangeMax)
      || amount > +currencyGiveItem.amount
    ) {
      isError = true;
    } else isError = false;
  }

  return isError;
};
