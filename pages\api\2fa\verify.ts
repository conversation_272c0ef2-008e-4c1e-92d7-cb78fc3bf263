/**
 * This handler use to verify 2fa code authenticator.
 */
import { apiMethods, httpCode } from '@/data';
import { NextApiRequest, NextApiResponse } from 'next';
import createApiError from '@/utils/api-utils/create-api-error';
import { verifyCode } from '@/utils/2fa/verify-code';
import { getJwt } from '@/utils/api-utils/jwt';

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const { token } = await getJwt(req);
  // api to verify google authenticator code by client
  if (req.method === apiMethods.POST) {
    try {
      const qrCode = req?.query?.qrCode;
      await verifyCode(qrCode as string, token, req);
      return res.status(httpCode.SUCCESS).json('success');
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }

  const error = createApiError({ error: '' });
  return res.status(httpCode.INTERNAL_SERVER_ERROR).json(error);
};
export default handler;
