/**
 * rates response schema
 */
import { z } from 'zod';

export const merchantBackendSchema = z.object({
  id: z.string(),
  properties: z.object({
    img: z.object({
      files: z.array(
        z
          .object({
            external: z
              .object({
                url: z.string(),
              })
              .optional(),
          })
          .nullable(),
      ),
    }),
    description: z.object({
      rich_text: z.array(
        z.object({
          text: z
            .object({
              content: z.string(),
            })
            .optional(),
        }),
      ),
    }),
    ar_description: z.object({
      rich_text: z.array(
        z.object({
          text: z
            .object({
              content: z.string(),
            })
            .optional(),
        }),
      ),
    }),
    tag: z.object({
      select: z
        .object({
          name: z.string(),
          color: z.string(),
        })
        .nullable(),
    }),
    featured: z.object({
      checkbox: z.boolean().nullable(),
    }),
    short_description: z.object({
      rich_text: z.array(
        z.object({
          text: z
            .object({
              content: z.string(),
            })
            .optional(),
        }),
      ),
    }),
    ar_short_description: z.object({
      rich_text: z.array(
        z.object({
          text: z
            .object({
              content: z.string(),
            })
            .optional(),
        }),
      ),
    }),
    title: z.object({
      rich_text: z.array(
        z.object({
          text: z
            .object({
              content: z.string(),
            })
            .optional(),
        }),
      ),
    }),
    ar_title: z.object({
      rich_text: z.array(
        z.object({
          text: z
            .object({
              content: z.string(),
            })
            .optional(),
        }),
      ),
    }),
    link: z.object({
      url: z.string(),
    }),
    banner: z.object({
      files: z.array(
        z
          .object({
            external: z
              .object({
                url: z.string(),
              })
              .optional(),
          })
          .nullable(),
      ),
    }),
  }),
});

export const merchantApiResponseSchema = (
  item: z.infer<typeof merchantBackendSchema>,
  locale: string,
) => ({
  id: item.id,
  image: item.properties.img.files[0]?.external?.url ?? '',
  name:
    item.properties[locale === 'ar' ? 'ar_title' : 'title']?.rich_text?.length
    > 0
      ? item.properties[locale === 'ar' ? 'ar_title' : 'title'].rich_text[0]
        .text?.content
      : '',
  description:
    item.properties[locale === 'ar' ? 'ar_description' : 'description']
      .rich_text[0]?.text?.content ?? '',
  shortDescription:
    item.properties[
      locale === 'ar' ? 'ar_short_description' : 'short_description'
    ].rich_text[0]?.text?.content ?? '',
  tag: {
    name: item.properties.tag.select?.name ?? '',
    color: item.properties.tag.select?.color ?? '',
  },
  featured: item.properties.featured.checkbox ?? false,
  link: item.properties.link?.url ?? '',
  banner: item.properties.banner?.files[0]?.external?.url ?? '',
});

export const merchantBackendResponseSchema = z.array(merchantBackendSchema);

export const merchantsApiResponseSchema = z
  .object({
    results: merchantBackendResponseSchema,
    locale: z.string(),
  })
  .transform(({ results, locale }) => ({
    data: results.map((item) => merchantApiResponseSchema(item, locale)),
  }));
