import { GlobalCard } from '@/components/common/global-card';
import { Icon } from '@/components/common/icon';
import { DATE_FORMAT } from '@/data';
import { GiftCardsApiResponse } from '@/store/gift-card';
import {
  Badge, Box, Group, Image, Stack, Text, Tooltip,
} from '@mantine/core';
import dayjs from 'dayjs';
import useTranslation from 'next-translate/useTranslation';
import React from 'react';

interface GiftCardItemProps {
  gift: GiftCardsApiResponse['data'][0];
}
function GiftCardItem({ gift }: GiftCardItemProps) {
  const { t } = useTranslation();
  const renderIconWithColor = () => {
    let icon = 'dots';
    let color = 'yellow';
    let label = 'notUsed';
    if (gift?.expiryDate && dayjs(gift.expiryDate) > dayjs()) {
      icon = 'x';
      color = 'red';
      label = 'expired';
    }
    if (gift?.isUsed) {
      icon = 'check';
      color = 'green';
      label = 'used';
    }
    return {
      color,
      icon,
      label,
    };
  };
  return (
    <GlobalCard props={{}} onClick={() => {}} key={gift?.uid}>
      <Group align="center" spacing="xs" noWrap>
        <Image
          alt="currency"
          src={gift?.currency?.image}
          height={40}
          width={40}
          radius={40}
        />
        <Stack w="90%" spacing="xs">
          <Group position="apart" noWrap>
            <Group position="apart" w="100%">
              <div>
                <Text span weight={700} size="md">
                  {gift?.amount}
                </Text>
                <Text mx={4} span weight={500}>
                  {gift?.currency?.symbol}
                </Text>
              </div>
              <Text weight={500} size="sm" color="dimmed" tt="uppercase">
                {gift?.uid}
              </Text>
            </Group>
            <Box>
              <Tooltip label={t(`common:${renderIconWithColor().label}`)}>
                <Badge
                  p={0}
                  w={25}
                  h={25}
                  sx={{ borderWidth: '0.15rem' }}
                  variant="outline"
                  radius={50}
                  color={renderIconWithColor().color}
                >
                  <Group>
                    <Icon
                      icon={renderIconWithColor().icon}
                      size={16}
                      color="dark"
                    />
                  </Group>
                </Badge>
              </Tooltip>
            </Box>
          </Group>
          <Group spacing="sm">
            <Group spacing="xs" position="left" align="center">
              <Text>
                {t('common:createdAt')}
                {' '}
                :
              </Text>
              <Text>{dayjs(gift?.createdAt).format(DATE_FORMAT)}</Text>
            </Group>
            {gift?.isUsed && gift?.updatedAt && (
              <Group spacing="xs" position="left" align="center">
                <Text>
                  {t('common:usedAt')}
                  {' '}
                  :
                </Text>
                <Text>{dayjs(gift?.updatedAt).format(DATE_FORMAT)}</Text>
              </Group>
            )}
            {!gift?.isUsed && gift?.expiryDate && (
              <Group spacing="xs" position="left" align="center">
                <Text>
                  {t('common:expiryTo')}
                  {' '}
                  :
                </Text>
                <Text>{dayjs(gift?.expiryDate).format(DATE_FORMAT)}</Text>
              </Group>
            )}
          </Group>
        </Stack>
      </Group>
    </GlobalCard>
  );
}

export default GiftCardItem;
