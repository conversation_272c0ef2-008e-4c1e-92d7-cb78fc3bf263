/* eslint-disable max-lines */
/**
 * This component renders a History page.
 *
 * @description
 * This page used to display list of transfers and payments for the logging in user account and filter it using tabs component by five tabs
 *  deposit, withdraw, exchange,transfer,received and all.
 * Deposit and withdraw display list of payment with filter by type of payment "deposit" or "withdraw"
 * Transfer, Exchange and received display list of transfers with filter by type "exchange" or "transfer".
 * In "received" tab display the list of transfer with type "transfer" and the "user to account id" is equal the current user logging in.
 * In "all" tab display last 20 items from payment and 20 items from transfers and order it by "createdAt" key.
 * There is input text to search "filter" by "transaction id".
 * There is button to load more items in each tab except "all" tab.
 * Each item from the list display some details about the transaction and on click it will open popup to display more details.
 */
import MetaTags from '@/components/common/meta-tags';
import { Layout } from '@/components/layout/layout';

import {
  Box,
  Container,
  Group,
  Loader,
  MantineTheme,
  Stack,
  Tabs,
  TabsValue,
  Text,
  TextInput,
} from '@mantine/core';

import React, { useState } from 'react';
import useTranslation from 'next-translate/useTranslation';
import { IconSearch } from '@tabler/icons-react';

import { useInfiniteQuery, useQuery } from '@tanstack/react-query';
import { getPaymentsQuery } from '@/store/payment';
import TabDataContents from '@/components/history/tab-data-contents/tab-data-contents';
import { getTransfersQuery } from '@/store/transfer';
import { useDebouncedValue } from '@mantine/hooks';
import { operationIcon } from '@/utils/operations-utils/operation-icons';
import { getUserQuery } from '@/store/user';
import TapContent from '@/components/history/tab-item-content';
import { Icon } from '@/components/common/icon';
import { PageTitle } from '@/components/common/page-title';
import { ExportAsCsv } from '@/components/common/export-as-csv';
import { HistoryFilters } from '@/components/history/history-filters';

function History() {
  const { t } = useTranslation();
  const { data } = useQuery(getUserQuery({}));
  // filters states
  const [currency, setCurrency] = useState<string | null>(null);
  const [status, setStatus] = useState<string | null>(null);
  const [paymentMethod, setPaymentMethod] = useState<string | null>(null);
  const [createdFrom, setCreatedFrom] = useState<string | null>(null);
  const [createdTo, setCreatedTo] = useState<string | null>(null);
  const [schedule, setSchedule] = useState<string | null>(null);
  // this state to filter and set pagination to transfers and payment by tab selected
  // when select all tab will get 20 payment and 20 transfer and merge it by "createdAt" param
  const [allData, setAllData] = useState<'transfers' | 'payments' | 'all'>(
    'all',
  );
  const [searchValue, setSearchValue] = useState('');
  const [debounced] = useDebouncedValue(searchValue, 200);

  const [paymentTypeFilter, setPaymentTypeFilter] = useState<
    'deposit' | 'withdraw' | null
  >(null);
  const [transferTypeFilter, setTransferTypeFilter] = useState<
    'exchange' | 'transfer' | 'received' | 'payment_link' | null
  >(null);
  // Call get user payments api and filter by type
  const paymentsDataQuery = useInfiniteQuery({
    ...getPaymentsQuery({
      pagination: {
        pageSize: 20,
      },
      filters: {
        type: paymentTypeFilter,
        search: allData !== 'transfers' ? debounced?.trim() : null,
        currencyId: currency,
        paymentMethod,
        status,
        createdFrom,
        createdTo,
      },
    }),
    enabled: allData !== 'transfers',
  });
  // Call get user transfers api and filter by type
  const transfersDataQuery = useInfiniteQuery({
    ...getTransfersQuery({
      pagination: {
        pageSize: 20,
      },
      filters: {
        // if transferTypeFilter is "received" will return transfers them "userTo" equal user account id
        received: transferTypeFilter === 'received' ? data?.accountId : null,
        // if transferTypeFilter is "transfer" will return transfers them "userFrom" equal user account id
        sent: transferTypeFilter === 'transfer' ? data?.accountId : null,
        // filter type if equal "type" param
        eqType: transferTypeFilter === 'exchange' ? transferTypeFilter : null,
        // filter type if not equal "type" param
        nEqType:
          transferTypeFilter !== 'exchange' && allData !== 'all'
            ? 'exchange'
            : null,
        // filter by "transactionId" param
        search: allData !== 'payments' ? debounced?.trim() : null,
        currencyId: currency,
        createdFrom,
        createdTo,
        paymentMethod,
        status,
      },
    }),
    enabled: allData !== 'payments',
  });
  // trigger to handle tabs changing and filter by the selected tab
  const handleTabsChange = (v: TabsValue) => {
    setSearchValue('');
    if (v === 'exchange') {
      setTransferTypeFilter('exchange');
      setAllData('transfers');
    } else if (v === 'transfer') {
      setTransferTypeFilter('transfer');
      setAllData('transfers');
    } else if (v === 'received') {
      setTransferTypeFilter('received');
      setAllData('transfers');
    } else if (v === 'deposit') {
      setPaymentTypeFilter('deposit');
      setAllData('payments');
    } else if (v === 'withdraw') {
      setPaymentTypeFilter('withdraw');
      setAllData('payments');
    } else if (v === 'all') {
      setPaymentTypeFilter(null);
      setTransferTypeFilter(null);
      setAllData('all');
    }
  };
  const isLoading = paymentsDataQuery?.isLoading || transfersDataQuery?.isLoading;
  // common style for all tabs
  const tabsStyle = (theme: MantineTheme) => ({
    [theme.fn.smallerThan('xs')]: {
      '& .mantine-ltr-Tabs-tabLabel ,& .mantine-rtl-Tabs-tabLabel': {
        display: 'none',
      },
      '& .mantine-ltr-Tabs-tabIcon ,& .mantine-rtl-Tabs-tabIcon': {
        margin: 'auto',
      },
    },
  });
  // trigger to handel change search input text and filter the list item by it
  const onSearchValueChange = (v: React.ChangeEvent<HTMLInputElement>) => setSearchValue(v?.currentTarget.value);

  // filters component props
  const filtersProps = {
    currency,
    paymentMethod,
    status,
    createdFrom,
    createdTo,
    setCurrency,
    setPaymentMethod,
    setStatus,
    setCreatedFrom,
    setCreatedTo,
    schedule,
    setSchedule,
    filteredDataType: allData,
  };
  return (
    <div>
      <MetaTags title={t('common:history')} />
      <Layout>
        <Container px={0} maw={800}>
          <PageTitle title="history" />
          <Stack mt="md" mx="auto" maw={800}>
            <Tabs
              defaultValue="all"
              onTabChange={handleTabsChange}
              sx={tabsStyle}
            >
              <Tabs.List>
                <Tabs.Tab
                  value="all"
                  icon={(
                    <Icon
                      icon="baseline-density-small"
                      size="1.2rem"
                      color="dark"
                    />
                  )}
                >
                  {t('common:all')}
                </Tabs.Tab>
                <Tabs.Tab
                  value="deposit"
                  icon={(
                    <Icon
                      icon={operationIcon({ operationType: 'deposit' })}
                      color="gray"
                      size="1.3rem"
                    />
                  )}
                >
                  {t('common:deposit')}
                </Tabs.Tab>
                <Tabs.Tab
                  value="withdraw"
                  icon={(
                    <Icon
                      icon={operationIcon({ operationType: 'withdraw' })}
                      color="gray"
                      size="1.3rem"
                    />
                  )}
                >
                  {t('common:withdraw')}
                </Tabs.Tab>
                <Tabs.Tab
                  value="transfer"
                  icon={(
                    <Icon
                      icon={operationIcon({ operationType: 'transfer' })}
                      color="gray"
                      size="1.3rem"
                    />
                  )}
                >
                  {t('common:transfer')}
                </Tabs.Tab>
                <Tabs.Tab
                  value="exchange"
                  icon={(
                    <Icon
                      icon={operationIcon({ operationType: 'exchange' })}
                      color="gray"
                      size="1.2rem"
                    />
                  )}
                >
                  {t('common:exchange')}
                </Tabs.Tab>
                <Tabs.Tab
                  value="received"
                  icon={(
                    <Icon
                      icon={operationIcon({ operationType: 'received' })}
                      color="gray"
                      size="1.3rem"
                    />
                  )}
                >
                  {t('common:received')}
                </Tabs.Tab>
              </Tabs.List>
              <TextInput
                onChange={onSearchValueChange}
                value={searchValue}
                my="sm"
                radius={13}
                placeholder="TxID"
                rightSection={<IconSearch color="gray" size={15} />}
                miw="100%"
              />

              <Tabs.Panel value="all" pt="xs">
                <Group position="apart">
                  <Text tt="capitalize">{t('common:recentTransactions')}</Text>
                  <Box display="flex" sx={{ gap: '10px' }}>
                    <HistoryFilters {...filtersProps} />
                    <ExportAsCsv
                      data={[
                        ...(paymentsDataQuery?.data?.pages[0]?.data ?? []),
                        ...(transfersDataQuery?.data?.pages[0]?.data ?? []),
                      ]}
                      transactionsType="all"
                      isLoading={isLoading}
                    />
                  </Box>
                </Group>
                {isLoading ? (
                  <Stack align="center">
                    <Loader />
                  </Stack>
                ) : (
                  <TabDataContents
                    userAccountId={data?.accountId ?? ''}
                    dataType="all"
                    // list of transfers and list of payments
                    mixData={[
                      ...(paymentsDataQuery?.data?.pages[0]?.data ?? []),
                      ...(transfersDataQuery?.data?.pages[0]?.data ?? []),
                    ]}
                    dataQueryProps={paymentsDataQuery}
                  />
                )}
              </Tabs.Panel>
              <TapContent
                accountId={data?.accountId}
                dataQueryProps={paymentsDataQuery}
                title={t('common:recentDepositTransactions')}
                dataType="payment"
                value="deposit"
                transactionsType="deposit"
                filtersProps={filtersProps}
              />
              <TapContent
                accountId={data?.accountId}
                dataQueryProps={paymentsDataQuery}
                title={t('common:recentWithdrawTransactions')}
                dataType="payment"
                value="withdraw"
                transactionsType="withdraw"
                filtersProps={filtersProps}
              />
              <TapContent
                accountId={data?.accountId}
                dataQueryProps={transfersDataQuery}
                title={t('common:recentTransferTransactions')}
                dataType="transfer"
                value="transfer"
                transactionsType="transfer"
                filtersProps={filtersProps}
              />
              <TapContent
                accountId={data?.accountId}
                dataQueryProps={transfersDataQuery}
                title={t('common:recentExchangeTransactions')}
                dataType="transfer"
                value="exchange"
                transactionsType="exchange"
                filtersProps={filtersProps}
              />
              <TapContent
                accountId={data?.accountId}
                dataQueryProps={transfersDataQuery}
                title={t('common:recentReceivedTransactions')}
                dataType="transfer"
                value="received"
                transactionsType="received"
                filtersProps={filtersProps}
              />
            </Tabs>
          </Stack>
        </Container>
      </Layout>
    </div>
  );
}

export default History;

export async function getServerSideProps() {
  return { props: {} };
}
