import axios from 'axios';
import { NextApiRequest } from 'next';

const backendURL = process.env.BACKEND_URL;
const appId = process.env.APP_ID;

const defaultHeader = (req: NextApiRequest) => {
  const { headers } = req;
  return {
    'x-app-id': appId,
    'x-real-ip': headers['x-real-ip'],
    'x-real-ip-kas': headers['x-real-ip'],
    'x-forwarded-host': headers['x-forwarded-host'],
    'x-vercel-ip-timezone': headers['x-vercel-ip-timezone'],
    'x-forwarded-for': headers['x-forwarded-for'],
    'x-vercel-proxied-for': headers['x-vercel-proxied-for'],
    'x-vercel-ip-latitude': headers['x-vercel-ip-latitude'],
    'x-vercel-ip-country-region': headers['x-vercel-ip-country-region'],
    'x-vercel-ip-country': headers['x-vercel-ip-country'],
    'x-vercel-ip-city': headers['x-vercel-ip-city'],
    'x-vercel-ip-longitude': headers['x-vercel-ip-longitude'],
    'x-vercel-forwarded-for': headers['x-vercel-forwarded-for'],
    'user-agent': headers['user-agent'],
  };
};
// next api server instance
export const ApiClient = axios.create({
  baseURL: '/api',
});
// backend server instance
export const BackendClient = (req: NextApiRequest) => axios.create({
  baseURL: `${backendURL}/api`,
  headers: {
    ...defaultHeader(req),
  },
});
