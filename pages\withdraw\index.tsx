/**
 * This component renders a withdraw page.
 *
 * @description
 * Tn this page we have a form to create payment type "withdraw"
 * We have select to choose the currency and after choose it will show select contain one
 *  or more payment method support the currency selected.
 * The currencies data it come from "get user currencies" api call to get the user balance in each currency and show it.
 * We can add currency uid to initial the currency item select in the route like this "withdraw/USD" then the default
 *  item in currencies select will be "USD" and we can change it to another currencies.
 * After form submitting will open popup to show create payment response details.
 */
import MetaTags from '@/components/common/meta-tags';
import { Layout } from '@/components/layout/layout';

import { Text, Stack, Paper } from '@mantine/core';
import { useRouter } from 'next/router';

import React, { useState } from 'react';
import useTranslation from 'next-translate/useTranslation';
import { useQuery } from '@tanstack/react-query';

import { UserApiResponse, getUserQuery } from '@/store/user';
import MyBalanceSkeleton from '@/components/balance/my-balance/my-balance-skeleton';
import { getPaymentMethodsQuery } from '@/store/payment-methods';

import { getRatesQuery } from '@/store/rate';

import { MyBalance } from '@/components/balance/my-balance';
import { PageTitle } from '@/components/common/page-title';

import { eqBy, prop, unionWith } from 'ramda';
import { getCurrenciesQuery } from '@/store/currencies';
import { SelectCurrencyCard } from '@/components/currency/select-currency-card';

import PaymentMethodsSelect from '@/components/payment-method/select-payment-methods';

function Withdraw() {
  const router = useRouter();
  const { query } = router;
  const { t } = useTranslation();

  const [selectedItem, setSelectedItem] = useState<UserApiResponse['currencies'][0]>();
  const handleSelectCurrency = (v: UserApiResponse['currencies'][0]) => {
    setSelectedItem(v);
    router.query.currency = v.uid;
    delete router.query.method;
    router.push(router);
  };
  const { data: ratesData } = useQuery(getRatesQuery());
  const { data, isLoading } = useQuery(getUserQuery({}));
  // call get all currencies api to get all currencies
  const allCurrencies = useQuery(getCurrenciesQuery({}));
  const isLoadingCurrencies = isLoading || allCurrencies.isLoading;
  // merge all currencies with user currencies to get "amount" props from user currencies
  const mergedCurrencies = unionWith(
    eqBy(prop('id')),
    data?.currencies ?? [],
    allCurrencies?.data?.data ?? [],
  );
  // init currency item selected if there is a "uid" query
  const initCurrency = query?.currency
    ? mergedCurrencies?.filter((i) => i.uid === query?.currency)
    : [];
  // get initial item or the selected user item
  const item = selectedItem ?? (initCurrency && initCurrency[0]);
  const paymentMethodsData = useQuery(
    getPaymentMethodsQuery({
      populate: {
        withdrawCurrencies: true,
        withdrawcustomFields: true,
      },
      filters: {
        withdrawCurrencyId: item?.uid,
      },
    }),
  );
  return (
    <div>
      <MetaTags title={t('common:withdraw')} />
      <Layout>
        <PageTitle title="withdraw" />
        <Stack align="center">
          <Text tt="capitalize" size="lg" weight={500}>
            {t('common:selectCurrency')}
          </Text>
          <Paper withBorder py="xs" px="md" radius={20}>
            <SelectCurrencyCard
              type="popup"
              data={mergedCurrencies ?? []}
              setSelectedItem={handleSelectCurrency}
              selectedItem={item}
            >
              {isLoadingCurrencies ? (
                <MyBalanceSkeleton />
              ) : (
                <MyBalance
                  precision={item?.precision ?? 2}
                  icon={item?.image}
                  balance={item?.amount ? +item.amount : 0}
                  symbol={undefined}
                  symbolCurrency={item?.symbol}
                  currency={item?.id}
                />
              )}
            </SelectCurrencyCard>
          </Paper>
          {item && (
            <Text tt="capitalize" weight={500} color="dimmed" size="lg">
              {t('common:myBalance')}
            </Text>
          )}
        </Stack>
        {item && (
          <Stack mt="md" mx="auto" maw={800}>
            <Text color="dimmed" tt="capitalize" weight={500} size="lg">
              {t('common:chosePaymentSystem')}
            </Text>

            <PaymentMethodsSelect
              rates={ratesData?.rates}
              selectedCurrency={item}
              data={item ? paymentMethodsData?.data?.data : []}
              transactionType="withdraw"
              dataLoading={paymentMethodsData?.isFetching}
            />
          </Stack>
        )}
      </Layout>
    </div>
  );
}

export default Withdraw;
export async function getServerSideProps() {
  return { props: {} };
}
