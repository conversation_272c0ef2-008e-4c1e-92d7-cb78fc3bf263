import { Button, ButtonProps, useMantineTheme } from '@mantine/core';
import React from 'react';

interface Props extends ButtonProps {
  // eslint-disable-next-line react/require-default-props
  onClick?: () => void;
}
function SubmitButton(props: Props) {
  const theme = useMantineTheme();
  return (
    <Button
      radius="lg"
      sx={{
        '&[data-disabled]': {
          color: theme.colorScheme === 'light' ? 'gray' : 'white',
        },
      }}
      {...props}
    />
  );
}

export default SubmitButton;
