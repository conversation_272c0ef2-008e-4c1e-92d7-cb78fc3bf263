import { apiEndpoints } from '@/data';
import { ApiClient } from '@/lib';
import { handleApiError } from '@/utils/api-utils/handle-backend-error';
import { CreateMassPayoutApiRequest } from './types';

enum queryKeys {
  create = 'create',
}

/**
 * @description function calls handler in "/custom/user/massPayout" api rout to create mass payout transfer.
 * If user enabled 2FA will get qrCode param and check code validity before crete mass payout.
 * @param "body, qrCode"
 * @returns list of transfers items with status ('success','failed') and the reason.
 */
const createMassPayoutRequest = ({
  body,
  qrCode,
  chaKey,
}: {
  body: CreateMassPayoutApiRequest;
  qrCode?: string;
  chaKey?: string;
}) => ApiClient.post(apiEndpoints.massPayout(), body, {
  params: {
    qrCode,
    chaKey,
  },
})
  .then((res) => res.data)
  .catch((err) => {
    handleApiError(err);
    throw err;
  });

export const createMassPayoutMutation = () => ({
  mutationKey: [queryKeys.create],
  mutationFn: ({
    body,
    qrCode,
    chaKey,
  }: {
    body: CreateMassPayoutApiRequest;
    qrCode?: string;
    chaKey?: string;
  }) => createMassPayoutRequest({ body, qrCode, chaKey }),
});
/** ************************************************************* */
/**
 * @description function calls handler in "/custom/user/checkMassPayout" api route to check items errors before create mass payout transfer.
 * @param "body"
 * @returns list of transfers items with status ('success','failed') and the reason.
 */
const checkMassPayoutRequest = ({
  body,
}: {
  body: CreateMassPayoutApiRequest;
}) => ApiClient.post(apiEndpoints.massPayoutCheck(), body)
  .then((res) => res.data)
  .catch((err) => {
    handleApiError(err);
    throw err;
  });

export const checkMassPayoutMutation = () => ({
  mutationKey: [queryKeys.create],
  mutationFn: ({ body }: { body: CreateMassPayoutApiRequest }) => checkMassPayoutRequest({ body }),
});
