/**
 * This handler to get rates.
 */
import { apiEndpoints, httpCode } from '@/data';
import { BackendClient } from '@/lib';
import { settingApiResponseSchema } from '@/store/setting';
import createApiError from '@/utils/api-utils/create-api-error';
import createApiResponse from '@/utils/api-utils/create-api-response';
import { getJwt } from '@/utils/api-utils/jwt';

import { NextApiRequest, NextApiResponse } from 'next';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token } = await getJwt(req);
  if (req.method === 'GET') {
    try {
      const { data } = await BackendClient(req).get(apiEndpoints.setting(), {
        headers: {
          authorization: token,
        },
      });

      return createApiResponse(res, settingApiResponseSchema, {
        data: data?.data,
      });
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }
  const error = createApiError({ error: '' });
  return res.status(httpCode.INTERNAL_SERVER_ERROR).json(error);
}

export default handler;
