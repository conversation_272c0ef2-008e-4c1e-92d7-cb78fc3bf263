import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { NavigationProgress, nprogress } from '@mantine/nprogress';
import { useMantineTheme } from '@mantine/core';
import { ROUTES } from '@/data';
// this components for navbar progress
export default function RouterTransition() {
  const router = useRouter();
  const theme = useMantineTheme();
  useEffect(() => {
    const handleStart = (url: string) => url !== router.asPath && nprogress.start();
    const handleComplete = () => nprogress.complete();

    router.events.on('routeChangeStart', handleStart);
    router.events.on('routeChangeComplete', handleComplete);
    router.events.on('routeChangeError', handleComplete);

    return () => {
      router.events.off('routeChangeStart', handleStart);
      router.events.off('routeChangeComplete', handleComplete);
      router.events.off('routeChangeError', handleComplete);
    };
    // eslint-disable-next-line  react-hooks/exhaustive-deps
  }, [router.asPath]);
  const returnColor = () => {
    if (
      router.pathname === '/'
      || router.pathname === ROUTES.merchant.path
      || router.pathname === ROUTES.agent.path
    ) return '#7ACB53';
    if (router.pathname === ROUTES.myAccount.path) return 'rgba(0,0,0,0)';
    if (theme.colorScheme === 'light') return theme.colors.primary[7];
    return '#79CA53';
  };
  return (
    <NavigationProgress
      progressLabel="nv progress"
      color={returnColor()}
      autoReset
    />
  );
}
