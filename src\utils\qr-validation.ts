/**
 * QR Code validation utilities for account ID validation and QR data processing
 */

// Account ID validation regex (assuming alphanumeric with possible special characters)
const ACCOUNT_ID_REGEX = /^k\d+$/;

export interface QRValidationResult {
  isValid: boolean;
  accountId?: string;
  error?: string;
  errorKey?: string;
}

export interface QRValidationOptions {
  currentUserAccountId?: string;
  allowSelfTransfer?: boolean;
  minLength?: number;
  maxLength?: number;
}

/**
 * Validates if a string is a valid account ID format
 */
export const isValidAccountId = (accountId: string): boolean => {
  const trimmed = accountId.trim();
  return trimmed.length >= 3 && trimmed.length <= 50 && ACCOUNT_ID_REGEX.test(trimmed);
};

/**
 * Validates if an account identifier (email or account ID) is valid
 */
export const isValidAccountIdentifier = (identifier: string): boolean => {
  const trimmed = identifier.trim();
  return isValidAccountId(trimmed);
};

/**
 * Extracts account ID from QR code data
 */
export const extractAccountIdFromQR = (qrData: string): string => {
  const trimmed = qrData.trim();

  try {
    // Try to parse as URL first
    const url = new URL(trimmed);
    const accountIdParam = url.searchParams.get('accountId');

    if (accountIdParam) {
      return accountIdParam.trim();
    }

    // Check for accountId in pathname (fallback for malformed URLs)
    const pathMatch = url.pathname.match(/accountId=([^/&]+)/);
    if (pathMatch) {
      return pathMatch[1].trim();
    }
  } catch {
    // Not a valid URL, check if it's a direct account ID
    // If it contains URL-like patterns but failed to parse, try regex extraction
    const urlPattern = /accountId=([^&\s]+)/i;
    const match = trimmed.match(urlPattern);
    if (match) {
      return match[1].trim();
    }
  }

  // Return as direct account ID if no URL patterns found
  return trimmed;
};

/**
 * Validates QR code data and extracts account ID with simplified validation
 */
export const validateQRData = (qrData: string): QRValidationResult => {
  const MISSING_ACCOUNT_ID_ERROR = 'QR code missing account ID';

  if (!qrData || typeof qrData !== 'string') {
    return {
      isValid: false,
      error: 'Invalid QR code format',
      errorKey: 'errors:invalidQrCodeFormatInvalidUrl',
    };
  }

  const accountId = extractAccountIdFromQR(qrData.trim());

  // Reject if pattern does not match “k” + digits
  if (!isValidAccountId(accountId)) {
    return {
      isValid: false,
      error: MISSING_ACCOUNT_ID_ERROR,
      errorKey: 'errors:invalidQrCodeFormatMissingAccountId',
    };
  }

  return { isValid: true, accountId };
};

/**
 * Sanitizes account ID input
 */
export const sanitizeAccountId = (accountId: string): string => accountId.trim().toLowerCase();

/**
 * Validates if QR code contains a valid transfer URL
 */
export const isValidTransferQR = (qrData: string): boolean => {
  try {
    const url = new URL(qrData.startsWith('http') ? qrData : `https://${qrData}`);

    // Check if it's a transfer-related URL
    const isTransferPath = url.pathname.includes('transfer')
                          || url.searchParams.has('accountId');

    return isTransferPath && !!url.searchParams.get('accountId');
  } catch {
    // If not a URL, check if it's a direct account ID
    return isValidAccountIdentifier(qrData);
  }
};

/**
 * Creates a standardized error message for QR validation failures
 */
export const getQRValidationErrorMessage = (
  errorKey: string,
  t: (key: string) => string,
): string => {
  const translatedMessage = t(errorKey);

  // Fallback to English if translation key not found
  if (translatedMessage === errorKey) {
    const fallbackMessages: Record<string, string> = {
      'errors:invalidQrCodeFormatInvalidUrl': 'Invalid QR code format',
      'errors:invalidQrCodeFormatMissingAccountId': 'QR code missing account ID',
    };

    return fallbackMessages[errorKey] || 'Invalid QR code';
  }

  return translatedMessage;
};
