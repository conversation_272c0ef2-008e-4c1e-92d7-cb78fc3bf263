/**
 * There are two handlers: get user transfers data create transfer.
 */
import { apiEndpoints, httpCode, playCaptcha } from '@/data';
import { BackendClient } from '@/lib';
import { createTransferBackendRequestSchema } from '@/store/transfer';
import { returnTransfersParams } from '@/store/transfer/params';
import {
  transferApiResponseSchema,
  transfersApiResponseSchema,
} from '@/store/transfer/responses-transformers';
import { verifyCode } from '@/utils/2fa/verify-code';
import createApiError from '@/utils/api-utils/create-api-error';
import createApiResponse from '@/utils/api-utils/create-api-response';
import { getJwt } from '@/utils/api-utils/jwt';
import { captchaValidator } from '@/utils/captcha-validator';
import { NextApiRequest, NextApiResponse } from 'next';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token, email } = await getJwt(req);
  const params = req.query;
  // this function to get list of transfers
  // get filter and pagination as params
  // return list of transfers with pagination

  if (req.method === 'GET') {
    // *****************************************************
    // if there are filters by status or payment method will process this filter without send
    // request to backend server ,because these params don't exist in transfers table.
    if (
      params?.paymentMethod
      || (params?.status && params?.status !== 'approved')
    ) {
      return createApiResponse(res, transfersApiResponseSchema, {
        meta: {
          pagination: {
            page: 0,
            pageSize: 10,
            pageCount: 0,
            total: 0,
          },
        },
        data: [],
      });
    }
    // *****************************************************************
    try {
      const { data } = await BackendClient(req).get(apiEndpoints.transfers(), {
        headers: {
          authorization: token,
        },
        params: { ...returnTransfersParams(req, email) },
      });
      return createApiResponse(res, transfersApiResponseSchema, data);
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
    // this function to create transfer
    // return created transfer details
  } else if (req.method === 'POST') {
    try {
      // get token "chaKey" returned from captha and pass it to "captchaValidator" to check toke validity
      // if validation success will call api ,else return error.
      const chaKey = req?.query?.chaKey;
      await captchaValidator({
        token: chaKey as string,
        secret: process.env.RECAPTCHA_SECRET_KEY as string,
        active: playCaptcha,
      });
      /**
       * If user enabled 2FA and type of payment is "transfer" of "payment_link", then he should add pin code get it from google authenticator app to complete transfer operation
       * If there is a qrCode, then will verify code validity and complete operation if return true
       */
      const qrCode = req?.query?.qrCode;
      if (req.body?.type === 'transfer' || req.body?.type === 'payment_link') {
        await verifyCode(qrCode as string, token, req);
      }

      const { data } = await BackendClient(req).post(
        apiEndpoints.transfers(),
        createTransferBackendRequestSchema.parse(req.body),
        {
          headers: {
            authorization: token,
          },
          params: {
            'populate[currency_from][populate]': '*',
            'populate[currency_to][populate]': '*',
          },
        },
      );
      return res
        .status(httpCode.SUCCESS)
        .json(transferApiResponseSchema(data?.data));
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }
  const error = createApiError({ error: '' });
  return res.status(httpCode.INTERNAL_SERVER_ERROR).json(error);
}

export default handler;
