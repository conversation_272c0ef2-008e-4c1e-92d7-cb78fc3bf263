import { apiEndpoints } from '@/data';
import { ApiClient } from '@/lib';
import { CreateGiveawayApiRequest } from './types';
import { handleApiError } from '@/utils/api-utils/handle-backend-error';

enum queryKeys {
  create = 'create',
}
/**
 * @description function calls handler in "/giveaway" api route to create giveaway.
 * @param body
 * @returns success message if success and error if failed
 */
const createGiveawayRequest = ({
  body,
  chaKey,
}: {
  body: CreateGiveawayApiRequest;
  chaKey?: string;
}) => ApiClient.post(apiEndpoints.giveaway(), body, {
  params: {
    chaKey,
  },
})
  .then((res) => res.data)
  .catch((err) => {
    handleApiError(err);
    throw err;
  });

export const createGiveawayMutation = () => ({
  mutationKey: [queryKeys.create],
  mutationFn: ({
    body,
    chaKey,
  }: {
    body: CreateGiveawayApiRequest;
    chaKey?: string;
  }) => createGiveawayRequest({ body, chaKey }),
});
