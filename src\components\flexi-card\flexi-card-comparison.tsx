/* eslint-disable react/no-array-index-key */
import React from 'react';
import { Table, Text, useMantineTheme } from '@mantine/core';
import { IconCheck, IconX } from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';

export function FlexiCardComparison() {
  const { t } = useTranslation();
  const theme = useMantineTheme();

  const comparisonData = {
    headers: [t('common:merchants'), 'SGD Premium', 'USD Zero', 'USD Basic', 'USD Pro'],
    sections: [
      {
        title: t('common:thirdPartyPayment'),
        rows: [
          { feature: 'Apple Pay', values: [false, false, false, true] },
          { feature: 'Google Pay', values: [false, false, false, true] },
          { feature: 'Paypal', values: [true, false, false, true] },
          { feature: 'Alipay', values: [true, true, true, true] },
          { feature: 'Wechat Pay', values: [true, true, true, true] },
          { feature: 'Line Pay', values: [true, true, true, true] },
        ],
      },
      {
        title: t('common:ai'),
        rows: [
          { feature: 'ChatGPT', values: [true, true, true, true] },
          { feature: 'OpenAI', values: [false, true, true, true] },
          { feature: 'Claude AI', values: [false, true, true, true] },
          { feature: 'Anthropic', values: [true, true, true, true] },
          { feature: 'Midjourney', values: [false, false, false, true] },
          { feature: 'DeepL', values: [true, true, true, true] },
          { feature: 'Perplexity', values: [true, true, true, true] },
        ],
      },
      {
        title: t('common:onlinePayments'),
        rows: [
          { feature: 'Amazon', values: [true, true, true, true] },
          { feature: 'eBay', values: [true, true, true, true] },
          { feature: 'Walmart', values: [true, true, true, true] },
          { feature: 'Alibaba', values: [true, true, true, true] },
        ],
      },
    ],
  };

  const Check = <IconCheck size={16} color={theme.colors.green[6]} />;
  const Cross = <IconX size={16} color={theme.colors.red[6]} />;

  return (
    <Table verticalSpacing="md">
      <thead>
        <tr>
          {comparisonData.headers.map((header, index) => (
            <th key={index}>{header}</th>
          ))}
        </tr>
      </thead>
      <tbody>
        {comparisonData.sections.map((section, sectionIndex) => (
          <React.Fragment key={sectionIndex}>
            <tr>
              <td colSpan={comparisonData.headers.length}>
                <Text weight={700}>{section.title}</Text>
              </td>
            </tr>
            {section.rows.map((row, rowIndex) => (
              <tr key={`${sectionIndex}-${rowIndex}`}>
                <td>{row.feature}</td>
                {row.values.map((value, valueIndex) => (
                  <td key={valueIndex}>{value ? Check : Cross}</td>
                ))}
              </tr>
            ))}
          </React.Fragment>
        ))}
      </tbody>
    </Table>
  );
}
