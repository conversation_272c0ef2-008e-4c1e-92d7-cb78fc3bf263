import { Layout } from '@/components/layout/layout';
import { useRouter } from 'next/router';
import FlexiCardApply from './apply';
import { useEffect } from 'react';
import { FlexiCardDashboard } from '@/components/flexi-card/flexi-card-dashboard/flexi-card-dashboard';
import { mockCards } from '@/components/flexi-card/mock-data';

export default function FlexiCardPage() {
  const router = useRouter();
  const isApplyPage = router.pathname === '/flexi-card/apply';

  useEffect(() => {
    if (!isApplyPage && mockCards.length === 0) {
      router.replace('/flexi-card/apply');
    }
  }, [isApplyPage, router]);

  return (
    <Layout>
      {isApplyPage ? <FlexiCardApply /> : <FlexiCardDashboard />}
    </Layout>
  );
}
