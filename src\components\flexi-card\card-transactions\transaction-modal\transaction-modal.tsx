/* eslint-disable complexity */
/* eslint-disable max-lines */
import {
  Mo<PERSON>, Stack, Group, Text, Divider, Box, ActionIcon, Badge, Button,
  ScrollArea, CopyButton, Tooltip,
  useMantineTheme,
  rem,
} from '@mantine/core';
import {
  IconCopy, IconCheck, IconClock, IconAlertCircle,
  IconShoppingBag, IconCreditCard, IconArrowUp, IconArrowDown,
  IconSettings, IconFileText,
} from '@tabler/icons-react';
import { useState, useEffect } from 'react';
import useTranslation from 'next-translate/useTranslation';
import { PrivateNoteModal } from './private-note-modal';

export interface TransactionData {
  id: string;
  description: string;
  amount: number;
  date: string;
  time?: string;
  type: 'purchase' | 'topup' | 'fee' | 'other' | 'refund' | 'maintenance';
  status: 'completed' | 'pending' | 'failed' | 'cancelled';
  activity?: string;
  card?: string;
  netAmountCredited?: number;
  generalTxnFee?: number;
  txnRef?: string;
  descriptor?: string;
  privateNote?: string;
  originalAmount?: number;
  originalCurrency?: string;
}

interface TransactionModalProps {
  opened: boolean;
  onClose: () => void;
  transaction: TransactionData;
  // eslint-disable-next-line react/require-default-props
  onMakeNote?: () => void;
  // eslint-disable-next-line react/require-default-props
  onSaveNote?: (note: string) => void;
  // eslint-disable-next-line react/require-default-props
  noteModalOpened?: boolean;
  // eslint-disable-next-line react/require-default-props
  onCloseNoteModal?: () => void;
}

export function TransactionModal({
  opened,
  onClose,
  transaction,
  onMakeNote,
  onSaveNote,
  noteModalOpened = false,
  onCloseNoteModal,
}: TransactionModalProps) {
  const { t } = useTranslation();
  const [noteText, setNoteText] = useState(transaction.privateNote || '');
  const theme = useMantineTheme();

  // Update noteText when transaction changes
  useEffect(() => {
    setNoteText(transaction.privateNote || '');
  }, [transaction.privateNote]);

  const handleMakeNote = () => {
    if (onMakeNote) {
      onMakeNote();
    }
  };

  const handleSaveNote = (note: string) => {
    setNoteText(note);
    if (onSaveNote) {
      onSaveNote(note);
    }
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'purchase':
        return <IconShoppingBag size={32} color="#6c757d" />;
      case 'topup':
        return <IconArrowDown size={32} color="#6c757d" />;
      case 'fee':
        return <IconArrowUp size={32} color="#6c757d" />;
      case 'maintenance':
        return <IconSettings size={32} color="#6c757d" />;
      case 'refund':
        return <IconArrowDown size={32} color="#6c757d" />;
      case 'other':
        return <IconFileText size={32} color="#6c757d" />;
      default:
        return <IconCreditCard size={32} color="#6c757d" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'teal';
      case 'pending':
        return 'blue';
      case 'failed':
      case 'cancelled':
        return 'red';
      default:
        return 'gray';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <IconCheck size={14} style={{ marginTop: 4 }} />;
      case 'pending':
        return <IconClock size={14} style={{ marginTop: 4 }} />;
      case 'failed':
      case 'cancelled':
        return <IconAlertCircle size={14} style={{ marginTop: 4 }} />;
      default:
        return <IconClock size={14} style={{ marginTop: 4 }} />;
    }
  };

  const formatAmount = (amount: number) => {
    const sign = amount >= 0 ? '+' : '-';
    const color = amount >= 0 ? '#00b894' : '#000';
    return {
      text: `${sign}$${Math.abs(amount).toFixed(2)} USD`,
      color,
    };
  };

  const formatDateTime = (date: string, time?: string) => {
    if (time) {
      return `${date} ${time}`;
    }
    return date;
  };

  const amountDisplay = formatAmount(transaction.amount);

  return (
    <>
      <Modal
        opened={opened}
        onClose={onClose}
        title=""
        centered
        size="sm"
        radius="xl"
        scrollAreaComponent={ScrollArea.Autosize}
        zIndex={1000}
        styles={{
          header: {
            padding: '20px 24px 0 24px',
            borderBottom: 'none',
          },
          body: {
            padding: '0 24px 24px 24px',
          },
          close: {
            border: 'none',
            backgroundColor:
        theme.colorScheme === 'dark'
          ? theme.colors.dark[5]
          : '#f8f9fa',
            borderRadius: '50%',
            width: rem(32),
            height: rem(32),
            color:
        theme.colorScheme === 'dark'
          ? theme.white
          : '#495057',
            '&:hover': {
              backgroundColor:
          theme.colorScheme === 'dark'
            ? theme.colors.dark[4]
            : '#e9ecef',
            },
          },
          content: {
            backgroundColor:
        theme.colorScheme === 'dark'
          ? theme.colors.dark[7]
          : theme.white,
            zIndex: 1000,
          },
          overlay: {
            backgroundColor: 'rgba(0, 0, 0, 0.4)',
            zIndex: 999,
          },
        }}
      >
        <Stack spacing={0}>
          {/* Header */}
          <Group position="apart" align="flex-start" mb="lg">
            <Text size="lg" weight={600}>
              {t('common:activities')}
            </Text>
          </Group>

          {/* Transaction Icon and Amount */}
          <Stack spacing="xs" align="center" mb="xl">
            <Box mb="xs">
              {getTransactionIcon(transaction.type)}
            </Box>

            <Text size="sm" align="center" mb="xs">
              {transaction.description}
            </Text>

            <Text
              size="lg"
              fz={40}
              weight={700}
              align="center"
              sx={{ fontSize: '24px' }}
            >
              {amountDisplay.text}
            </Text>
          </Stack>

          {/* Transaction Details */}
          <Stack spacing="md">
            {/* Status */}
            <Group position="apart" align="center">
              <Text size="sm">
                {t('common:status')}
              </Text>
              <Badge
                color={getStatusColor(transaction.status)}
                variant="filled"
                leftSection={getStatusIcon(transaction.status)}
                sx={{
                  textTransform: 'capitalize',
                  fontSize: '12px',
                  fontWeight: 500,
                  height: '24px',
                  paddingLeft: '8px',
                  paddingRight: '12px',
                }}
              >
                {transaction.status}
              </Badge>
            </Group>

            {/* Activity */}
            {transaction.activity && (
            <Group position="apart" align="center">
              <Text size="sm">
                {t('common:activities')}
              </Text>
              <Text size="sm" weight={500}>
                {transaction.activity}
              </Text>
            </Group>
            )}

            {/* Card */}
            {transaction.card && (
            <Group position="apart" align="center">
              <Text size="sm">
                {t('common:card')}
              </Text>
              <Text size="sm" weight={500}>
                {transaction.card}
              </Text>
            </Group>
            )}

            {/* Net Amount Credited */}
            {transaction.netAmountCredited !== undefined && (
            <>
              <Divider color="#f1f3f4" />
              <Group position="apart" align="center">
                <Text size="sm">
                  {t('common:netAmountCredited')}
                </Text>
                <Text size="sm" weight={500}>
                  $
                  {transaction.netAmountCredited.toFixed(2)}
                  {' '}
                  USD
                </Text>
              </Group>
            </>
            )}

            {/* General Transaction Fee */}
            {transaction.generalTxnFee !== undefined && (
            <Group position="apart" align="center">
              <Text size="sm">
                {t('common:generalTxnFee')}
              </Text>
              <Text size="sm" weight={500}>
                $
                {transaction.generalTxnFee.toFixed(2)}
                {' '}
                USD
              </Text>
            </Group>
            )}

            {/* Divider before transaction time */}
            <Divider color="#f1f3f4" />

            {/* Transaction Time */}
            <Group position="apart" align="center">
              <Text size="sm">
                {t('common:txnTime')}
              </Text>
              <Text size="sm" weight={500}>
                {formatDateTime(transaction.date, transaction.time)}
              </Text>
            </Group>

            {/* Descriptor */}
            {transaction.descriptor && (
            <Group position="apart" align="center">
              <Text size="sm">
                {t('common:descriptor')}
              </Text>
              <Text size="sm" weight={500}>
                {transaction.descriptor}
              </Text>
            </Group>
            )}

            {/* Transaction Reference */}
            {transaction.txnRef && (
            <Group position="apart" align="flex-start">
              <Text size="sm">
                {t('common:txnRef')}
              </Text>
              <Group spacing="xs" align="center">
                <Text
                  size="sm"
                  weight={500}
                  sx={{
                    wordBreak: 'break-all',
                    maxWidth: '180px',
                    color: '#8b5a2b',
                    lineHeight: 1.4,
                  }}
                >
                  {transaction.txnRef}
                </Text>
                <CopyButton value={transaction.txnRef}>
                  {({ copied, copy }) => (
                    <Tooltip label={copied ? t('common:copied') : t('common:copy')}>
                      <ActionIcon
                        size="xs"
                        variant="subtle"
                        onClick={copy}
                      >
                        {copied ? <IconCheck size={12} /> : <IconCopy size={12} />}
                      </ActionIcon>
                    </Tooltip>
                  )}
                </CopyButton>
              </Group>
            </Group>
            )}

            {/* Divider before private note */}
            <Divider color="#f1f3f4" />

            {/* Private Note */}
            <Group position="apart" align="center">
              <Text size="sm">
                {t('common:privateNote')}
              </Text>
              {noteText ? (
                <Group spacing="xs" align="center">
                  <Text size="sm" weight={500}>
                    {noteText}
                  </Text>
                  <Button
                    variant="subtle"
                    size="xs"
                    onClick={handleMakeNote}
                    sx={{
                      padding: 0,
                      height: 'auto',
                      fontWeight: 500,
                      fontSize: '14px',
                      color: '#007bff',
                      '&:hover': {
                        backgroundColor: 'transparent',
                        textDecoration: 'underline',
                      },
                    }}
                  >
                    {t('common:edit')}
                  </Button>
                </Group>
              ) : (
                <Button
                  variant="subtle"
                  size="xs"
                  onClick={handleMakeNote}
                  sx={{
                    padding: 0,
                    height: 'auto',
                    fontWeight: 500,
                    fontSize: '14px',
                    '&:hover': {
                      backgroundColor: 'transparent',
                      textDecoration: 'underline',
                    },
                  }}
                >
                  {t('common:makeANote')}
                </Button>
              )}
            </Group>
          </Stack>
        </Stack>
      </Modal>

      {/* Private Note Modal - Independent */}
      <PrivateNoteModal
        opened={noteModalOpened}
        onClose={onCloseNoteModal || (() => {})}
        onSave={handleSaveNote}
        initialNote={noteText}
      />
    </>
  );
}
