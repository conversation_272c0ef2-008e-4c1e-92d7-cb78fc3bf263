import { ApiClient } from '@/lib';
import { handleApiError } from '@/utils/api-utils/handle-backend-error';

enum queryKeys{
  email='email'
}

interface EmailBody {
  to: string;
  subject: string;
  text: string;
}
/**
 * @description function calls handler in "/email" api route to sent email
 * @param body
 * @returns success or error if failed
 */
const sentEmailRequest = ({ body }: { body: EmailBody }) => ApiClient.post('/email', body)
  .then((res) => res.data)
  .catch((e) => {
    handleApiError(e);
    throw e.response.data;
  });

export const sentEmailMutation = () => ({
  mutationKey: [queryKeys.email],
  mutationFn: ({ body }: { body: EmailBody }) => sentEmailRequest({ body }),
});
