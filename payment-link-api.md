# Create Payment Link

To create a payment link, you can use the following API endpoint. This allows you to initiate a payment process for a specified amount and currency.

### HTTP Request

```bash

POST https://outdoor.kasroad.com/wallet/createPaymentLink

```

### Headers

- `x-api-key`: Your API key
- `Content-Type`: application/json

### Request Body

```json
{
  "amount": "YOUR_AMOUNT_HERE",
  "currency": "USD",
  "email": "YOUR_EMAIL_HERE",
  "ref": "YOUR_UNIQUE_REFERENCE",
  "redirectUrl": "https://yourwebsite.com?kazawallet=success"
}

```

### cURL Example

```bash
curl -X POST 'https://outdoor.kasroad.com/wallet/createPaymentLink' \\
-H 'x-api-key: XXXXXXXXXX' \\
-H 'Content-Type: application/json' \\
-d '{
  "amount": "YOUR_AMOUNT_HERE",
  "currency": "USD",
  "email": "YOUR_EMAIL_HERE",
  "redirectUrl": "https://yourwebsite.com?kazawallet=success"
}'

```

## Webhook Notification

To receive a notification when a user completes a payment, you must set up a webhook URL in your profile. Once the payment is made, a POST request with the payment details will be sent to your webhook URL **ONLY ONE TIME**.

### Webhook Payload

The webhook will send a POST request with the following information:

- `order_id`: The Id of the payment link
- `secret`: A secret code for verification
- `amount`: The amount paid
- `ref`: Sent with the payload, for example you can use it to Reference the user id in your system
- `status`: Can be "fulfilled" or "timed_out", this will indicate whether the link is paid or not.
- `currency`: The code of used currency.

## Security Verification

To verify the authenticity of the webhook request, you need to calculate a verification code and compare it with the `secret` received from the API. Below is a PHP example of how to perform this verification.

### PHP Example

```php
<?php

$amount = 'your_amount'; // Replace with the actual amount
$order_id = 'your_order_id'; // Replace with the actual payment link ID
$kazawalletApiKey = 'your_kazawallet_api_key'; // Replace with the actual kazawallet API key
$kazawalletApiSecret = 'your_kazawallet_api_secret'; // Replace with the actual kazawallet API secret

// Create the secret string by concatenating the amount, order_id, and kazawalletApiKey
$secretString = $amount . ':::' . $order_id . ':::' . $kazawalletApiKey;

// Generate a SHA-256 hash of the secret string
$hashDigest = hash('sha256', $secretString, true);

// Generate an HMAC-SHA512 hash of the SHA-256 hash using the kazawalletApiSecret
$hmacDigest = hash_hmac('sha512', $hashDigest, $kazawalletApiSecret, true);

// Encode the HMAC-SHA512 hash in Base64
$hmacDigestBase64 = base64_encode($hmacDigest);

// Output the Base64-encoded HMAC-SHA512 signature
echo $hmacDigestBase64;

?>

```

### Verification Process

To ensure the security of the webhook notification, perform the following steps:

1. Receive the webhook POST request with the `order_id`, `secret`, `status` and `amount`.
2. The provided PHP code is just a dummy example to demonstrate how to calculate the HMAC-SHA512 signature based on the received `order_id`, `status` and `amount`, along with your `kazawalletApiKey` and `kazawalletApiSecret`.
3. Compare the calculated HMAC-SHA512 signature with the `secret` received from the webhook.
4. If they match, the request is authentic, and you can proceed with processing the payment confirmation.

Please ensure that your API key and secret are kept confidential and are not exposed in client-side code.


&nbsp;

# Create Withdrawal Request

### Description
This API endpoint allows you to create a withdrawal request from a user's wallet. The request includes details such as the user's email, the currency, the amount to withdraw, a note, the payment method, and any custom fields required by the payment method.

### Endpoint
`POST https://outdoor.kasroad.com/wallet/createWithdrawalRequest`

### Headers
- `x-api-key`: Your API key.
- `x-api-secret`: Your API secret (you can request it from the support team).
- `Content-Type`: `application/json`

### Request Body
| Field          | Type   | Description                                                                 |
|----------------|--------|-----------------------------------------------------------------------------|
| `email`        | string | The email address of the merchent.                                              |
| `currency`     | string | The currency code (e.g., `USD`).                                            |
| `amount`       | string | The amount to withdraw.                                                     |
| `note`         | string | A note for the withdrawal request.                                          |
| `paymentMethod`| string | The ID of the payment method.                                               |
| `fields`       | object | A set of custom fields based on the payment method.                         |


### Example Request Body

```
{
    "email": "<EMAIL>",
    "currency": "USD",
    "amount": "10",
    "note": "This is a note",
    "paymentMethod": "xx",
    "fields": {
        "field1": "xxx",
        "field2": "yyy"
    }
}

```

### Example Success Response

```
{
    "success": true,
    "status": "approved",
    "message": "Withdrawal request created successfully"
}
```

### cURL Example

```sh
curl --location 'https://outdoor.kasroad.com/wallet/createWithdrawalRequest' \
--header 'x-api-key: xxxxxxxxxxx' \
--header 'x-api-secret: xxxxxxxxxxx' \
--header 'Content-Type: application/json' \
--data-raw '{
    "email": "<EMAIL>",
    "currency": "USD",
    "amount": "10",
    "note": "This is a note",
    "paymentMethod": "xx",
    "fields": {
        "field1": "xxxx",
        "field2": "yyyy"
    }
}'
```

### PHP Example
```
<?php

$curl = curl_init();

$data = [
    "email" => "<EMAIL>",
    "currency" => "USD",
    "amount" => "10",
    "note" => "This is a note",
    "paymentMethod" => "xx",
    "fields" => [
        "field1" => "xxxx",
        "field2" => "yyyy"
    ]
];

curl_setopt_array($curl, [
    CURLOPT_URL => 'https://outdoor.kasroad.com/wallet/createWithdrawalRequest',
    CURLOPT_CUSTOMREQUEST => 'POST',
    CURLOPT_POSTFIELDS => json_encode($data),
    CURLOPT_HTTPHEADER => [
        'x-api-key: xxxxxxxxxxx',
        'x-api-secret: xxxxxxxxxxx',
        'Content-Type: application/json'
    ],
]);

$response = curl_exec($curl);

curl_close($curl);
echo $response;
?>
```

### Payment Methods

Ensure all submitted data is valid and accurate to prevent account restrictions and rejections. Repeated rejections may result in the revocation of your merchant privileges.

| Payment Method            | ID  | Custom Fields                                                                 |
|---------------------------|-----|-------------------------------------------------------------------------------|
| Haram less than 3M        | 37  | - `name`: The full name of the recipient, (e.g., `سعيد محمد حمدان`).                                          |
|                           |     | - `phone`: The phone number of the recipient.                                 |
|                           |     | - `hawalahmauto-custom-field-withdraw-3`: The governorate of the recipient.   |
| Haram more than 3M        | 35  | - `name`: The name of the recipient.                                          |
|                           |     | - `phone`: The phone number of the recipient.                                 |
|                           |     | - `hawalahmauto-custom-field-withdraw-3`: The governorate of the recipient.   |
| SY Cash                   | 38  | - `phone`: The phone number of the recipient.                                 |



### Notes

-   Ensure that the  `x-api-key`  and  `x-api-secret`  headers are correctly set with your API credentials.
-   The  `fields`  object should contain the necessary custom fields based on the selected payment method.