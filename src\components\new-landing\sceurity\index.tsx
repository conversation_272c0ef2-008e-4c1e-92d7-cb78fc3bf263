import {
  Container,
  Text,
  Image,
  SimpleGrid,
  Stack,
  Box,
  BackgroundImage,
  useMantineTheme,
} from '@mantine/core';
import styles from './styles.module.scss';
import useTranslation from 'next-translate/useTranslation';
import { assetBaseUrl } from '@/data';
import { useRouter } from 'next/router';

export default function Security() {
  const { t } = useTranslation();
  const style = useMantineTheme();
  const router = useRouter();
  return (
    <Box className={styles.root}>
      <Container
        py={40}
        px={{
          xs: 20,
          sm: 30,
          lg: 40,
          base: 20,
        }}
        size={1200}
      >
        <SimpleGrid
          cols={2}
          breakpoints={[
            { maxWidth: 'md', cols: 2, spacing: 'md' },
            { maxWidth: 'sm', cols: 1, spacing: 'sm' },
            { maxWidth: 'xs', cols: 1, spacing: 'sm' },
          ]}
        >
          <Stack
            justify="center"
            sx={(theme) => ({
              [theme.fn.smallerThan('sm')]: {
                display: 'none',
              },
            })}
          >
            <BackgroundImage w={500} src={`${assetBaseUrl}/assets/new-landing/blur-hero.webp`} radius="xs">
              <Image
                width={480}
                src={`${assetBaseUrl}/assets/new-landing/security.webp`}
                alt="security"
                mx="auto"
              />
            </BackgroundImage>
          </Stack>
          <Stack justify="center">
            <Text
              color="white"
              ff={router.locale === 'ar' ? style.fontFamily : `BauhausC, ${style.fontFamily}`}
              size={43}
              weight="bold"
            >
              {t('common:securityLandingTitle')}
            </Text>
            <Text lh={1.7} size="lg" mt="lg" color="dimmed">
              {t('common:securityLandingDescriptionFirst')}
            </Text>
            <Text lh={1.7} size="lg" color="dimmed">
              {t('common:securityLandingDescriptionSecond')}
            </Text>
          </Stack>
          <Stack
            justify="center"
            sx={(theme) => ({
              display: 'none',
              [theme.fn.smallerThan('sm')]: {
                display: 'block',
              },
            })}
          >
            <BackgroundImage mx="auto" w={300} src={`${assetBaseUrl}/assets/new-landing/blur-hero.webp`} radius="xs">
              <Image
                width={300}
                src={`${assetBaseUrl}/assets/new-landing/security.webp`}
                alt="security"
                mx="auto"
              />
            </BackgroundImage>
          </Stack>
        </SimpleGrid>
      </Container>
    </Box>
  );
}
