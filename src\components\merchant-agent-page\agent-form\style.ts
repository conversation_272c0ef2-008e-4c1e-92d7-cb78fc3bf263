import { createStyles } from '@mantine/core';

export const useStyles = createStyles(() => ({
  input: {
    label: {
      color: 'white',
    },
    input: {
      backgroundColor: 'inherit',
      color: 'white',
    },
    '.mantine-ltr-Textarea-input ,.mantine-rtl-Textarea-input ,.mantine-rtl-MultiSelect-input,.mantine-ltr-MultiSelect-input':
      {
        backgroundColor: 'inherit',
        color: 'white',
      },
  },
}));
