import {
  UpdateUserApiRequest,
  updateUserApiRequestSchema,
  updateUserMutation,
  UserApiResponse,
} from '@/store/user';
import { preserveUserData } from '@/utils/profile-utils';
import {
  Button, Paper, Stack, TextInput,
} from '@mantine/core';
import { useForm, zodResolver } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { IconWebhook } from '@tabler/icons-react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import useTranslation from 'next-translate/useTranslation';
import React from 'react';
import ApiKeySection from '../api-key-section/api-key-section';

interface MerchantTabProps {
  data: UserApiResponse | undefined;
}
function MerchantTab({ data }: MerchantTabProps) {
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  const form = useForm<UpdateUserApiRequest>({
    initialValues: {
      webhookUrl: data?.webhookUrl,
    },
    validate: zod<PERSON><PERSON>olver(updateUserApiRequestSchema),
  });

  const { mutate, isLoading } = useMutation(updateUserMutation().mutationFn, {
    onSuccess() {
      queryClient.invalidateQueries(['user']);
      notifications.show({
        message: t('common:merchantSettingsUpdateSuccess'),
        color: 'blue',
      });
      form.resetDirty();
    },
  });

  const submit = (values: UpdateUserApiRequest) => {
    mutate(preserveUserData(values, data));
  };
  return (
    <Stack spacing={0} align="stretch" m="auto" w="100%" maw={650}>
      <Paper p="md" withBorder radius="lg">
        <form onSubmit={form.onSubmit(submit)}>
          <TextInput
            disabled={isLoading}
            radius="lg"
            label={`${t('common:webhookUrl')}`}
            placeholder={`${t('common:webhookUrl')}`}
            type="url"
            icon={<IconWebhook />}
            {...form.getInputProps('webhookUrl')}
          />

          <Button
            disabled={!form.isDirty()}
            mt="lg"
            type="submit"
            fullWidth
            radius="lg"
          >
            {t('common:save')}
          </Button>
        </form>
      </Paper>
      <ApiKeySection data={data} />
    </Stack>
  );
}

export default MerchantTab;
