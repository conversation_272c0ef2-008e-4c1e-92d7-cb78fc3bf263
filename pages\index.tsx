/**
 * This component renders landing page.
 */
import MetaTags from '@/components/common/meta-tags';
import HeroSection from '@/components/new-landing/heroSection';
import InfoSection from '@/components/new-landing/infoSection';
import About from '@/components/new-landing/about';
import Security from '@/components/new-landing/sceurity';
import Advantages from '@/components/new-landing/advantages';
import Download from '@/components/new-landing/download';
import { Box, Text, useMantineTheme } from '@mantine/core';

import useTranslation from 'next-translate/useTranslation';

import { getRatesQuery } from '@/store/rate';
import { ExchangeRatesList } from '@/components/rates/exhange-rates';
import { Layout } from '@/components/layout/layout';
import { useSession } from 'next-auth/react';
import Payment from '@/components/new-landing/payment-section';

import { useQuery } from '@tanstack/react-query';
import AgentProgram from '@/components/new-landing/agent-program';
import { useRouter } from 'next/router';

export default function Home() {
  const { t } = useTranslation();
  const theme = useMantineTheme();
  const { status } = useSession();
  const authenticated = status === 'authenticated';
  const { data, isLoading } = useQuery(getRatesQuery());
  const router = useRouter();

  return (
    <div
      style={{
        backgroundColor: 'rgb(0, 13, 35)',
      }}
    >
      <MetaTags />
      <Layout>
        <HeroSection isAuth={authenticated} />
        <InfoSection />
        <div style={{ paddingTop: 40 }} id="rates-section">
          <Box p={40} mx="auto" maw={800}>
            <Text
              ff={router.locale === 'ar' ? theme.fontFamily : `BauhausC, ${theme.fontFamily}`}
              ta="center"
              mb="lg"
              tt="capitalize"
              weight="bold"
              size={43}
              color="white"
            >
              {t('common:rates')}
            </Text>
            <ExchangeRatesList
              color={theme.colors.gray[5]}
              forLandingPage
              rates={data}
              isLoading={isLoading}
            />
          </Box>
        </div>
        <About />
        <Security />
        <Payment />
        <AgentProgram />
        <Advantages />
        <Download isAuth={authenticated} />
      </Layout>
    </div>
  );
}

export async function getServerSideProps() {
  return { props: {} };
}
