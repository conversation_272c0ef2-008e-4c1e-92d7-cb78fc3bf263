/* eslint-disable no-nested-ternary */
/**
 * User response schema with support for dynamic payment methods and fixed operations
 */
import { z } from 'zod';
import {
  currencyApiResponseSchema,
  currencyBackendSchema,
} from '../currencies';
import { imageBackedSchema } from '../currencies/responses-transformers';
import { countries } from 'countries-list';

export enum KYC_STATUS {
  Pending = 'pending',
  Approved = 'approved',
  Rejected = 'rejected',
  Disabled = 'disabled',
}

const currencyLimitSchema = z.object({
  count: z.number().optional(),
  amount: z.number().optional(),
  symbol: z.string().optional(),
  precision: z.string().optional(),
});

const paymentMethodSchema = z.object({
  deposit: z.record(currencyLimitSchema).optional(),
  withdraw: z.record(currencyLimitSchema).optional(),
});

const operationSchema = z.record(currencyLimitSchema);

const limitsSchema = z.record(z.union([
  paymentMethodSchema,
  operationSchema,
])).and(z.object({
  exchange: operationSchema.optional(),
  transfer: operationSchema.optional(),
  payment_link: operationSchema.optional(),
}));

// Main user backend schema
export const userBackendSchema = z.object({
  id: z.number().optional(),
  account_id: z.string(),
  email: z.string(),
  username: z.string(),
  first_name: z.string(),
  last_name: z.string(),
  country: z.string().nullable(),
  authenticator_enabled: z.boolean().nullable(),
  authenticator_token: z.string().nullable(),
  api_key: z.string().nullable(),
  webhook_url: z.string().nullable(),
  admin_role: z.enum(['admin', 'super admin', 'merchant', 'agent']).nullable(),
  avatar: z
    .object({
      id: z.number(),
      name: z.string().optional(),
      url: z.string(),
      formats: z
        .object({
          thumbnail: z.object({
            url: z.string(),
          }),
        })
        .nullable(),
    })
    .nullable()
    .optional(),
  user_currencies: z
    .array(
      z.object({
        id: z.number(),
        amount: z.string().nullable(),
        currency: currencyBackendSchema,
      }),
    )
    .optional(),
  favorite_currencies: z.array(currencyBackendSchema).optional(),
  badges: z
    .array(
      z.object({
        id: z.number(),
        title: z.string(),
        title_ar: z.string().nullable(),
        description: z.string().nullable(),
        description_ar: z.string().nullable(),
        logo: imageBackedSchema.nullable().optional(),
        order: z.number(),
        limits: limitsSchema.optional().nullable(),
      }),
    )
    .nullable()
    .optional(),
  send_verification_conditions: z
    .enum(['never', 'ip_change', 'subnet_change', 'browser_change', 'always'])
    .nullable(),
  kyc: z
    .object({
      status: z
        .enum([
          KYC_STATUS.Approved,
          KYC_STATUS.Disabled,
          KYC_STATUS.Pending,
          KYC_STATUS.Rejected,
        ])
        .nullable()
        .optional(),
      mobile: z.string().nullable().optional(),
      preferredCommunicationMethod: z.string().nullable().optional(),
      country: z.string().nullable().optional(),
      id1: z.string().nullable().optional(),
      id2: z.string().nullable().optional(),
      selfie: z.string().nullable().optional(),
      agreetc: z.boolean().nullable().optional(),
      adminNote: z.string().nullable().optional(),
    })
    .nullable()
    .optional(),
});

const transformCurrencyLimits = (
  limits: Record<string, {
    count?: number;
    amount?: number;
    symbol?: string;
    precision?: string;
  }> | undefined,
) => {
  if (!limits) {
    return [];
  }

  return Object.entries(limits).map(([currency, limit]) => {
    let precision = 3;
    if (limit.amount !== undefined && limit.amount !== null) {
      const amountStr = limit.amount.toString();
      if (amountStr.includes('.')) {
        precision = 3;
      } else {
        precision = 0;
      }
    }

    return {
      currency,
      count: limit.count ?? '-',
      amount: limit.amount,
      symbol: limit.symbol ?? currency,
      precision,
    };
  });
};

const transformPaymentMethod = (
  method: {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    deposit?: Record<string, any>;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    withdraw?: Record<string, any>;
  } | undefined,
) => {
  if (!method) {
    return null;
  }

  return {
    deposit: transformCurrencyLimits(method.deposit),
    withdraw: transformCurrencyLimits(method.withdraw),
  };
};

const transformOperation = (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  operation: Record<string, any> | undefined,
) => {
  if (!operation) {
    return null;
  }
  return transformCurrencyLimits(operation);
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const limitsTransformer = (limits: Record<string, any> | undefined | null) => {
  if (!limits) {
    return undefined;
  }

  const {
    // eslint-disable-next-line camelcase
    exchange, transfer, payment_link, ...paymentMethods
  } = limits;

  return {
    paymentMethods: Object.entries(paymentMethods).reduce(
      (acc, [key, value]) => ({
        ...acc,
        [key]: transformPaymentMethod(value),
      }),
      {} as Record<string, ReturnType<typeof transformPaymentMethod>>,
    ),
    operations: {
      exchange: transformOperation(exchange),
      transfer: transformOperation(transfer),
      payment_link: transformOperation(payment_link),
    },
  };
};

// Main API response schema with transformations
export const userApiResponseSchema = z
  .object({
    data: userBackendSchema,
  })
  // eslint-disable-next-line sonarjs/cognitive-complexity
  .transform((data) => ({
    accountId: data.data.account_id,
    email: data.data.email,
    firstName: data.data.first_name,
    lastName: data.data.last_name,
    country: data.data.country,
    authenticatorEnabled: data.data.authenticator_enabled,
    hasAuthenticatorToken: !!data.data.authenticator_token,
    apiKey: data.data.api_key,
    webhookUrl: data.data.webhook_url,
    role: data.data.admin_role,
    avatar: data.data.avatar
      ? {
        name: data.data.avatar.name,
        url:
            data.data.avatar.formats?.thumbnail.url ?? data.data.avatar.url,
      }
      : undefined,
    currencies: data.data.user_currencies?.map((i) => ({
      id: i.currency?.id,
      value: `${i.currency?.id}`,
      amount: i.amount ? +i.amount : 0,
      ...currencyApiResponseSchema(i.currency),
      image:
        i.currency?.icon?.url
        || (i.currency?.icon?.data?.attributes?.formats?.thumbnail.url
          ?? i.currency?.icon?.data?.attributes?.url),
    })) ?? [],
    favoriteCurrencies: data.data.favorite_currencies?.map((i) => ({
      id: i?.id,
      value: `${i?.id}`,
      amount: 0,
      ...currencyApiResponseSchema(i),
      image:
        i?.icon?.url
        || (i?.icon?.data?.attributes?.formats?.thumbnail.url
          ?? i?.icon?.data?.attributes?.url),
    })) ?? [],
    badges: data.data.badges?.map((i) => ({
      title: i.title,
      titleAr: i.title_ar,
      description: i.description,
      descriptionAr: i.description_ar,
      image: i.logo?.formats?.thumbnail.url ?? i.logo?.url,
      order: i.order,
      limits: limitsTransformer(i.limits),
    })) ?? [],
    sendVerificationConditions: data.data.send_verification_conditions,
    kyc:
      data.data.kyc?.status === KYC_STATUS.Rejected
        ? {
          status: data.data.kyc.status,
          adminNote: data.data.kyc.adminNote,
          mobile: undefined,
          agreetc: undefined,
          country: undefined,
          id1: undefined,
          id2: undefined,
          selfie: undefined,
          preferredCommunicationMethod: undefined,
        }
        : data.data.kyc?.mobile && data.data.kyc.country
          ? {
            ...data.data.kyc,
            mobile: data.data.kyc.mobile.slice(
              countries[data.data.kyc.country as keyof typeof countries].phone
                .length + 1,
            ),
          }
          : data.data.kyc,
  }));
