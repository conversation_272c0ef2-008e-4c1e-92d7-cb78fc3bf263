import { useViewportSize } from '@mantine/hooks';
import { RESPONSIVE_BREAKPOINTS, QR_SIZES, QRSizeConfig } from '../qr-constants';

export const useResponsiveQRSizes = () => {
  const { width: viewportWidth } = useViewportSize();

  const isMobile = viewportWidth < RESPONSIVE_BREAKPOINTS.MOBILE;
  const isTablet = viewportWidth >= RESPONSIVE_BREAKPOINTS.MOBILE && viewportWidth < RESPONSIVE_BREAKPOINTS.TABLET;

  const getResponsiveSizes = (): QRSizeConfig => {
    if (isMobile) {
      return QR_SIZES.MOBILE;
    }
    if (isTablet) {
      return QR_SIZES.TABLET;
    }
    return QR_SIZES.DESKTOP;
  };

  const sizes = getResponsiveSizes();

  return {
    sizes,
    isMobile,
    isTablet,
    viewportWidth,
  };
};
