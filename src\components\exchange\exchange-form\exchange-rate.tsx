import { RatesApiResponse } from '@/store/rate';
import { UserApiResponse } from '@/store/user';
import { FeesExchangeCalculate } from '@/utils/fees-functions/fee-exchange';
import { Group, Paper, Text } from '@mantine/core';
import currency from 'currency.js';
import useTranslation from 'next-translate/useTranslation';
import React from 'react';

interface Props {
  currencyGiveItem: UserApiResponse['currencies'][0] | undefined;
  currencyGetItem: UserApiResponse['currencies'][0] | undefined;
  dataRates: RatesApiResponse;
  fees: number;
}

function ExchangeRateAndFees({
  currencyGetItem,
  currencyGiveItem,
  dataRates,
  fees,
}: Props) {
  const { t } = useTranslation();
  const { exchangeRate, commissionFacts, reverseExchangeRate } = FeesExchangeCalculate(
    currencyGiveItem?.code,
    currencyGetItem,
    dataRates,
    1,
    false,
  );

  return (
    <Paper p="sm" withBorder radius="lg">
      <Group position="apart">
        <span>
          <Text span weight={500}>
            {`${t('common:conversionFee')}: `}
          </Text>
          {currency(fees, {
            symbol: '',
            precision: currencyGetItem?.precision ?? 2,
          }).format()}
          {' '}
          {commissionFacts && (
            <Text span size="sm" weight={500}>
              (
              {commissionFacts}
              )
            </Text>
          )}
          {' '}
          <Text span size="sm" weight={500}>
            {currencyGetItem?.symbol}
          </Text>
        </span>
        <Group position="left" spacing={3}>
          {(currencyGiveItem || currencyGetItem) && (
            <Text weight={500} span>
              {`${t('common:rate')}: `}
            </Text>
          )}
          {currencyGiveItem && (
            <div>
              <Text span>1</Text>
              <Text mx={2} span>
                {currencyGiveItem?.symbol}
              </Text>
              =
            </div>
          )}
          {currencyGetItem && (
            <div>
              <Text span>{exchangeRate ? `${exchangeRate}` : ''}</Text>
              <Text mx={2} span>
                {currencyGetItem?.symbol}
              </Text>
            </div>
          )}
          ~
          {currencyGetItem && (
            <div>
              <Text span>1</Text>
              <Text mx={2} span>
                {currencyGetItem?.symbol}
              </Text>
              =
            </div>
          )}
          {currencyGiveItem && (
            <div>
              <Text span>
                {reverseExchangeRate ? `${reverseExchangeRate}` : ''}
              </Text>
              <Text mx={2} span>
                {currencyGiveItem?.symbol}
              </Text>
            </div>
          )}
        </Group>
      </Group>
    </Paper>
  );
}

export default ExchangeRateAndFees;
