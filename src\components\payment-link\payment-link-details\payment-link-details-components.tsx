import { ColorSchemeFactory } from '@/components/common/color-scheme-component';
import { ResponsiveFactory } from '@/components/common/responsive-component';
import { logo } from '@/data';
import {
  AspectRatio, Button, Group, Image, Title,
} from '@mantine/core';
import dynamic, { Loader } from 'next/dynamic';
import { useRouter } from 'next/router';
import { CountdownProps } from 'react-countdown';

export const RTitle = ResponsiveFactory({
  component: Title,
  sharedProps: { order: 5, weight: 600 },
});
export const RTitle1 = ResponsiveFactory({
  component: Title,
  sharedProps: { order: 5, weight: 700 },
});
export const CTitle1 = ColorSchemeFactory({
  component: RTitle1,
  defaultProps: {
    dark: { sharedProps: { color: 'white', opacity: 0.8 } },
    light: { sharedProps: { color: 'gray' } },
  },
});
export const RButton = ResponsiveFactory({
  component: Button,
  sharedProps: {
    color: 'green',
    w: 300,
    radius: 'md',
  },
  defaultProps: { xs: { w: 280 } },
});
export const RGroup = ResponsiveFactory({
  component: Group,
  sharedProps: {
    position: 'apart',
    px: 50,
    mt: 60,
  },
  defaultProps: {
    xs: { px: 10 },
  },
});
export function ReturnLogo() {
  const { locale } = useRouter();
  return (
    <AspectRatio ratio={1 / 2} w={100} h={50}>
      <Image
        alt="kazaWallet logo"
        src={locale === 'ar' ? logo.fullAr : logo.fullEn}
      />
    </AspectRatio>
  );
}

export const CountDown = dynamic(
    import('react-countdown').then((e) => e.default) as Loader<CountdownProps>,
    { ssr: false },
);
