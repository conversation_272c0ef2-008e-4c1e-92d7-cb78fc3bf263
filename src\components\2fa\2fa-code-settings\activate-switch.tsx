import CustomSwitch from '@/components/common/custom-switch/custom-switch';
import { updateUserMutation, UserApiResponse } from '@/store/user';
import { preserveUserData } from '@/utils/profile-utils';
import { getCookie, setCookie } from 'cookies-next';
import React, { useEffect, useState } from 'react';

import useTranslation from 'next-translate/useTranslation';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { verify2faTokenMutation } from '@/store/2fa/calls';
import { CodeConfirmModal } from '../code-confirm-modal';
import { notifications } from '@mantine/notifications';
import { ConfirmModal } from '@/components/common/confirm-modal';
import { useDisclosure } from '@mantine/hooks';

interface Props {
  data: UserApiResponse | undefined;
  firstEnableAuthCallbackFn: () => void;
  onDisableAuthCallbackFn: () => void;
  initialLoading: boolean;
}
function ActivateSwitch({
  data,
  firstEnableAuthCallbackFn,
  initialLoading,
  onDisableAuthCallbackFn,
}: Props) {
  const { t } = useTranslation();
  const [codeError, setCodeError] = useState('');
  const [code, setCode] = useState('');
  const [opened, { open, close }] = useDisclosure(false);
  const [openedCode, setOpenedCode] = useState(false);
  const [authEnabled, setAuthEnabled] = useState(!!data?.authenticatorEnabled);
  const queryClient = useQueryClient();
  // update user mutation
  const { mutate: mutateUser, isLoading } = useMutation({
    ...updateUserMutation(),
    onSuccess(res) {
      setAuthEnabled(!!res?.authenticatorEnabled);
      setOpenedCode(false);
      setCode('');
      setCodeError('');
      close();
      setCookie('2fa-enabled', !!res?.authenticatorEnabled);
      queryClient.invalidateQueries(['user']);
      notifications.show({
        message: t('common:addAuthenticatorSettingsSuccess'),
        color: 'blue',
      });
    },
  });

  const { mutate, isLoading: loading } = useMutation({
    ...verify2faTokenMutation(),
    onSuccess: () => {
      mutateUser(preserveUserData({
        authenticatorEnabled: false,
      }, data));
      onDisableAuthCallbackFn();
    },
    onError: () => {
      setCodeError('invalidCode');
      setCode('');
    },
  });

  useEffect(() => {
    setAuthEnabled(!!data?.authenticatorEnabled);
  }, [data?.authenticatorEnabled]);

  const handleChange = (v: 'true' | 'false') => {
    if (v === 'true') {
      if (data?.hasAuthenticatorToken) {
        open();
      } else firstEnableAuthCallbackFn();
    }
    if (v === 'false' && getCookie('2fa-enabled')) {
      setOpenedCode(true);
    }
  };
  return (
    <div>
      <CustomSwitch
        disabled={isLoading || initialLoading}
        label=""
        onChange={handleChange}
        value={authEnabled ? 'true' : 'false'}
        width="100%"
      />

      <CodeConfirmModal
        title={t('common:enterCode')}
        codeError={codeError}
        btnText={t('common:confirm')}
        setOpenedCode={setOpenedCode}
        isLoading={loading}
        onClick={mutate}
        openedDefault={openedCode}
        code={code}
        setCode={setCode}
        closeable={false}
        additionalContent={undefined}
      />
      <ConfirmModal
        isLoading={isLoading}
        onClick={() => mutateUser(preserveUserData({
          authenticatorEnabled: true,
        }, data))}
        message={t('common:enableGoogleAuthenticator')}
        openedDefault={opened}
        close={close}
      />
    </div>
  );
}

export default ActivateSwitch;
