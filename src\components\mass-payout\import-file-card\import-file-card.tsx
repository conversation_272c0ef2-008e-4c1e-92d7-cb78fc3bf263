/* eslint-disable @typescript-eslint/no-explicit-any */
import { Icon } from '@/components/common/icon';
import SubmitButton from '@/components/common/submit-button';
import { assetBaseUrl } from '@/data';
import {
  MassPayoutApiResponse,
  TransferItemType,
  checkMassPayoutMutation,
} from '@/store/mass-payout';
import {
  Button,
  Card,
  Group,
  Input,
  Stack,
  Image,
  Text,
  useMantineTheme,
  ActionIcon,
  Tooltip,
  Paper,
} from '@mantine/core';
import { Dropzone } from '@mantine/dropzone';
import { useMutation } from '@tanstack/react-query';
import useTranslation from 'next-translate/useTranslation';
import Papa from 'papaparse';
import { equals } from 'ramda';
import React, { ReactNode, useState } from 'react';

const allowedExtensions = ['csv', 'vnd.ms-excel'];

interface Props {
  setFileDataChecked: (v: TransferItemType[]) => void;
  setCurrenciesSelectDisabled: (v: boolean) => void;
  disabled: boolean;
  children: ReactNode;
  setResponseData: (v: MassPayoutApiResponse['data']) => void;
  selectedCurrencyId: string | number;
  setGeneralError:(v:string)=>void
}
export default function ImportFileCard({
  setFileDataChecked,
  children,
  disabled,
  setCurrenciesSelectDisabled,
  setResponseData,
  selectedCurrencyId,
  setGeneralError,
}: Props) {
  const { t } = useTranslation();
  const theme = useMantineTheme();
  const [file, setFile] = useState<any>('');
  const [error, setError] = useState('');

  // call check mass payout api to check uploaded file items before create mass payout transfers
  // When success will set the transfer items after checking in the state and set the general error if founded in the state
  const { mutate, isLoading } = useMutation({
    ...checkMassPayoutMutation(),
    onSuccess: (res) => {
      setFileDataChecked(res?.data);
      setGeneralError(res?.generalError);
    },
  });
  // Handle file change function
  // When change file will delete error,generale error and previous uploaded file data
  // and check file format and extension
  const handleFileChange = (e: any) => {
    setError('');
    setGeneralError('');
    setFileDataChecked([]);
    if (e) {
      const inputFile = e[0];
      const fileExtension = inputFile?.type.split('/')[1];
      if (!allowedExtensions.includes(fileExtension)) {
        setError('pleaseEnterCsvFile');
        return;
      }
      setFile(inputFile);
      setCurrenciesSelectDisabled(true);
    }
  };
  // This function to read items from the file and mutate check mass payout api
  // In this function will check if items data are completed and correspond with file format
  // If the item data is not completed will discard it and will not display it to the user.
  const handleParse = () => {
    setResponseData([]);
    if (!file) return setError('enterValidFile');

    const reader = new FileReader();
    reader.onload = async ({ target }: { target: any }) => {
      const csv = Papa.parse(target.result, { header: true });
      const parsedData: any[] = csv?.data;
      const keys = parsedData.length > 0 ? Object.keys(parsedData[0]) : '';
      // validate csv file columns *************
      if (equals(keys, ['id', 'amount', 'account', 'note'])) {
        // checking item data integration
        const realData = parsedData?.filter(
          (i) => i.amount !== '' && i.account !== '' && i.id !== '',
        );
        // if the file does not contain data
        if (realData?.length === 0) return setError('thisFileIsEmpty');
        // get data items list
        const dataArr = realData.map((i) => ({
          id: i?.id,
          amount: i?.amount.trim(),
          account: i?.account.trim(),
          note: i?.note.trim(),
        }));
        // mutate check mass payout api to check data
        mutate({
          body: {
            currency: selectedCurrencyId ?? '',
            transfers: dataArr?.map((i) => ({
              account: i?.account,
              amount: `${i?.amount}`,
              notes: i?.note,
            })),
          },
        });
        return realData;
      }
      return setError('incorrectFileFormatting');
    };
    reader.readAsText(file);
    return true;
  };
  // on click to reset button will reset all data and errors
  function handleReset() {
    setGeneralError('');
    setFile('');
    setFileDataChecked([]);
    setResponseData([]);
    setCurrenciesSelectDisabled(false);
  }
  return (
    <Paper mb="md" shadow="sm" px="lg" py="md" radius={25} withBorder>
      <Group position="right" align="center">
        <Tooltip label={t('common:reset')}>
          <ActionIcon onClick={() => handleReset()} size={40} radius={50}>
            <Icon icon="reload" size={24} color="dark" />
          </ActionIcon>
        </Tooltip>
      </Group>
      {children}
      <Stack>
        <Button
          component="a"
          download
          href="/file/file-test.csv"
          radius="lg"
          variant="outline"
          leftIcon={<Icon icon="file-download" size="1.4rem" color="dark" />}
        >
          {t('common:clickHereToGetFileTemplate')}
        </Button>
        <Card radius="lg" p={0} sx={{ borderBottom: 0 }}>
          <Dropzone
            disabled={disabled}
            sx={{
              zIndex: 1,
              borderColor:
                theme.colorScheme === 'light'
                  ? theme.colors.gray[2]
                  : theme.colors.gray[8],
              borderBottomLeftRadius: 10,
              borderBottomRightRadius: 10,
            }}
            p={0}
            onDrop={handleFileChange}
            mih={70}
            radius="lg"
            accept={{
              'text/csv': ['.csv'],
            }}
            onReject={(errors) => {
              setError(t(`errors:${errors[0].errors[0].code}`) as string);
            }}
          >
            <Group p="xs" spacing="xs" align="center">
              <Dropzone.Accept>
                <Icon
                  icon="upload"
                  size={50}
                  color={
                    theme.colors[theme.primaryColor][
                      theme.colorScheme === 'dark' ? 4 : 6
                    ]
                  }
                />
              </Dropzone.Accept>
              <Dropzone.Reject>
                <Icon
                  icon="x"
                  size={50}
                  color={theme.colors.red[theme.colorScheme === 'dark' ? 4 : 6]}
                />
              </Dropzone.Reject>
              <Dropzone.Idle>
                <Image
                  alt="csv-icon"
                  src={`${assetBaseUrl}/assets/icons/csv.jpeg`}
                  width={45}
                  mt="xs"
                  radius="md"
                />
              </Dropzone.Idle>

              <div style={{ textAlign: 'left' }}>
                <Group mt="xs">
                  <Text size={14} weight={500} inline align="left">
                    {t(`common:${file ? 'fileSelected' : 'selectCsvFile'}`)}
                  </Text>
                  {file && (
                    <Icon icon="circle-check" size="1.2rem" color="dark" />
                  )}
                </Group>
                <Text mt="xs">CSV</Text>
              </div>
            </Group>
          </Dropzone>
          {!!error && <Input.Error p="xs">{t(`common:${error}`)}</Input.Error>}
        </Card>
        <SubmitButton
          loading={isLoading}
          disabled={error !== '' || file === ''}
          onClick={() => {
            handleParse();
          }}
        >
          {t('common:checkFileTransfers')}
        </SubmitButton>
      </Stack>
    </Paper>
  );
}
