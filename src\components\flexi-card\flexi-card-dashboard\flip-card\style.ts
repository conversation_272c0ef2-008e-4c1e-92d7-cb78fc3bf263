/* eslint-disable sonarjs/no-duplicate-string */
import { rem, createStyles } from '@mantine/core';

export const useFlipStyles = createStyles((theme) => ({
  root: {
    perspective: '1200px',
    width: '100%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    padding: '0 10px', // Add small padding to prevent edge touching
  },

  card: {
    position: 'relative',
    width: '100%',
    maxWidth: rem(380), // Maximum size on desktop
    aspectRatio: '86 / 54',
    transformStyle: 'preserve-3d',
    transition: 'transform 600ms cubic-bezier(0.2, 0.7, 0.2, 1)',
    cursor: 'pointer',
    outline: 'none',
    borderRadius: rem(16),

    // Simple responsive sizing
    [theme.fn.smallerThan('sm')]: {
      maxWidth: rem(320),
      borderRadius: rem(14),
    },
  },

  flipped: {
    transform: 'rotateY(180deg)',
  },

  face: {
    position: 'absolute',
    inset: 0,
    borderRadius: 'inherit',
    overflow: 'hidden',
    backfaceVisibility: 'hidden',
    WebkitBackfaceVisibility: 'hidden',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
    padding: rem(20),

    [theme.fn.smallerThan('sm')]: {
      padding: rem(16),
    },
  },

  front: {
    background: 'repeating-linear-gradient(45deg, rgba(255,255,255,0.03) 0px, rgba(255,255,255,0.03) 1px, rgba(255,255,255,0) 2px), #111',
    color: theme.white,
    zIndex: 2,
  },

  back: {
    background: 'repeating-linear-gradient(45deg, rgba(255,255,255,0.03) 0px, rgba(255,255,255,0.03) 1px, rgba(255,255,255,0) 2px), #111',
    color: theme.white,
    transform: 'rotateY(180deg)',
    zIndex: 1,
  },

  brand: {
    fontWeight: 800,
    letterSpacing: 0.3,
    fontSize: rem(18),
    [theme.fn.smallerThan('sm')]: {
      fontSize: rem(16),
    },
  },

  sub: {
    opacity: 0.7,
    fontSize: rem(12),
  },

  row: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: rem(8),
    flexWrap: 'wrap',
  },

  hintBar: {
    position: 'absolute',
    left: rem(20),
    right: rem(20),
    top: '50%',
    transform: 'translateY(-50%)',
    background: 'linear-gradient(180deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.08) 100%)',
    padding: rem(10),
    borderRadius: rem(8),
    display: 'flex',
    alignItems: 'center',
    gap: rem(8),
    justifyContent: 'center',
    fontWeight: 600,
    fontSize: rem(14),
    backdropFilter: 'blur(4px)',

    [theme.fn.smallerThan('sm')]: {
      padding: rem(8),
      fontSize: rem(13),
    },
  },

  chipRow: {
    display: 'flex',
    gap: rem(12),
    paddingBottom: rem(8),
    opacity: 0.7,
  },

  mono: {
    fontFamily: 'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace',
    letterSpacing: 1,
    fontSize: rem(15),

    [theme.fn.smallerThan('sm')]: {
      fontSize: rem(13),
    },
  },
  field: {
    marginBottom: rem(10),
    padding: rem(10),
    borderRadius: theme.radius.sm,
    backgroundColor: theme.colorScheme === 'dark'
      ? theme.colors.dark[6]
      : theme.colors.gray[0],
  },

  valueRow: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: rem(4),
  },

  iconBtn: {
    color: theme.colorScheme === 'dark'
      ? theme.colors.gray[5]
      : theme.colors.gray[6],
    '&:hover': {
      backgroundColor: theme.colorScheme === 'dark'
        ? theme.colors.dark[5]
        : theme.colors.gray[1],
    },
  },

  monoWrap: {
    fontFamily: 'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace',
    letterSpacing: 1,
    fontSize: rem(15),
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',

    [theme.fn.smallerThan('sm')]: {
      fontSize: rem(13),
      maxWidth: '80%',
    },
  },
}));
