import { CustomCopyButton } from '@/components/common/custom-copy-button';
import { TranslatedTextValue } from '@/components/common/translated-text-value';
import { DATE_FORMAT } from '@/data';
import { AgentPaymentApiResponse } from '@/store/agent';
import { CUSTOM_FIELDS_TYPE } from '@/types/custom-fields-type.type';
import {
  Anchor, Group, Image, Paper, Stack, Text,
} from '@mantine/core';
import currency from 'currency.js';
import dayjs from 'dayjs';
import useTranslation from 'next-translate/useTranslation';
import React from 'react';

interface AgentPaymentDetailsProps {
  agentPaymentData: AgentPaymentApiResponse;
  toBeDeposit: number;
  fees: number;
}
function AgentPaymentDetails(props: AgentPaymentDetailsProps) {
  const { agentPaymentData, toBeDeposit, fees } = props;
  const { t } = useTranslation();
  const renderFields = () => agentPaymentData?.fields?.map((item) => {
    if (
      item?.type === CUSTOM_FIELDS_TYPE.string
        || item?.type === CUSTOM_FIELDS_TYPE.number
        || item?.type === CUSTOM_FIELDS_TYPE.list
    ) {
      return (
        <Group key={item?.name}>
          <Text tt="capitalize" weight={500} miw={170} w="30%">
            <TranslatedTextValue
              keyEn={item?.name ?? ''}
              keyAr={item?.nameAr}
            />
          </Text>
          <Text>{`${item?.value}`}</Text>
        </Group>
      );
    }
    if (item?.type === CUSTOM_FIELDS_TYPE.multiple) {
      return (
        <Group align="start" key={item?.name}>
          <Text tt="capitalize" weight={500} miw={170} w="30%">
            <TranslatedTextValue
              keyEn={item?.name ?? ''}
              keyAr={item?.nameAr}
            />
          </Text>
          <Stack>
            {item?.value?.map((i: string) => (
              <Text key={i}>{i}</Text>
            ))}
          </Stack>
        </Group>
      );
    }
    if (item?.type === CUSTOM_FIELDS_TYPE.datetime) {
      return (
        <Group key={item?.name}>
          <Text tt="capitalize" weight={500} miw={170} w="30%">
            <TranslatedTextValue
              keyEn={item?.name ?? ''}
              keyAr={item?.nameAr}
            />
          </Text>
          <Text>{dayjs(`${item?.value}`).format(DATE_FORMAT)}</Text>
        </Group>
      );
    }
    return (
      <Stack key={item?.name}>
        <Text tt="capitalize" weight={500} miw={170} maw={170} w="30%">
          <TranslatedTextValue
            keyEn={item?.name ?? ''}
            keyAr={item?.nameAr}
          />
        </Text>
        <Anchor target="_blank" href={item?.value?.url}>
          <Image width={300} src={item?.value?.url} alt="payment-method" />
        </Anchor>
      </Stack>
    );
  });
  return (
    <Paper w="100%" p="md" withBorder radius="lg">
      <Text weight={700}>{t('common:withdrawRequestDetails')}</Text>
      <Group mt="md">
        <Text miw={170} w="30%" weight={500}>
          {t('common:currency')}
        </Text>
        <Group>
          <Image width={35} src={agentPaymentData?.currency?.image} />
          <Text>
            <TranslatedTextValue
              keyEn={agentPaymentData?.currency?.label}
              keyAr={agentPaymentData?.currency?.labelAr}
            />
          </Text>
        </Group>
      </Group>
      <Group mt="md">
        <Text miw={170} w="30%" weight={500}>
          {t('common:operationNo')}
        </Text>
        <Group spacing={5}>
          <Text>{agentPaymentData.transactionId}</Text>
          <CustomCopyButton value={`${agentPaymentData.transactionId}`} />
        </Group>
      </Group>
      <Group mt="md">
        <Text miw={170} w="30%" weight={500}>
          {t('common:toBePaid')}
        </Text>
        <Group spacing={5}>
          <Text>
            {currency(agentPaymentData?.actualAmount ?? 0, {
              precision: agentPaymentData?.currency?.precision ?? 0,
              symbol: '',
            }).format()}
          </Text>
          <Text weight={500} span>
            {agentPaymentData?.currency?.symbol}
          </Text>
        </Group>
      </Group>
      <Group mt="md">
        <Text miw={170} w="30%" weight={500}>
          {t('common:yourCommission')}
        </Text>
        <Group spacing={5}>
          <Text>
            {currency(fees, {
              precision: agentPaymentData?.currency?.precision ?? 0,
              symbol: '',
            }).format()}
          </Text>
          <Text weight={500} span>
            {agentPaymentData?.currency?.symbol}
          </Text>
        </Group>
      </Group>
      <Group mt="md">
        <Text miw={170} w="30%" weight={500}>
          {t('common:toBeDeposited')}
        </Text>
        <Group spacing={5}>
          <Text>
            {currency(toBeDeposit, {
              precision: agentPaymentData?.currency?.precision ?? 0,
              symbol: '',
            }).format()}
          </Text>
          <Text weight={500} span>
            {agentPaymentData?.currency?.symbol}
          </Text>
        </Group>
      </Group>
      <Stack mt="md">{renderFields()}</Stack>
    </Paper>
  );
}

export default AgentPaymentDetails;
