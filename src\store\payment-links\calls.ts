import { apiEndpoints } from '@/data';
import { ApiClient } from '@/lib';
import { handleApiError } from '@/utils/api-utils/handle-backend-error';
import {
  CreatePaymentLinkApiRequest,
  PaymentLinksApiResponse,
  getPaymentLinksQueryProps,
} from './types';
import { UseQueryOptions } from '@tanstack/react-query';

enum queryKeys {
  create = 'create',
  paymentLinks = 'payment-links',
}

/** ******************************************** */
/**
 * @description
 * function calls handler in "/payment-links" api route with "get" method to fetch payment link data
 * @param body
 * @returns payment link data or error
 */
const createPaymentLinkRequest = (body: CreatePaymentLinkApiRequest) => ApiClient.post(apiEndpoints.paymentLinks(), body)
  .then((res) => res.data)
  .catch((err) => {
    handleApiError(err);
    throw err;
  });

export const createPaymentLinkMutation = () => ({
  mutationKey: [queryKeys.create],
  mutationFn: (body: CreatePaymentLinkApiRequest) => createPaymentLinkRequest(body),
});

/** ******************************************** */
/**
 * @description
 * function calls handler in "/payment-links" api route to create payment link.
 * @param props
 * @returns payment link url or error
 */
const getPaymentLinksRequest = (props: getPaymentLinksQueryProps) => {
  const { filters } = props;
  return ApiClient.get(apiEndpoints.paymentLinks(), {
    params: {
      uid: filters?.uid,
    },
  })
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e, true);
      throw e.response?.data;
    });
};
export const getPaymentLinksQuery = (
  props: getPaymentLinksQueryProps,
): UseQueryOptions<PaymentLinksApiResponse> => ({
  queryKey: [queryKeys.paymentLinks],
  queryFn: () => getPaymentLinksRequest(props),
  refetchOnWindowFocus: false,
});
/** ******************************************** */
