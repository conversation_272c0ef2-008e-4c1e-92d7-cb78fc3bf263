/**
 * request params to get transfer api call
 */
import { NextApiRequest } from 'next';

export const returnTransfersParams = (
  req: NextApiRequest,
  email: string | null | undefined,
) => {
  const params = req.query;
  return {
    'pagination[page]': params.page,
    'pagination[pageSize]': params.pageSize,
    'pagination[start]': params.start,
    'pagination[limit]': params.limit,
    'filters[type][$eq]': params?.eqType,
    'filters[type][$ne]': params?.nEqType,
    'filters[$or][0][currency_from][id][$eq]': params?.currencyId,
    'filters[$or][1][currency_to][id][$eq]': params?.currencyId,
    'populate[currency_from][populate]': '*',
    'populate[currency_to][populate]': '*',
    'filters[$or][0][user_from][email][$eq]': email,
    'filters[$or][1][user_to][email][$eq]': email,
    'filters[user_to][account_id][$eq]': params?.received,
    'filters[user_from][account_id][$eq]': params?.sent,
    'filters[transaction_id][$containsi]': params?.search,
    'filters[createdAt][$gte]':
      typeof params?.createdFrom === 'string' && params?.createdFrom !== ''
        ? params?.createdFrom
        : null,
    'filters[createdAt][$lte]':
      typeof params?.createdTo === 'string' && params?.createdTo !== ''
        ? params?.createdTo
        : null,
    sort: 'createdAt:desc',
  };
};
