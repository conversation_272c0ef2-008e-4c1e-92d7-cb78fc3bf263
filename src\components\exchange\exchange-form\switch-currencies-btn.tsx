import { Icon } from '@/components/common/icon';
import { UserApiResponse } from '@/store/user';
import { ActionIcon } from '@mantine/core';
import { UseFormReturnType } from '@mantine/form';
import { useRouter } from 'next/router';
import React from 'react';
import { fmt } from './helper';

interface Props {
  setSelectedGet: (v: UserApiResponse['currencies'][0]) => void;
  selectedGet: UserApiResponse['currencies'][0] | undefined;
  setSelectedGive: (v: UserApiResponse['currencies'][0]) => void;
  selectedGive: UserApiResponse['currencies'][0] | undefined;
  setGiveAmount: (v: number | '' | undefined) => void;
  setGetAmount: (v: number | '' | undefined) => void;
  form: UseFormReturnType<
    {
      amount: number | undefined;
      type: string;
    },
    (values: { amount: number | undefined; type: string }) => {
      amount: number | undefined;
      type: string;
    }
  >;
}

function SwitchCurrenciesBtn({
  form,
  selectedGet,
  selectedGive,
  setGetAmount,
  setGiveAmount,
  setSelectedGet,
  setSelectedGive,
}: Props) {
  const { query, push, pathname } = useRouter();

  const onBtnClick = () => {
    if (!(selectedGive && selectedGet)) return;

    /* cache current references *before* mutating state */
    const giveCur = selectedGive;
    const getCur = selectedGet;
    const currentAmount = form.values.amount;

    /* 1. swap currencies */
    setSelectedGive(getCur);
    setSelectedGet(giveCur);

    /* 2. keep the numeric value in the same input field */
    setGiveAmount(currentAmount);
    setGetAmount(undefined); // force recalculation for the other side
    form.setValues({ amount: currentAmount });

    /* 3. update the query‑string */
    const newQuery = {
      ...query,
      currency: getCur.uid,
      to: giveCur.uid,
      ...(typeof currentAmount === 'number' && currentAmount > 0
        ? { amount: String(fmt(currentAmount, getCur)) }
        : {}),
    };

    push({ pathname, query: newQuery }, undefined, { shallow: true });
  };
  return (
    <ActionIcon size="2rem" mx="auto" onClick={onBtnClick}>
      <Icon icon="arrows-up-down" size="2rem" color="gray" />
    </ActionIcon>
  );
}

export default SwitchCurrenciesBtn;
