/**
 * This component renders a wallets page.
 *
 * @description
 * This page used to display:
 * -list of user currencies
 * -carousel of static images
 * -total user balance by "$"
 * -carousel of payment methods with their deposit currencies with rate of each currency
 * When click to currency card will navigate to currency page.
 * There are to buttons the first one to refresh total balance,and the second to navigate t history page.
 */
import { Layout } from '@/components/layout/layout';
import MetaTags from '@/components/common/meta-tags';

import useTranslation from 'next-translate/useTranslation';
import { useQuery } from '@tanstack/react-query';
import { getUserQuery } from '@/store/user';
import { getRatesQuery } from '@/store/rate';

import { StatisticsCarousel } from '@/components/carousel-statistics';

import { ListWallets } from '@/components/wallets/list-wallets';
import { WalletsHero } from '@/components/wallets-hero-section';
import { Button, Group } from '@mantine/core';
import { FeedbackPopup } from '@/components/feedback-popup';
import { useDisclosure } from '@mantine/hooks';

export default function Wallets() {
  const [opened, { open, close }] = useDisclosure(false);
  const { t } = useTranslation();
  const { data: ratesDat, isLoading: isLoadingRates } = useQuery(
    getRatesQuery(),
  );
  // Call get user data api to get user currencies
  const { data, isLoading } = useQuery(getUserQuery({}));

  return (
    <div>
      <MetaTags title={t('common:wallets')} />
      <Layout>
        <WalletsHero />
        <StatisticsCarousel />
        {/* <CarouselRates
          isLoadingRates={ratesData?.isLoading}
          ratesData={ratesData?.data}
        /> */}

        <ListWallets
          isLoading={isLoading}
          data={data?.currencies}
          favoriteCurrencies={data?.favoriteCurrencies}
          isLoadingRates={isLoadingRates}
          ratesData={ratesDat}
        />
        <Group position="center">
          <Button onClick={open} radius={50} w={200} h={45}>
            {t('common:feedback')}
          </Button>
        </Group>
        {data && (
          <FeedbackPopup
            close={close}
            opened={opened}
            userEmail={data?.email ?? ''}
            userFullName={`${data?.firstName ?? ''} ${data?.lastName ?? ''}`}
          />
        )}
      </Layout>
    </div>
  );
}

export async function getServerSideProps() {
  return { props: {} };
}
