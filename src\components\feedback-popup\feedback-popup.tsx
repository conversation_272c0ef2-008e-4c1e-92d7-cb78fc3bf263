import {
  Box,
  Button,
  Group,
  Modal,
  Text,
  Textarea,
  useMantineTheme,
} from '@mantine/core';
import React from 'react';
import useTranslation from 'next-translate/useTranslation';
import { RatingSmiles } from './rating';
import { useForm, zodResolver } from '@mantine/form';
import {
  CreateFeedbackApiRequest,
  createFeedbackFormSchema,
  createFeedbackMutation,
} from '@/store/feedback';
import { useMutation } from '@tanstack/react-query';
import { notifications } from '@mantine/notifications';

interface Props {
  opened: boolean;
  close: () => void;
  userEmail: string;
  userFullName: string;
}
function FeedbackPopup({
  opened, close, userEmail, userFullName,
}: Props) {
  const { t } = useTranslation();
  const theme = useMantineTheme();
  const form = useForm<CreateFeedbackApiRequest>({
    initialValues: {
      email: userEmail,
      feedback: '',
      name: userFullName,
      rate: 0,
      url: window.location.href,
    },

    validate: zodResolver(createFeedbackFormSchema(t)),
  });

  const { mutate, isLoading } = useMutation({
    ...createFeedbackMutation(),
    onSuccess() {
      form.reset();
      close();
      notifications.show({
        message: t('common:feedbackSentSuccessfully'),
        color: 'blue',
      });
    },
    onError() {
      notifications.show({
        message: t('errors:feedbackError'),
        color: 'red',
      });
    },
  });
  const submit = (values: CreateFeedbackApiRequest) => {
    mutate(values);
  };
  const onModalClose = () => {
    form.reset();
    close();
  };
  return (
    <Modal
      radius="lg"
      centered
      opened={opened}
      onClose={onModalClose}
      title={t('common:feedback')}
    >
      <Text mb={4}>
        {' '}
        {t('common:feedbackDescription')}
      </Text>
      <form onSubmit={form.onSubmit(submit)}>
        <Textarea
          required
          radius="md"
          placeholder={t('common:yourFeedback')}
          {...form.getInputProps('feedback')}
        />
        <Box
          mt="md"
          mb={-16}
          mx={-16}
          px={16}
          bg={theme.colorScheme === 'light' ? '#E9ECEF' : theme.colors.dark[6]}
          h={60}
        >
          <Group position="apart" h="100%" align="center">
            <RatingSmiles form={form} />
            <Button loading={isLoading} type="submit">
              {t('common:send')}
            </Button>
          </Group>
        </Box>
      </form>
    </Modal>
  );
}

export default FeedbackPopup;
