import {
  Container,
  Divider,
  Group,
  Paper,
  SimpleGrid,
  Stack,
  Text,
  useMantineTheme,
} from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import {
  IconBusinessplan,
  IconWallet,
  IconCoins,
  IconWorld,
} from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';
import { useRouter } from 'next/router';
import { ReactNode } from 'react';

interface Props {
  icon: ReactNode;
  number: string;
  title: string;
  isLast:boolean;
}

function RenderItem({
  icon, number, title, isLast,
}: Props) {
  const theme = useMantineTheme();
  const matches = useMediaQuery(`(max-width:${theme.breakpoints.md})`);
  return matches ? (
    <>
      <Group spacing="xs" align="center" position="apart">
        {icon}
        <Divider orientation="vertical" color={theme.colors.dark[4]} size={1} />
        <Stack w={150} spacing={5} justify="center" align="start">
          <Text color="white" size={40} weight="bold">
            {' '}
            {number}
          </Text>
          <Text color="dimmed">{title}</Text>
        </Stack>
      </Group>
      <Divider
        orientation="horizontal"
        color={theme.colors.dark[4]}
        size={1}
        sx={{
          display: 'none',
          [theme.fn.smallerThan('xs')]: {
            display: isLast ? 'none' : 'block',
          },
        }}
      />
    </>
  ) : (
    <Group position="apart">
      <Stack spacing={5}>
        {icon}
        <Text color="white" size={40} weight="bold">
          {' '}
          {number}
        </Text>
        <Text color="dimmed">{title}</Text>
      </Stack>
      <Divider
        sx={{
          display: isLast ? 'none' : 'block',
          [theme.fn.smallerThan('xs')]: {
            display: 'none',
          },
        }}
        orientation="vertical"
        h={200}
        color={theme.colors.dark[4]}
        size={1}
      />
    </Group>
  );
}

export default function InfoSection() {
  const { t } = useTranslation();
  const theme = useMantineTheme();
  const router = useRouter();
  return (
    <Container
      py={40}
      px={{
        xs: 20,
        sm: 30,
        lg: 40,
        base: 20,
      }}
      size={1200}
    >
      <Text
        mb={80}
        mx="auto"
        maw={600}
        ta="center"
        weight="bold"
        size={43}
        color="white"
        ff={router.locale === 'ar' ? theme.fontFamily : `BauhausC, ${theme.fontFamily}`}
      >

        {t('common:infoLandingTitle')}
      </Text>
      <Paper
        bg={theme.colors.primary[9]}
        radius="md"
        p={{
          lg: 50, md: 30, sm: 30, xs: 30, base: 20,
        }}
      >
        <SimpleGrid
          mx="auto"
          spacing="lg"
          cols={4}
          breakpoints={[
            { maxWidth: 'md', cols: 2, spacing: 'md' },
            { maxWidth: 'sm', cols: 2, spacing: 'sm' },
            { maxWidth: 'xs', cols: 1, spacing: 'sm' },
          ]}
        >
          <RenderItem
            isLast={false}
            icon={(
              <IconCoins
                color="#0ca678"
                width={55}
                height={55}
                style={{ marginBottom: '25px' }}
              />
            )}
            number="10+"
            title={t('common:globalCurrencies')}
          />
          <RenderItem
            isLast={false}
            icon={(
              <IconBusinessplan
                color="#0ca678"
                width={55}
                height={55}
                style={{ marginBottom: '25px' }}
              />
            )}
            number="5+"
            title={t('common:cryptocurrencies')}
          />
          <RenderItem
            isLast={false}
            icon={(
              <IconWallet
                color="#0ca678"
                width={55}
                height={55}
                style={{ marginBottom: '25px' }}
              />
            )}
            number="50+"
            title={t('common:paymentSystems')}
          />
          <RenderItem
            isLast
            icon={(
              <IconWorld
                color="#0ca678"
                width={55}
                height={55}
                style={{ marginBottom: '25px' }}
              />
            )}
            number="10+"
            title={t('common:countriesSupported')}
          />
        </SimpleGrid>
      </Paper>
    </Container>
  );
}
