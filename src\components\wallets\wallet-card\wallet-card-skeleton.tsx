import { GlobalCard } from '@/components/common/global-card';
import {
  Group, Skeleton,
} from '@mantine/core';

export function WalletCardSkeleton() {
  return (
    <GlobalCard
      onClick={() => {}}
      props={{}}
    >
      <Group position="apart">
        <Skeleton height={20} maw={50} />
        <Skeleton height={20} maw={120} />
        <Skeleton height={40} width={40} circle />
      </Group>
      <Group mt={5} position="center">
        <Skeleton height={30} maw={180} />
      </Group>
      <Group position="center" align="flex-end" spacing="xs" mt={25}>
        <Skeleton height={30} width={150} radius={50} />
        <Skeleton height={30} width={150} radius={50} />
      </Group>
    </GlobalCard>
  );
}
