import { z } from 'zod';
import { currenciesApiResponseSchema } from './responses-transformers';
import { Pagination } from '@/types';
import { QueryFunctionContext } from '@tanstack/react-query';

export type Filter = {};
export interface getCurrenciesQueryProps {
  populate?: {};
  pagination?: Pagination;
  filters?: Filter;
  params?: QueryFunctionContext;
  enabled?: boolean;
}

export type CurrenciesApiResponse = z.infer<typeof currenciesApiResponseSchema>;
