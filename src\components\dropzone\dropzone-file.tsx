import {
  ActionIcon, Card, Group, Title, Avatar, Grid,
} from '@mantine/core';
import Link from 'next/link';
import React from 'react';

import { filePostResponseData } from '@/store/file/response-transformer';
import { Icon } from '../common/icon';
import { assetBaseUrl } from '@/data';

interface DropzoneFileProps {
  deleteFile: (id: number) => void;
  file: filePostResponseData[number];
}
function DropzoneFile(props: DropzoneFileProps) {
  const {
    deleteFile,
    file: { url, id, name },
  } = props;
  //* ************************************************* */
  return (
    <Card radius="md" mt={3} withBorder>
      <Grid justify="space-between" gutter={0}>
        <Grid.Col span={8}>
          <Group position="left" spacing={4}>
            <Avatar
              radius={30}
              size={30}
              src={`${assetBaseUrl}/assets/icons/file.png`}
              maw={30}
              alt="drop"
            />
            <Title
              order={6}
              maw={200}
              lineClamp={1}
              sx={{ textAlign: 'start' }}
            >
              {name}
            </Title>
          </Group>
        </Grid.Col>
        <Grid.Col span={4}>
          <Group spacing={5} position="right">
            <ActionIcon
              variant="default"
              component={Link}
              href={url}
              target="_blank"
            >
              <Icon icon="eye" size={18} color="gray" />
            </ActionIcon>
            <ActionIcon
              variant="default"
              onClick={() => {
                deleteFile(id);
              }}
            >
              <Icon
                icon="trash"
                size={18}
                color="red"

              />
            </ActionIcon>
          </Group>
        </Grid.Col>
      </Grid>
    </Card>
  );
}

export default DropzoneFile;
