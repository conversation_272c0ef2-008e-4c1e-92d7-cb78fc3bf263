/**
 * Function to check if the code is correct.
 * check if the user enabled 2fa and generated authenticator token before ,
 *   if true then check the code verification else return "true"
 */
import { apiEndpoints } from '@/data';
import { BackendClient } from '@/lib';
import { NextApiRequest } from 'next';
import { authenticator } from 'otplib';

// google authenticator code verification in server

export const verifyCode = async (code: string, token: string, req:NextApiRequest) => {
  const { data } = await BackendClient(req).get(`${apiEndpoints.users()}/me`, {
    headers: {
      authorization: token,
    },
  });
  const secret = data?.authenticator_token;
  const enabled = data?.authenticator_enabled;
  if (secret && enabled) {
    const success = authenticator.check(code, secret);
    if (!success) throw new Error('invalidCode');
    return secret;
  }
  return true;
};
