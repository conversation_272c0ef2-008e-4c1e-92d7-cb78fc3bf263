import { RatesApiResponse } from '@/store/rate';
import { UserApiResponse } from '@/store/user';
import currency from 'currency.js';

//  function to calculate the exchange rate for 3 different cases
//  first : Transferring from USD to another currency
//  second: Transferring from any currency to USD
//  third: Transferring between two currencies other than the US dollar
const calculateExchange = (
  fromCurrencyCode: string | undefined,
  toCurrency: UserApiResponse['currencies'][0] | undefined,
  ratesRecords: RatesApiResponse,
) => {
  if (!ratesRecords?.rates) {
    return { exchangeRate: 0, reverseExchangeRate: 0 };
  }

  let exchangeRate = 0;
  let reverseExchangeRate = 0;
  if (fromCurrencyCode && toCurrency) {
    if (fromCurrencyCode === 'USD') {
      exchangeRate = ratesRecords?.rates[`${toCurrency?.code}_FROM`];
      reverseExchangeRate = 1 / ratesRecords.rates[`${toCurrency?.code}_FROM`];
    }
    if (toCurrency?.code === 'USD') {
      exchangeRate = 1 / ratesRecords.rates[`${fromCurrencyCode}_TO`];
      reverseExchangeRate = ratesRecords?.rates[`${fromCurrencyCode}_TO`];
    }
    if (fromCurrencyCode !== 'USD' && toCurrency?.code !== 'USD') {
      exchangeRate = ratesRecords.rates[`${toCurrency?.code}_FROM`]
        / ratesRecords.rates[`${fromCurrencyCode}_TO`];
      reverseExchangeRate = ratesRecords.rates[`${fromCurrencyCode}_TO`]
        / ratesRecords.rates[`${toCurrency?.code}_FROM`];
    }
  }
  return { exchangeRate, reverseExchangeRate };
};

// return commission factories as string

const returnCommissionFacts = (
  currencyItem: UserApiResponse['currencies'][0],
) => `${
  currencyItem.exchangeFeesPercent
    ? `${currencyItem.exchangeFeesPercent}%`
    : ''
} ${
  currencyItem.exchangeFeesFixed
    ? `+${currency(currencyItem.exchangeFeesFixed, {
      precision: currencyItem?.precision ?? 0,
      symbol: '',
    }).format()}`
    : ''
}`;

//  function to calculate the amount by actual amount and fees in exchange page
//   Ex:  actual_amount && fees ==> amount
const calculateReverseAmount = (
  toCurrency: UserApiResponse['currencies'][0],
  amount: number,
  exchangeRate: number,
  reverseExchangeRate: number,
) => {
  let commission = 0;
  // commission factories for displaying
  const commissionFacts = returnCommissionFacts(toCurrency);

  let totalAmountBeforeCommission = (amount * (100 + (toCurrency.exchangeFeesPercent ?? 0))) / 100
    + (toCurrency.exchangeFeesFixed ?? 0);
  commission = totalAmountBeforeCommission - amount;
  if (toCurrency.exchangeFeesMin && commission < toCurrency.exchangeFeesMin) {
    totalAmountBeforeCommission = amount + toCurrency.exchangeFeesMin;
  } else if (
    toCurrency.exchangeFeesMax
    && commission > toCurrency.exchangeFeesMax
  ) {
    totalAmountBeforeCommission = amount + toCurrency.exchangeFeesMax;
  }
  const actualAmount = totalAmountBeforeCommission / exchangeRate;

  return {
    calculatedValue: actualAmount > 0 ? actualAmount : 0,
    exchangeRate,
    commission,
    commissionFacts: commissionFacts.trim(),
    reverseExchangeRate,
  };
};

//  function to calculate fees in exchange transaction
//  there are to ways to calculate
//  first for the main way to exchange amount - fees = actual-amount
//  second for reverse way to exchange actual_amount && fees ==> amount

export function FeesExchangeCalculate(
  fromCurrencyCode: string | undefined,
  toCurrency: UserApiResponse['currencies'][0] | undefined,
  ratesRecords: RatesApiResponse,
  amount: number | undefined | '',
  isReverse: boolean,
) {
  const { exchangeRate, reverseExchangeRate } = calculateExchange(
    fromCurrencyCode,
    toCurrency,
    ratesRecords,
  );
  let commission = 0;
  let commissionFacts = '';
  if (fromCurrencyCode && toCurrency && amount) {
    // calculate the actual value revers the main way ************
    if (isReverse) {
      return calculateReverseAmount(
        toCurrency,
        amount,
        exchangeRate,
        reverseExchangeRate,
      );
    }
    // ************************************************
    const newAmount = amount * exchangeRate;
    // commission factories for displaying
    commissionFacts = returnCommissionFacts(toCurrency);
    // commission value for calculating
    commission = newAmount * ((toCurrency.exchangeFeesPercent ?? 0) / 100)
      + (toCurrency.exchangeFeesFixed ?? 0);
    if (toCurrency.exchangeFeesMin && commission < toCurrency.exchangeFeesMin) {
      commission = toCurrency.exchangeFeesMin;
    } else if (
      toCurrency.exchangeFeesMax
      && commission > toCurrency.exchangeFeesMax
    ) {
      commission = toCurrency.exchangeFeesMax;
    }
    return {
      calculatedValue: newAmount - commission > 0 ? newAmount - commission : 0,
      exchangeRate,
      commission,
      commissionFacts: commissionFacts.trim(),
      reverseExchangeRate,
    };
  }
  return {
    calculatedValue: undefined,
    exchangeRate,
    commission,
    commissionFacts: commissionFacts.trim(),
    reverseExchangeRate,
  };
}
