import { GlobalCard } from '@/components/common/global-card';
import { Text } from '@mantine/core';
import React from 'react';

interface Props{
    title: string;
    description: string;
}
export default function StepDescriptionCard({ description, title }:Props) {
  return (
    <GlobalCard props={{ ta: 'center' }} onClick={() => { }}>
      <Text tt="capitalize" color="dimmed" size="lg" weight={500}>
        {title}
      </Text>
      <Text>
        {description}
      </Text>
    </GlobalCard>
  );
}
