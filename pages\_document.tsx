import { ltrCache, rtlCache } from '@/lib';
import { createStylesServer, ServerStyles } from '@mantine/next';
import Document, {
  DocumentContext,
  Head,
  Html,
  Main,
  NextScript,
} from 'next/document';

const rtlStyleServer = createStylesServer(rtlCache);
const ltrStyleServer = createStylesServer(ltrCache);

export default class _Document extends Document {
  static async getInitialProps(ctx: DocumentContext) {
    const initialProps = await Document.getInitialProps(ctx);

    return {
      ...initialProps,
      styles: [
        initialProps.styles,
        <ServerStyles
          html={initialProps.html}
          server={ctx.locale === 'en' ? ltrStyleServer : rtlStyleServer}
          key="styles"
        />,
      ],
    };
  }

  render() {
    return (
      <Html>
        <Head>
          <meta name="description" content="Kazawallet" />
          <meta name="ccpayment-site-verification" content="0b823d8b45ee3eaf26e538b6cff6f451" />
          <link rel="icon" href="/favicon.ico" />
          <link rel="manifest" href="/manifest.json" />
          <link href="https://fonts.cdnfonts.com/css/bauhausc" rel="stylesheet" />
        </Head>
        <body>
          <Main />
          <NextScript />
        </body>
      </Html>
    );
  }
}
