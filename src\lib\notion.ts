import { Client } from '@notionhq/client';

const notionToken = '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';

const notion = new Client({
  auth: notionToken,
});

export const notionQuery = ({ database }: { database: string }) => notion.databases.query({
  database_id: database,
});
export const notionMutation = ({
  database,
  body,
}: {
  database: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  body: any;
}) => notion.pages.create({
  parent: { database_id: database },
  properties: body,
});
