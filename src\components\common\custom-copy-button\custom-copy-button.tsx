import { <PERSON>Icon, CopyButton, Tooltip } from '@mantine/core';
import React from 'react';

import useTranslation from 'next-translate/useTranslation';
import { Icon } from '../icon';

function CustomCopyButton({ value }: { value: string }) {
  const { t } = useTranslation();
  return (
    <CopyButton value={value} timeout={2000}>
      {({ copied, copy }) => (
        <Tooltip
          label={copied ? t('common:copied') : t('common:copy')}
          withArrow
          position="right"
        >
          <ActionIcon color={copied ? 'teal' : 'gray'} onClick={copy} aria-label="copy btn">
            {copied ? (
              <Icon icon="check" size="1rem" color="dark" />
            ) : (
              <Icon icon="copy" size="1rem" color="dark" />
            )}
          </ActionIcon>
        </Tooltip>
      )}
    </CopyButton>
  );
}

export default CustomCopyButton;
