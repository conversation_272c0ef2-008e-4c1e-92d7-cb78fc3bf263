/**
 * Authentication logic
 * using keyckloak provider
 * @description
 * There are three cases:
 *  -if did not have session: when user click "login or register" will navigate to form on fill and complete it
 *   will save access and refresh tokens in session and navigate to landing with session to call all apis.
 * -if user already logged in will return current toke.
 *
//  *  -if user already logged in will check if the refresh token not expired then the user complete works with sane session.
//  *  -if user already logged in but refresh token expired then will call logout function, navigate to login form page and delete the session.
 * When user click logout button will call logout function, delete the session and navigate to landing page.
 * When happened any error on keyckloak will navigate to this route "api/auth/signin"
//  * Every 2 minutes will recall the session and call  "refreshAccessToken" function .
//  * "refreshAccessToken" function refreshes access token and checks if refresh token expired when call it.
 */

// @ts-nocheck
/* eslint no-param-reassign: "error" */
import NextAuth from 'next-auth';
import KeycloakProvider from 'next-auth/providers/keycloak';
import axios from 'axios';
import { TOKEN_SECRET } from '@/data';
// api config
const authLink = process.env.AUTH_LINK;
const clientId = process.env.CLIENT_ID;
const clientSecret = process.env.CLIENT_SECRET;
// logout request function
// When call logout function the session will be delete from keyckloak provider.
const logout = async (token) => {
  const params = {
    client_id: clientId,
    client_secret: clientSecret,
    refresh_token: token.refreshToken,
    id_token_hint: token?.idToken,
    post_logout_redirect_uri: '',
  };
  const { accessToken } = token;
  const logoutUrl = `${authLink}/realms/kasroad/protocol/openid-connect/logout`;
  const config = {
    headers: {
      authorization: `Bearer ${accessToken}`,
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  };
  try {
    await axios.post(logoutUrl, params, config);
  } catch (e) {
    // console.log("logout error", e);
  }
};

// /**
//  * This function to refresh access token and return session.
//  * If api failed call logout function.
//  */
// // eslint-disable-next-line consistent-return
// async function refreshAccessToken(token): Promise<string> {
//   console.log("refresh token called");
//   console.log("refresh token called", token);

//   const url = `${authLink}/realms/kasroad/protocol/openid-connect/token`;
//   const params = {
//     client_id: clientId,
//     client_secret: clientSecret,
//     grant_type: "refresh_token",
//     refresh_token: token?.refreshToken,
//   };
//   try {
//     const response = await axios.post(url, params, {
//       headers: {
//         "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",
//       },
//     });
//     // if api call success
//     if (response.status === 200) {
//       const refreshedToken = response.data;
//       return {
//         ...token,
//         accessToken: refreshedToken?.access_token,
//         refreshToken: refreshedToken?.refresh_token,
//         idToken: refreshedToken?.id_token,
//       };
//     }
//   } catch (e) {
//     /**
//      * if api call failed call logout function
//      */
//     logout(token);
//     return {};
//   }
// }

export const authOptions = {
  // Configure one or more authentication providers
  // Configure keycloak providers
  secret: clientSecret,
  providers: [
    KeycloakProvider({
      clientId,
      clientSecret,
      issuer: `${authLink}/realms/kasroad`,
      profile: (profile) => ({
        ...profile,
        id: profile.sub,
      }),
    }),
  ],
  session: {
    jwt: true,
    maxAge: 30 * 24 * 60 * 60, // 30 Days
  },
  jwt: {
    secret: TOKEN_SECRET,
    encryption: true,
    signingKey:
      '{"kty":"oct","kid":"Avyk2ib7GV1zoCXfnCdgHGWTRNT_t6VaXteICSNoAkw","alg":"HS512","k":"J1PUUch-TsGspq1bFMhd9Q-thFRDWwkhQy8nVw0DhEo"}',
    encryptionKey:
      '{"kty":"oct","kid":"dVO0fAPQDxaZ9S2SnzHEa9-0spte9Y8apTJVqoZG__A","alg":"A256GCM","k":"k576i864CnpP7UkPjkPSV2_YHYe9gApsGX1kF2Ysxls"}',
  },
  callbacks: {
    async jwt({ token, user, account }) {
      if (account && user) {
        return {
          ...token,
          accessToken: account?.access_token,
          refreshToken: account?.refresh_token,
          user,
        };
      }
      return token;
    },
    // when user signin and have a session before.
    async session({ session }) {
      return session;
    },
  },
  events: {
    // Customization logout trigger
    // If there is a token call logout function
    async signOut({ token }) {
      if (token) {
        logout(token);
      }
    },
  },
  pages: {
    error: '/api/auth/signin',
  },
};
export default NextAuth(authOptions);
