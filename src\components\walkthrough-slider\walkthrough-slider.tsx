import {
  MantineTheme,
  Modal,
  Tabs,
  useMantineTheme,
} from '@mantine/core';
import { useDisclosure, useLocalStorage, useMediaQuery } from '@mantine/hooks';
import React, { useEffect, useState } from 'react';
import useTranslation from 'next-translate/useTranslation';
import { Icon } from '../common/icon';
import { operationIcon } from '@/utils/operations-utils/operation-icons';
import TabOperationContent from './tab-operation-content';
import { desktopImages, mobileImages } from './images-list';
// common style for all tabs
const tabsStyle = (theme: MantineTheme) => ({
  [theme.fn.smallerThan('sm')]: {
    '& .mantine-ltr-Tabs-tabLabel ,& .mantine-rtl-Tabs-tabLabel': {
      display: 'none',
    },
    '& .mantine-ltr-Tabs-tabIcon ,& .mantine-rtl-Tabs-tabIcon': {
      margin: 'auto',

    },
    '& .tabler-icon': {
      height: 25,
      width: 25,
      color: 'gray',
    },
    '& .mantine-ltr-UnstyledButton-root ,& .mantine-rtl-UnstyledButton-root': {
      padding: 10,
    },
  },
});
interface Props {
  isAuth: boolean;
}
function WalkthroughSlider({ isAuth }: Props) {
  const [value] = useLocalStorage<string>({
    key: 'walkthrough-opened',
    defaultValue: 'false',
    getInitialValueInEffect: false,
  });
  const [opened, { open, close }] = useDisclosure(false);

  const { t } = useTranslation();
  const theme = useMantineTheme();

  const matchesMaxXs = useMediaQuery(
    `(max-width: ${theme.breakpoints.xs})`,
    true,
    {
      getInitialValueInEffect: false,
    },
  );
  const matchesMaxSm = useMediaQuery(
    `(max-width: ${theme.breakpoints.sm})`,
    true,
    {
      getInitialValueInEffect: false,
    },
  );
  const matchesMaxMd = useMediaQuery(
    `(max-width: ${theme.breakpoints.md})`,
    true,
    {
      getInitialValueInEffect: false,
    },
  );
  const matchesMaxLg = useMediaQuery(
    `(max-width: ${theme.breakpoints.lg})`,
    true,
    {
      getInitialValueInEffect: false,
    },
  );
  const matchesMaxXl = useMediaQuery(
    `(max-width: ${theme.breakpoints.xl})`,
    true,
    {
      getInitialValueInEffect: false,
    },
  );

  const returnModalWidth = () => {
    const modalWidth = 850;
    const imageWidth = 820;
    if (matchesMaxXs) {
      return {
        modalWidth: 360,
        imageWidth: 340,
      };
    }
    if (matchesMaxSm) {
      return {
        modalWidth: 510,
        imageWidth: 480,
      };
    }
    if (matchesMaxMd) {
      return {
        modalWidth: 610,
        imageWidth: 580,
      };
    }
    if (matchesMaxLg) {
      return {
        modalWidth: 630,
        imageWidth: 600,
      };
    }
    if (matchesMaxXl) {
      return {
        modalWidth: 635,
        imageWidth: 605,
      };
    }
    return {
      modalWidth,
      imageWidth,
    };
  };

  const [operationType, setOperationType] = useState<string | null>('deposit');

  useEffect(() => {
    if (isAuth && value === 'false') {
      open();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuth, value]);
  return (
    <Modal
      size={returnModalWidth()?.modalWidth}
      fullScreen={matchesMaxXs}
      radius="lg"
      centered
      opened={opened}
      onClose={() => {}}
      withCloseButton={false}
      sx={{
        '.mantine-ltr-Modal-body , .mantine-rtl-Modal-body': {
          height: '100%',
        },
      }}
    >
      <Tabs
        defaultValue={operationType}
        value={operationType}
        onTabChange={setOperationType}
        sx={tabsStyle}
      >
        <Tabs.List>
          <Tabs.Tab
            value="deposit"
            icon={(
              <Icon
                icon={operationIcon({ operationType: 'deposit' })}
                size="0.8rem"
                color=""
              />
            )}
          >
            {t('common:deposit')}
          </Tabs.Tab>
          <Tabs.Tab
            value="exchange"
            icon={(
              <Icon
                icon={operationIcon({ operationType: 'exchange' })}
                size="0.8rem"
                color=""
              />
            )}
          >
            {t('common:exchange')}
          </Tabs.Tab>
          <Tabs.Tab
            value="massPayout"
            icon={(
              <Icon
                icon={operationIcon({ operationType: 'massPayout' })}
                size="0.8rem"
                color=""
              />
            )}
          >
            {t('common:massPayout')}
          </Tabs.Tab>
          <Tabs.Tab
            value="transfer"
            icon={(
              <Icon
                icon={operationIcon({ operationType: 'transfer' })}
                size="0.8rem"
                color=""
              />
            )}
          >
            {t('common:transfer')}
          </Tabs.Tab>
          <Tabs.Tab
            value="withdraw"
            icon={(
              <Icon
                icon={operationIcon({ operationType: 'withdraw' })}
                size="0.8rem"
                color=""
              />
            )}
          >
            {t('common:withdraw')}
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="deposit" pt="xs">
          <TabOperationContent
            setOperationType={setOperationType}
            operation="deposit"
            close={close}
            mobileImages={mobileImages.deposit}
            desktopImages={desktopImages.deposit}
            imageWidth={returnModalWidth()?.imageWidth}
          />
        </Tabs.Panel>

        <Tabs.Panel value="exchange" pt="xs">
          <TabOperationContent
            setOperationType={setOperationType}
            operation="exchange"
            close={close}
            mobileImages={mobileImages.exchange}
            desktopImages={desktopImages.exchange}
            imageWidth={returnModalWidth()?.imageWidth}
          />
        </Tabs.Panel>

        <Tabs.Panel value="massPayout" pt="xs">
          <TabOperationContent
            setOperationType={setOperationType}
            operation="massPayout"
            close={close}
            mobileImages={mobileImages.massPayout}
            desktopImages={desktopImages.massPayout}
            imageWidth={returnModalWidth()?.imageWidth}
          />
        </Tabs.Panel>
        <Tabs.Panel value="transfer" pt="xs">
          <TabOperationContent
            setOperationType={setOperationType}
            operation="transfer"
            close={close}
            mobileImages={mobileImages.transfer}
            desktopImages={desktopImages.transfer}
            imageWidth={returnModalWidth()?.imageWidth}
          />
        </Tabs.Panel>
        <Tabs.Panel value="withdraw" pt="xs">
          <TabOperationContent
            setOperationType={setOperationType}
            operation="withdraw"
            close={close}
            mobileImages={mobileImages.withdraw}
            desktopImages={desktopImages.withdraw}
            imageWidth={returnModalWidth()?.imageWidth}
          />
        </Tabs.Panel>
      </Tabs>
    </Modal>
  );
}

export default WalkthroughSlider;
