import { Icon } from '@/components/common/icon';
import {
  Center,
  Container,
  SimpleGrid,
  Stack,
  Text,
  useMantineTheme,
} from '@mantine/core';

interface FeaturesProps {
  benefits: {
    icon: string;
    title: string;
    description: string;
  }[];
}
export default function Features({ benefits }: FeaturesProps) {
  const theme = useMantineTheme();
  return (
    <div style={{ paddingTop: 40 }}>
      <Container
        pb={40}
        px={{
          xs: 20,
          sm: 30,
          lg: 40,
          base: 20,
        }}
        size={1200}
      >
        <SimpleGrid
          cols={4}
          spacing="md"
          breakpoints={[
            { maxWidth: 'md', cols: 2, spacing: 'sm' },
            { maxWidth: 'xs', cols: 1, spacing: 'sm' },
          ]}
        >
          {benefits?.map((benefit) => (
            <Stack align="center" key={benefit.title}>
              <div>
                <Center>
                  <Icon
                    icon={benefit.icon}
                    size={50}
                    color={theme.colors.teal[6]}
                  />
                </Center>
                <Text ta="center" color="white" weight="bold" size="xl">
                  {benefit.title}
                </Text>
              </div>
              <Text maw={200} ta="center" color="dimmed" size="lg" weight={500}>
                {benefit.description}
              </Text>
            </Stack>
          ))}
        </SimpleGrid>
      </Container>
    </div>
  );
}
