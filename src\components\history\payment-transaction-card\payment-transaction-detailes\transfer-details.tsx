import { CustomCopyButton } from '@/components/common/custom-copy-button';
import { Item } from '@/components/pop-up-success/pop-up-item';

import { PaymentsApiResponse } from '@/store/payment';
import { TransfersApiResponse } from '@/store/transfer/types';
import { Text, Image, Group } from '@mantine/core';
import currency from 'currency.js';
import React from 'react';

interface Props {
  amount: number;
  actualAmount: PaymentsApiResponse['data'][0]['actualAmount'];
  userFrom:
    | PaymentsApiResponse['data'][0]['user']
    | TransfersApiResponse['data'][0]['userFrom']
    | undefined;
  userTo: TransfersApiResponse['data'][0]['userTo'] | undefined;
  currencyFrom:
    | PaymentsApiResponse['data'][0]['currency']
    | TransfersApiResponse['data'][0]['currencyFrom']
    | undefined;

  operationType:
    | 'deposit'
    | 'transfer'
    | 'withdraw'
    | 'exchange'
    | 'received'
    | string;
  fees: number | undefined;
  gif: null | string;
}
function TransferDetails({
  currencyFrom,
  operationType,
  amount,
  fees,
  actualAmount,
  userFrom,
  userTo,
  gif,
}: Props) {
  return (
    <>
      {operationType !== 'received' && (
        <Item
          align="center"
          name="sentAmount"
          content={(
            <div
              style={{
                display: 'flex',
                gap: 6,
                color: 'red',
              }}
            >
              <Text weight={500} span>
                {currency(amount, {
                  symbol: '',
                  precision: currencyFrom?.precision ?? 2,
                }).format()}
              </Text>
              <Text weight={500} mr={4} span>
                {currencyFrom?.symbol}
              </Text>
            </div>
          )}
        />
      )}
      {operationType !== 'received' && (
        <Item
          align="center"
          name="fees"
          content={(
            <div
              style={{
                display: 'flex',
                gap: 6,
              }}
            >
              <Text>
                {currency(`${fees}`, {
                  symbol: '',
                  precision: currencyFrom?.precision ?? 2,
                }).format()}
              </Text>
              <Text weight={500} mr={4} span>
                {currencyFrom?.symbol}
              </Text>
            </div>
          )}
        />
      )}
      <Item
        align="center"
        name={operationType !== 'received' ? 'toBeReceived' : 'amount'}
        content={(
          <div
            style={{
              display: 'flex',
              gap: 6,
              color: operationType === 'received' ? 'green' : 'red',
            }}
          >
            <Text weight={500} span>
              {currency(`${actualAmount}`, {
                symbol: '',
                precision: currencyFrom?.precision ?? 2,
              }).format()}
            </Text>
            <Text weight={500} mr={4} span>
              {currencyFrom?.symbol}
            </Text>
          </div>
        )}
      />
      <Item
        align="center"
        name="currency"
        content={<Text>{currencyFrom?.code}</Text>}
      />
      {operationType !== 'received' && (
        <>
          <Group>
            <Item
              align="center"
              name="receiver"
              content={<Text>{userTo?.accountId}</Text>}
            />
            <CustomCopyButton value={userTo?.accountId ?? ''} />
          </Group>
          <Item
            align="center"
            name="receiverEmail"
            content={<Text>{userTo?.email}</Text>}
          />
        </>
      )}

      {operationType === 'received' && (
        <Group>
          <Item
            align="center"
            name="sender"
            content={<Text>{userFrom?.accountId}</Text>}
          />
          <CustomCopyButton value={userFrom?.accountId ?? ''} />
        </Group>
      )}

      {gif && (
        <Group spacing={5} align="start">
          <Text weight={500} color="dimmed">
            Gif
          </Text>
          <Image src={gif} width={200} mx="auto" alt="gif" />
        </Group>
      )}
    </>
  );
}

export default TransferDetails;
