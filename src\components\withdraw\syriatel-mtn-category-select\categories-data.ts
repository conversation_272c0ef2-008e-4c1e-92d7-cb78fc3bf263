const prepaidMtnPackages = [
  '10000',
  '11000',
  '15000',
  '20000',
  '30000',
  '32000',
  '36000',
  '40000',
  '70000',
  '75000',
];

const cashMtnPackages = ['5000', '10000', '15000', '20000', '25000', '30000', '40000', '50000', '60000', '70000', '75000', '78000', '80000', '90000', '95000', '100000', '200000', '250000'];

const postpaidMtnPackages = ['1000', '10000', '15000', '20000', '25000', '30000', '40000', '50000', '70000', '80000'];

const prepaidSyriatelPackages = ['2588', '4026', '4506', '4793', '5273', '6232', '6807', '7190', '7766', '8149', '8628', '9587', '10067', '10546', '11505', '12464', '13039', '14381', '16011',
  '16299', '17257', '18312', '19175', '21093', '23969', '28763', '31639', '36912', '47938', '57526', '62320', '71907', '76701', '94918'];

const cashSyriatelPackages = ['5000', '10000', '15000', '20000', '25000', '30000', '40000', '50000', '60000', '100000', '150000', '200000', '250000'];

const postpaidSyriatelPackages = ['5000', '10000', '15000', '20000', '25000', '30000', '35000', '40000', '45000', '50000'];

export function getOptionsByTag(tag: string) {
  switch (tag) {
    case 'syriatel_credit':
    case 'syriatel_two_credit':
      return prepaidSyriatelPackages;
    case 'syriatel_cash':
    case 'syriatel_two_cash':
      return cashSyriatelPackages;
    case 'mtn_credit':
    case 'mtn_two_credit':
      return prepaidMtnPackages;
    case 'mtn_cash':
    case 'mtn_two_cash':
      return cashMtnPackages;
    case 'mtn_postpaid':
    case 'mtn_two_postpaid':
      return postpaidMtnPackages;
    case 'syriatel_postpaid':
    case 'syriatel_two_postpaid':
      return postpaidSyriatelPackages;
    default:
      return [];
  }
}
