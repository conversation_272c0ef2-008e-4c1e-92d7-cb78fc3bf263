/**
 * There are two handlers: get user gifts data ,redeem gift card and create gift card.
 */

import { apiEndpoints, apiMethods, httpCode } from '@/data';
import { BackendClient } from '@/lib';
import { customGiftApiResponseSchema } from '@/store/gift-card';

import createApiError from '@/utils/api-utils/create-api-error';
import { getJwt } from '@/utils/api-utils/jwt';
import { captchaValidator } from '@/utils/captcha-validator';

import { NextApiRequest, NextApiResponse } from 'next';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token } = await getJwt(req);
  if (req.method === apiMethods.POST) {
    try {
      const chaKey = req?.query?.chaKey;

      await captchaValidator({
        token: chaKey as string,
        secret: process.env.RECAPTCHA_SECRET_KEY as string,
      });
      const { data } = await BackendClient(req).get(apiEndpoints.giftCard, {
        headers: {
          authorization: token,
        },
        params: {
          code: req.body?.code,
        },
      });

      return res
        .status(httpCode.SUCCESS)
        .json(customGiftApiResponseSchema(data));
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }
  const error = createApiError({ error: '' });
  return res.status(httpCode.INTERNAL_SERVER_ERROR).json(error);
}

export default handler;
