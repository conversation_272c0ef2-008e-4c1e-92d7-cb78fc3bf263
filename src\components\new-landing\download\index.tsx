import {
  Button,
  Container,
  Group,
  Image,
  Paper,
  SimpleGrid,
  Stack,
  Text,
  useMantineTheme,
} from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';
import styles from './styles.module.scss';
import Link from 'next/link';
import {
  ROUTES, apkDownloadLink, assetBaseUrl, googlePlayAppLink,
} from '@/data';
import { useMediaQuery } from '@mantine/hooks';
import { signIn } from 'next-auth/react';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';

export default function Download(props: { isAuth: boolean }) {
  const { isAuth } = props;
  const theme = useMantineTheme();
  const { t } = useTranslation();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  useEffect(
    () => () => {
      setLoading(false);
    },
    [],
  );
  const matches = useMediaQuery('(max-width: 65em)', true, {
    getInitialValueInEffect: false,
  });
  const matchesMd = useMediaQuery('(max-width: 56em)', true, {
    getInitialValueInEffect: false,
  });
  const matchesXs = useMediaQuery('(max-width: 38em)', true, {
    getInitialValueInEffect: false,
  });
  const imageSize = () => {
    let width = 500;
    if (matchesXs) {
      width = 300;
    } else if (matchesMd) {
      width = 350;
    } else if (matches) {
      width = 450;
    }
    return width;
  };
  return (
    <Container
      py={40}
      px={{
        xs: 20,
        sm: 30,
        lg: 40,
        base: 20,
      }}
      size={1200}
    >
      <Paper bg={theme.colors.primary[9]} radius="md" px={10}>
        <SimpleGrid
          cols={2}
          breakpoints={[
            { maxWidth: 'md', cols: 2, spacing: 'md' },
            { maxWidth: 'sm', cols: 1, spacing: 'sm' },
            { maxWidth: 'xs', cols: 1, spacing: 'sm' },
          ]}
        >
          <Stack p={50} justify="center">
            <Text
              ff={router.locale === 'ar' ? theme.fontFamily : `BauhausC, ${theme.fontFamily}`}
              size={43}
              weight="bold"
              color="white"
            >
              <Text
                span
                variant="gradient"
                gradient={{ from: 'gray', to: 'green' }}
              >
                {t('common:start')}
              </Text>
              {' '}
              {t('common:downloadLandingTitle')}
            </Text>
            <Text color="dimmed" size="xl" weight={500} mb="xs">
              {t('common:downloadLandingDescriptionFirst')}
            </Text>
            <Text color="dimmed" size="xl" weight={500} mb="xs">
              {t('common:downloadLandingDescriptionSecond')}
            </Text>
            <Text color="dimmed" size="xl" weight={500} mb="xs">
              {t('common:downloadLandingDescriptionThird')}
            </Text>
            {isAuth ? (
              <Link
                href={ROUTES.wallets.path}
                style={{
                  textDecoration: 'none',
                  color: theme.colors.primary[7],
                }}
                className={styles.aHero}
              >
                {t('common:gotoWallets')}
              </Link>
            ) : (
              <Button
                loading={loading}
                onClick={() => {
                  setLoading(true);
                  signIn('keycloak', {
                    callbackUrl: ROUTES.wallets.path,
                  });
                }}
                style={{
                  color: theme.colors.primary[7],
                }}
                className={styles.aHero}
              >
                {t('common:joinUsToday')}
              </Button>
            )}
            <div>
              <Text color="dimmed" size="sm" weight={500} my="lg">
                {t('common:heroLandingComingSoon')}
              </Text>
              <Group>
                <Link href={googlePlayAppLink} target="_blank">
                  <Image width={136} src={`${assetBaseUrl}/assets/svg/googleplay.svg`} />
                </Link>
                <Link href={apkDownloadLink} target="_blank">
                  <Image width={136} src={`${assetBaseUrl}/assets/new-landing/apk.png`} />
                </Link>
              </Group>
            </div>
          </Stack>
          <Stack align="center" justify="center">
            <Image
              width={imageSize()}
              src={`${assetBaseUrl}/assets/new-landing/download-landing.webp`}
              alt="phone"
            />
          </Stack>
        </SimpleGrid>
      </Paper>
    </Container>
  );
}
