import {
  Box, Group, Skeleton, Stack,
} from '@mantine/core';
import React from 'react';

function NotificationItemSkeleton() {
  return (
    <Stack
      w="100%"
      py="md"
      sx={{
        borderBlockEnd: '0.5px solid ',
        borderBlockEndColor: 'rgba(0,0,0,0.05)',
        paddingInlineStart: '8px',
        minHeight: 60,
      }}
    >
      <Group spacing="xs" pr="8px">
        <Box>
          <Skeleton height={20} circle />
        </Box>
        <Skeleton w={150} mih={20} />
      </Group>

      <Skeleton miw={280} mih={25} />
    </Stack>
  );
}

export default NotificationItemSkeleton;
