import { LocaleCookie, httpCode } from '@/data';
import { ROUTES, authPages, iframePages } from '@/data/routes';
import { BlockedErrorKeys, LimitsErrorKeys } from '@/types/error.type';
import { notifications } from '@mantine/notifications';
import { AxiosError } from 'axios';
import { getCookie, setCookie } from 'cookies-next';
import { signIn, signOut } from 'next-auth/react';
import getT from 'next-translate/getT';

const handleAccountBlockedError = (errorKey: string) => {
  const lang = getCookie(LocaleCookie);
  setCookie('ACCOUNT_BLOCK', errorKey);
  if (
    typeof window !== 'undefined'
    && !window.location.pathname.includes(ROUTES.accountBlockedChangIp.path)
    && !window.location.pathname.includes(ROUTES.accountBlocked.path)
  ) {
    window.location.replace(
      errorKey === BlockedErrorKeys.USER_BLOCKED_CHANGE_IP
        ? `/${lang}/${ROUTES.accountBlockedChangIp.path}`
        : `/${lang}/${ROUTES.accountBlocked.path}`,
    );
  }
};

const handleUnauthorizedCondition = () => {
  if (
    typeof window !== 'undefined'
    && authPages.includes(window.location.pathname)
  ) {
    signIn('keycloak');
  }
  if (
    typeof window !== 'undefined'
    && iframePages.includes(window.location.pathname)
  ) {
    window.location.replace(
      `${ROUTES.iframeLogin.path}?callbackUrl=${window.location.pathname}${window.location.search}`,
    );
  }
};

const errorCache: { [key: string]: number } = {};
export const handleApiError = async <T extends object>(
  error: AxiosError<{
    message: { fallback: string; key: string; params: T };
    code: number;
  }>,
  isQueryRequest?: boolean,
  withoutNotification?: boolean,
) => {
  const lang = getCookie(LocaleCookie);
  const t = await getT(lang as string, 'errors');
  // this is not a pure function and it is ok since it will be used only in the context of this app
  if (error.response?.status === httpCode.UNAUTHORIZED) {
    // if authorized then logout
    signOut({ redirect: false });
    handleUnauthorizedCondition();
  } else if (
    error.response?.data?.message?.key === BlockedErrorKeys.USER_BLOCKED
    || error.response?.data?.message?.key
      === BlockedErrorKeys.USER_BLOCKED_CHANGE_IP
  ) {
    handleAccountBlockedError(error.response?.data?.message?.key);
  } else if (error.response && !withoutNotification) {
    const {
      response: {
        data: { message },
      },
    } = error;
    const keyTranslate = t(message?.key, { ...message?.params });
    const errorMessage = keyTranslate === message?.key ? message.fallback : keyTranslate;
    const canShowNotification = Date.now() - (errorCache[message?.key ?? ''] ?? 0) > 30 * 1000;
    if (message.key === LimitsErrorKeys.ERROR_PASS_THE_LIMIT) {
      // don call notification here because we have popup to handle limit errors
    } else if (
      typeof window !== 'undefined'
      && canShowNotification
      && isQueryRequest
    ) {
      errorCache[message?.key || ''] = Date.now();
      notifications.show({
        message: errorMessage,
        color: 'red',
      });
    } else if (!isQueryRequest) {
      notifications.show({
        message: errorMessage,
        color: 'red',
      });
    }
  }
};
