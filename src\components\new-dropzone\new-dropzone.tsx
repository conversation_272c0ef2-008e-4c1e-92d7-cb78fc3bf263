/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react/require-default-props */
import {
  Box,
  Card,
  Image,
  Group,
  Input,
  Text,
  useMantineTheme,
  Stack,
} from '@mantine/core';
import { Dropzone, DropzoneProps } from '@mantine/dropzone';
import { IconUpload, IconX } from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';
import React, { useState } from 'react';
import DropzoneFile from './dropzone-file';
import { assetBaseUrl, UploadFileSize } from '@/data';

export type FileType = { id: number; name: string; url: string };

interface NewDropzoneProps {
  title: string;
  description: string[];
  error: string | undefined;
  dropzoneProps: Omit<DropzoneProps, 'children' | 'onDrop'>;
  onDelete: (deletedFile: FileType) => void;
  uploadFile?: Function;
  loading?: boolean;
  files?: FileType[];
  deleteFile?: (id: number) => void;
  iconSrc?: string;
  iconSize?: number;
}

function NewDropzone(props: NewDropzoneProps) {
  const {
    title,
    dropzoneProps,
    description,
    error,
    onDelete,
    uploadFile = () => {},
    loading = false,
    files = [],
    deleteFile = () => {},
    iconSrc = `${assetBaseUrl}/assets/icons/img.png`,
    iconSize = 40, // Default size
  } = props;
  const theme = useMantineTheme();
  const [rejectFileMessage, setRejectFileMessage] = useState('');
  const { t } = useTranslation();

  const selectedFileMessage = files.length === 1
    ? `${files.length} ${t('common:fileSelected')}`
    : `${files.length} ${t('common:filesSelected')}`;

  const dropzoneMessage = files.length === 0
    ? title || t('common:dropYourFilesHere')
    : selectedFileMessage;

  function handleDeleteFile(value: number) {
    const deletedFile = files.find((v) => v.id === value);
    if (onDelete && deletedFile) onDelete(deletedFile);
    deleteFile(value);
  }

  //* **************************************************** */
  const renderMediaTitles = (filesParam: FileType[]) => filesParam.map((v) => (
    <DropzoneFile
      key={v.id}
      deleteFile={(fileId) => handleDeleteFile(fileId)}
      file={v}
    />
  ));

  //* ********************************************** */

  const handleSelectFile = (data: any) => {
    setRejectFileMessage('');
    uploadFile(data);
  };
  const renderDescriptions = (desc?: string[]) => {
    if (!desc) return [];
    return desc.map((v) => (
      <div key={v}>
        <Text size="xs" inline weight={500} color="dimmed" mih={20}>
          {t('common:imageDesc', { value: UploadFileSize })}
        </Text>
        <Text
          ta="start"
          mih={14}
          my={5}
          size="xs"
          lineClamp={1}
          weight={500}
          color="dimmed"
          inline
          maw={{
            sm: 450,
            md: 450,
            lg: 450,
            xs: 300,
            base: 160,
          }}
        >
          {v}
        </Text>
      </div>
    ));
  };
  return (
    <Box>
      <Card radius="lg" p={0} sx={{ borderBottom: 0 }}>
        <Dropzone
          radius="lg"
          maxSize={1024 * UploadFileSize * 1024}
          loading={loading}
          accept={{ 'image/*': [] }}
          sx={{
            zIndex: 1,
            borderColor:
              theme.colorScheme === 'light'
                ? theme.colors.gray[2]
                : theme.colors.gray[8],
            borderBottomLeftRadius: 10,
            borderBottomRightRadius: 10,
          }}
          p={0}
          {...dropzoneProps}
          onDrop={handleSelectFile}
          mih={50}
          onReject={(errors) => {
            setRejectFileMessage(
              t(`errors:${errors[0].errors[0].code}`) as string,
            );
          }}
        >
          <Group p="xs" spacing="xs" align="center">
            <Dropzone.Accept>
              <IconUpload
                size={50}
                stroke={1.5}
                color={
                  theme.colors[theme.primaryColor][
                    theme.colorScheme === 'dark' ? 4 : 6
                  ]
                }
              />
            </Dropzone.Accept>
            <Dropzone.Reject>
              <IconX
                size={50}
                stroke={1.5}
                color={theme.colors.red[theme.colorScheme === 'dark' ? 4 : 6]}
              />
            </Dropzone.Reject>
            <Dropzone.Idle>
              <Image src={iconSrc} width={iconSize} alt="file-icon" />
            </Dropzone.Idle>

            <div style={{ textAlign: 'left' }}>
              <Text
                size={14}
                weight={500}
                inline
                align="left"
                tt="capitalize"
              >
                {dropzoneMessage}
              </Text>
              <Stack spacing={0} mt="xs">
                {renderDescriptions(description)}
              </Stack>
            </div>
          </Group>
        </Dropzone>
        {loading && <Text p="xs">{t('common:uploading')}</Text>}
        {(!!error || !!rejectFileMessage) && (
          <Input.Error p="xs">{error || rejectFileMessage}</Input.Error>
        )}
      </Card>
      {files.length > 0 && <Box mt="xs">{renderMediaTitles(files)}</Box>}
    </Box>
  );
}
export default NewDropzone;
