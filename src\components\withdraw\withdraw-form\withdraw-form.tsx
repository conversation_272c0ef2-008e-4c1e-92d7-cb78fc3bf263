/* eslint-disable max-lines */
import {
  Stack,
  rem,
  TextInput,
  Textarea,
  Text,
  SimpleGrid,
  Accordion,
  Button,
} from '@mantine/core';
import { useForm, zodResolver } from '@mantine/form';
import { useDisclosure } from '@mantine/hooks';

import React, { useState } from 'react';

import currency from 'currency.js';
import { UserApiResponse } from '@/store/user';
import { PaymentMethodsApiResponse } from '@/store/payment-methods';
import {
  PaymentsApiResponse,
  createPaymentMutation,
  createPaymentApiRequestSchema,
} from '@/store/payment';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { FeesTransferCalculate } from '@/utils/fees-functions/fees-transfer-withdraw-deposit';
import { RatesApiResponse } from '@/store/rate';

import { customFieldsErrorValidate } from '@/utils/validate-custom-fields-error';
import useTranslation from 'next-translate/useTranslation';
import MarkdownView from 'react-showdown';

import { getCookie } from 'cookies-next';
import { ErrorFrontendType } from '@/types';
import MinMaxAmount from './min-max';
import {
  getTranslatedTextValue,
  minMaxError,
  validateAmountStep,
} from '@/utils';
import { CodeConfirmModal } from '@/components/2fa/code-confirm-modal';

import { Icon } from '@/components/common/icon';
import { CustomFieldsForm } from '@/components/custom-fields';
import { PopUpSuccess } from '@/components/pop-up-success';

import { AmountInputDepositWithdraw } from '@/components/amount-input-deposit-withdraw';
import SubmitButton from '@/components/common/submit-button';
import { SyriatelMtnCategorySelect } from '../syriatel-mtn-category-select';
import { TransactionConfirmModal } from '@/components/common/transaction-confirm-modal';
import WithdrawDepositDetails from '@/components/history/payment-transaction-card/payment-transaction-detailes/withdraw-deposit-details';
import { RenderCustomFields } from '@/components/custom-fields/render-custom-fields';
import { Captcha } from '@/components/common/captcha';
import { useCaptcha } from '@/hooks';
import { returnDefaultCustomFieldValue } from '@/components/custom-fields/render-custom-fileds-value';
import { LimitsErrorKeys } from '@/types/error.type';

import { ErrorPopup } from '@/components/common/errorr-poup';
import Link from 'next/link';
import { playCaptcha, ROUTES } from '@/data';
import { AgentsList } from '@/components/agents/agents-list';
import { TranslatedTextValue } from '@/components/common/translated-text-value';
import { useRouter } from 'next/router';

interface Props {
  selectedPaymentMethod: PaymentMethodsApiResponse['data'][0];
  selectedCurrency: UserApiResponse['currencies'][0];
  rates: RatesApiResponse['rates'];
}
// eslint-disable-next-line sonarjs/cognitive-complexity,complexity
function WithdrawFormInner({
  selectedPaymentMethod,
  selectedCurrency,
  rates,
}: Props) {
  const { t } = useTranslation();
  const { locale } = useRouter();
  const [opened, { open, close }] = useDisclosure(false);
  const [openLimitPopup, setLimitPopup] = useState(false);

  const [openedCode, setOpenedCode] = useState(false);
  const [code, setCode] = useState('');
  const [codeError, setCodeError] = useState('');
  const [responseData, setResponseData] = useState<PaymentsApiResponse['data'][0]>();
  const [isLoading, setLoading] = useState(false);
  const queryClient = useQueryClient();
  const WITHDRAW_TRANSLATE = t('common:withdraw');
  // boolean to return true if payment method has "agent" or "internal_agent" tag to handle list agents viewing and form actions
  const isAgentPaymentMethod = selectedPaymentMethod?.tag === 'agent';
  const isInternalAgentPaymentMethod = selectedPaymentMethod?.tag === 'internal_agent';

  const { execute, reCaptchaRef, reset } = useCaptcha();

  // create payment form type"withdraw"
  const form = useForm({
    initialValues: {
      amount: '' as number | string,
      note: '',
      status: 'pending',
      currencyId: '',
      paymentMethodId: '',
      type: 'withdraw',
      // return payment method withdraw custom fields to initial "fields" in form
      fields: selectedPaymentMethod.withdrawCustomFields?.map((i) => ({
        type: i?.type,
        name: i?.name,
        nameAr: i?.nameAr,
        // this function to initial value by type of custom field
        value: returnDefaultCustomFieldValue(i?.type),
      })),
    },
    validate: zodResolver(createPaymentApiRequestSchema),
  });
  // create payment mutation
  const { mutate } = useMutation(createPaymentMutation().mutationFn, {
    onSuccess: (data) => {
      setResponseData(data);
      queryClient.invalidateQueries({ queryKey: ['user'] });
      close();
      setCodeError('');
      setOpenedCode(false);
      form.reset();
      setCode('');
      reset();
      setLoading(false);
    },
    onError(error: ErrorFrontendType) {
      setCode('');
      close();
      if (error?.response?.data?.message?.key === 'invalidCode') {
        setCodeError('invalidCode');
      } else setCodeError('');
      if (
        error?.response?.data?.message?.key
        === LimitsErrorKeys.ERROR_PASS_THE_LIMIT
      ) {
        setLimitPopup(true);
      }
      reset();
      setLoading(false);
    },
  });
  // submit function
  function submit(qrCode: string | undefined) {
    setLoading(true);
    const body = {
      body: {
        ...form.values,
        currencyId: selectedCurrency?.id ?? '',
        paymentMethodId: selectedPaymentMethod?.id,
      },
      qrCode,
    };
    if (reCaptchaRef.current) {
      execute((token) => mutate({
        ...body,
        chaKey: token,
      }));
    } else {
      mutate(body);
    }
  }

  // return fees after calculate it for withdraw operation
  const fees = FeesTransferCalculate(
    selectedPaymentMethod?.withdrawFeesFixed,
    selectedPaymentMethod?.withdrawFeesPercentage,
    selectedPaymentMethod?.withdrawFeesMin,
    selectedPaymentMethod?.withdrawFeesMax,
    +form.values.amount,
  );
  // boolean return true if the amount greater than max or less than min
  const isMinMaxError = minMaxError({
    amount: +form.values.amount + fees,
    maxAmount: selectedPaymentMethod?.withdrawAmountMax,
    minAmount: selectedPaymentMethod?.withdrawAmountMin,
    currencyAmount: selectedCurrency?.amount,
  });
  // boolean return true if amount step deferent about the payment method step
  const isAmountStepError = validateAmountStep(
    selectedPaymentMethod?.additionalFields?.step,
    +form.values.amount,
  ); // return currency precision
  const precision = selectedCurrency?.precision ?? 2;
  // payment method withdraw custom fileds
  const customFields = selectedPaymentMethod?.withdrawCustomFields;
  // determine if currency is crypto
  const isCrypto = ['crypto', 'crypto_stable'].includes(selectedCurrency?.type);
  // get the appropriate rate based on currency type
  const rateKey = isCrypto
    ? `${selectedCurrency.code}_TO`
    : `${selectedCurrency.code}_FROM`;
  // amount usd price
  const usdPrice = rates[rateKey]
    ? currency((1 / rates[rateKey]) * +form.values.amount, {
      symbol: '',
    }).format()
    : 0;
  // return received amount
  const toBeReceived = currency(+form.values.amount + fees, {
    symbol: '',
    precision,
  }).format();
  // this boolean will return true if the received amount is less than fee
  // to be received amount = amount
  const isToBeReceivedError = typeof form.values.amount === 'number' && form.values.amount - fees <= 0;
  // return selected payment method custom fields
  const returnPaymentMethodCustomFields = selectedPaymentMethod?.withdrawCustomFields?.map((i, index) => ({
    ...i,
    value: form.values?.fields ? form.values?.fields[index]?.value : '',
  }));
  // conditions to disabled and enabled submit button
  const isSubmitButtonDisabled = typeof form.values.amount !== 'number'
    || form?.values?.amount <= 0
    || form.values.amount - fees <= 0
    || isMinMaxError
    || isAmountStepError;
  // onSubmit function
  // validate custom fields errors
  // if 2FA enabled open code popup to enter code from google auth app, else open confirm popup
  const onSubmit = () => {
    customFieldsErrorValidate(
      form,
      customFields,
      // if 2FA enabled open code popup else open confirm popup
      getCookie('2fa-enabled') ? () => setOpenedCode(true) : open,
    );
  };

  const syMtnTags = [
    'syriatel_credit',
    'syriatel_cash',
    'syriatel_postpaid',
    'mtn_credit',
    'mtn_cash',
    'mtn_postpaid',
    'syriatel_two_credit',
    'syriatel_two_cash',
    'syriatel_two_postpaid',
    'mtn_two_credit',
    'mtn_two_cash',
    'mtn_two_postpaid',
  ];

  return (
    <>
      <form>
        <Stack my="md" mx="auto">
          <MinMaxAmount
            minAmount={selectedPaymentMethod?.withdrawAmountMin}
            maxAmount={selectedPaymentMethod?.withdrawAmountMax}
            symbol={selectedCurrency?.symbol}
            currencyAmount={selectedCurrency?.amount}
            currencyPrecision={selectedCurrency?.precision}
          />
          {syMtnTags.includes(selectedPaymentMethod.tag) ? (
            <SyriatelMtnCategorySelect
              company={selectedPaymentMethod?.tag}
              form={form}
            />
          ) : (
            <AmountInputDepositWithdraw
              operationType="withdraw"
              form={form}
              isMinMaxError={isMinMaxError}
              precision={precision}
              selectedPaymentMethod={selectedPaymentMethod}
            />
          )}
          <SimpleGrid cols={3} spacing="xs">
            <TextInput
              disabled
              readOnly
              radius="lg"
              size="sm"
              label={t('common:usdPrice')}
              value={usdPrice}
            />
            <div>
              <TextInput
                disabled
                readOnly
                radius="lg"
                size="sm"
                value={currency(fees, {
                  symbol: '',
                  precision,
                }).format()}
                label={
                  <Text tt="capitalize">{t('common:paymentMethodFees')}</Text>
                }
              />
              {isToBeReceivedError && (
                <Text mx={2} color="red" size="sm">
                  {t('common:toBeReceivedError')}
                </Text>
              )}
            </div>
            <TextInput
              readOnly
              radius="lg"
              size="sm"
              value={toBeReceived}
              label={<Text tt="capitalize">{t('common:toBePaid')}</Text>}
            />
          </SimpleGrid>
          {!isAgentPaymentMethod && (
            <CustomFieldsForm
              form={form}
              fields={returnPaymentMethodCustomFields}
            />
          )}
          <Text>
            <TranslatedTextValue
              keyEn={selectedPaymentMethod?.shortDescriptionWithdraw ?? ''}
              keyAr={selectedPaymentMethod?.shortDescriptionWithdrawAr}
            />
          </Text>
          <Accordion radius="lg" variant="contained" defaultValue="description">
            <Accordion.Item value="description">
              <Accordion.Control
                icon={<Icon icon="info-circle" size={rem(20)} color="gray" />}
              >
                {t('common:description')}
              </Accordion.Control>
              <Accordion.Panel>
                <MarkdownView
                  markdown={getTranslatedTextValue(
                    locale,
                    selectedPaymentMethod?.descriptionWithdraw ?? '',
                    selectedPaymentMethod?.descriptionWithdrawAr,
                  )}
                  options={{ tables: true, emoji: true }}
                />
              </Accordion.Panel>
            </Accordion.Item>
          </Accordion>

          {(isAgentPaymentMethod || isInternalAgentPaymentMethod) && (
            <AgentsList
              viewAs={isAgentPaymentMethod ? 'list' : 'accordion'}
              withDescription={isAgentPaymentMethod}
              currencyCode={selectedCurrency?.code}
            />
          )}
          {!isAgentPaymentMethod && (
            <>
              <Textarea
                size="sm"
                radius="lg"
                placeholder={t('common:note') ?? ''}
                label={<Text tt="capitalize">{t('common:note')}</Text>}
                {...form.getInputProps('note')}
              />
              <SubmitButton
                disabled={isSubmitButtonDisabled}
                onClick={onSubmit}
                fullWidth
              >
                {WITHDRAW_TRANSLATE}
              </SubmitButton>
            </>
          )}
        </Stack>

        <TransactionConfirmModal
          close={close}
          openedDefault={opened}
          isLoading={isLoading}
          onClick={() => submit(undefined)}
          operationType="withdraw"
        >
          <WithdrawDepositDetails
            adminMessage={null}
            actualAmount={form.values.amount as number}
            amount={toBeReceived}
            currencyFrom={selectedCurrency}
            fees={fees}
            operationType="withdraw"
            paymentMethod={selectedPaymentMethod}
            originPaymentAmount={undefined}
          />
          <RenderCustomFields fields={form.values.fields} />
        </TransactionConfirmModal>
        <CodeConfirmModal
          codeError={codeError}
          btnText={t('common:withdraw')}
          setOpenedCode={setOpenedCode}
          isLoading={isLoading}
          onClick={(v) => submit(v)}
          openedDefault={openedCode}
          code={code}
          setCode={setCode}
          title={t('common:enterCode')}
          closeable={false}
          additionalContent={undefined}
        />
        {responseData && (
          <PopUpSuccess
            currencySymbol={responseData?.currency?.symbol}
            currencyToSymbol=""
            operationType="withdraw"
            setResponseData={setResponseData}
            operationId={responseData?.transactionId}
            amount={responseData?.amount}
            actualAmount={responseData?.actualAmount}
            fees={responseData?.totalFees}
            status={responseData?.status}
            note={responseData?.note}
            date={responseData?.createdAt}
            sender={undefined}
            receiver={undefined}
            currencyCode={responseData?.currency?.code}
            currencyToCode={undefined}
            paymentMethod={responseData?.paymentMethod}
            fields={responseData?.fields}
            rate={0}
            precision={responseData?.currency?.precision ?? 2}
            currencyToPrecision={2}
            gif={null}
            cryptoGetaway={responseData?.cryptoGetaway}
            originPaymentAmount={responseData.originPaymentAmount}
            isLoading={false}
            adminMessage={responseData?.adminMessage}
          />
        )}
        <ErrorPopup
          open={openLimitPopup}
          setOpen={setLimitPopup}
          message={t('errors:limit-error')}
          actionButton={(
            <Button
              fullWidth
              radius="lg"
              component={Link}
              href={`${ROUTES.myAccount.path}?page=limits`}
            >
              {t('common:goToLimits')}
            </Button>
          )}
        />
      </form>
      <Captcha reCaptchaRef={reCaptchaRef} active={playCaptcha} />
    </>
  );
}

// Wrapper that uses key to force remount when payment method changes
export default function WithdrawForm({
  selectedPaymentMethod,
  selectedCurrency,
  rates,
}: Props) {
  // Use payment method ID as key to force component remount when payment method changes
  return (
    <WithdrawFormInner
      key={selectedPaymentMethod?.id}
      selectedPaymentMethod={selectedPaymentMethod}
      selectedCurrency={selectedCurrency}
      rates={rates}
    />
  );
}
