import {
  Stack,
  Title,
  ActionIcon,
  Button,
  useMantineTheme,
} from '@mantine/core';

import Link from 'next/link';
import { useRouter } from 'next/router';
import React, { Component, PropsWithChildren } from 'react';
import { Layout } from '../layout/layout';
import { ROUTES } from '@/data';
import { IconSquareX } from '@tabler/icons-react';

function ErrorUI() {
  const theme = useMantineTheme();
  const router = useRouter();
  return (
    <Layout>
      <Stack h="70vh" justify="center" align="center" spacing="xs">
        <ActionIcon
          size={100}
          disabled
          sx={{
            ':disabled': {
              backgroundColor: 'transparent',
              border: 0,
              color:
                theme.colorScheme === 'dark'
                  ? theme.colors.red[9]
                  : theme.colors.red[7],
            },
          }}
        >
          <IconSquareX width={100} height={100} stroke={1.2} />
        </ActionIcon>
        <Title order={4} color="red">
          {router.locale === 'en'
            ? 'There is an error happened, please try again!'
            : 'حدث خطأ ما, يرجى إعادة المحاولة!'}
        </Title>
        <Button
          color="primary"
          component={Link}
          href={ROUTES.root.path}
          variant="subtle"
        >
          {router.locale === 'en' ? 'Go to home' : 'الذهاب للصفحة الرئيسية'}
        </Button>
      </Stack>
    </Layout>
  );
}
export default class ErrorBoundaries extends Component<
  PropsWithChildren,
  { hasError: boolean }
> {
  constructor(props: PropsWithChildren) {
    super(props);
    this.state = { hasError: false };
  }

  shouldComponentUpdate(): boolean {
    const { hasError } = this.state;
    if (hasError) this.setState({ hasError: false });
    return true;
  }

  componentDidCatch(): void {
    this.setState({ hasError: true });
  }

  render() {
    const { hasError } = this.state;
    const { children } = this.props;
    if (hasError) {
      return <ErrorUI />;
    }
    return children;
  }
}
