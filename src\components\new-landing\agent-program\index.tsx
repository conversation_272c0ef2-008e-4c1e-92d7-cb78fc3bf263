import {
  Container,
  Text,
  Image,
  SimpleGrid,
  Stack,
  Box,
  BackgroundImage,
  useMantineTheme,
} from '@mantine/core';
import styles from './styles.module.scss';
import useTranslation from 'next-translate/useTranslation';
import Link from 'next/link';
import { assetBaseUrl, ROUTES } from '@/data';
import { useRouter } from 'next/router';

export default function AgentProgram() {
  const { t } = useTranslation();
  const theme = useMantineTheme();
  const router = useRouter();
  return (
    <Box className={styles.root} id="agents-program">
      <Container
        py={40}
        px={{
          xs: 20,
          sm: 30,
          lg: 40,
          base: 20,
        }}
        size={1200}
      >
        <SimpleGrid
          cols={2}
          breakpoints={[
            { maxWidth: 'md', cols: 2, spacing: 'md' },
            { maxWidth: 'sm', cols: 1, spacing: 'sm' },
            { maxWidth: 'xs', cols: 1, spacing: 'sm' },
          ]}
        >
          <Stack
            justify="center"
            sx={() => ({
              [theme.fn.smallerThan('sm')]: {
                display: 'none',
              },
            })}
          >
            <BackgroundImage
              h={{
                lg: 550,
                md: 530,
                sm: 370,
                xs: 280,
                base: 250,
              }}
              w={{ xs: '90%', base: '100%' }}
              src={`${assetBaseUrl}/assets/new-landing/blur-hero.webp`}
              radius="xs"
            >
              <Stack h="100%" justify="center" align="center">
                <Image
                  width="100%"
                  src={`${assetBaseUrl}/assets/new-landing/agent-program.webp`}
                  alt="agent"
                  mx="auto"
                />
              </Stack>
              {' '}
            </BackgroundImage>
          </Stack>
          <Stack justify="center">
            <Text
              color="white"
              ff={router.locale === 'ar' ? theme.fontFamily : `BauhausC, ${theme.fontFamily}`}
              size={43}
              weight="bold"
            >
              {t('common:agentProgramLandingTitle')}
            </Text>
            <Text lh={1.7} size="lg" mt="lg" color="dimmed">
              {t('common:agentProgramLandingDescription')}
            </Text>
            <Link
              href={ROUTES.agent.path}
              style={{ textDecoration: 'none', color: theme.colors.primary[7] }}
              className={styles.aHero}
            >
              {t('common:joinOurAgentsNow')}
            </Link>
          </Stack>
          <Stack
            justify="center"
            sx={() => ({
              display: 'none',
              [theme.fn.smallerThan('sm')]: {
                display: 'block',
              },
            })}
          >
            <BackgroundImage
              mt={20}
              mx="auto"
              w={300}
              src={`${assetBaseUrl}/assets/new-landing/blur-hero.webp`}
              radius="xs"
            >
              <Image
                width={300}
                src={`${assetBaseUrl}/assets/new-landing/agent-program.webp`}
                alt="agent"
                mx="auto"
              />
            </BackgroundImage>
          </Stack>
        </SimpleGrid>
      </Container>
    </Box>
  );
}
