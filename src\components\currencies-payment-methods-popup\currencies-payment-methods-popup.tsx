import {
  Button,
  Group,
  Modal,
  ScrollArea,
  SimpleGrid,
  Stack,
  TextInput,
  UnstyledButton,
  useMantineTheme,
} from '@mantine/core';
import { useDisclosure, useMediaQuery } from '@mantine/hooks';
import classes from './styles.module.scss';

import useTranslation from 'next-translate/useTranslation';

import { UserApiResponse } from '@/store/user';

import currency from 'currency.js';

import { ReactNode, useRef, useState } from 'react';
import { Item } from './item';
import {
  IconChevronsDown,
  IconChevronsUp,
  IconSearch,
  IconSelector,
} from '@tabler/icons-react';
import { PaymentMethodsApiResponse } from '@/store/payment-methods';
import { CurrenciesApiResponse } from '@/store/currencies';
import { EmptyData } from '../common/empty-data';
import { getTranslatedTextValue } from '@/utils';
import { useRouter } from 'next/router';

interface CurrenciesOrPaymentMethodsPopupProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  setSelectedItem: (v: any) => void;
  selectedItem:
    | UserApiResponse['currencies'][0]
    | PaymentMethodsApiResponse['data'][0]
    | undefined;
  children: ReactNode;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: any[];
  // eslint-disable-next-line react/require-default-props
  disabled?: boolean;
  dataType: 'currency' | 'paymentMethod';
  paymentType: 'withdraw' | 'deposit' | undefined;
  currencySymbol: CurrenciesApiResponse['data'][0]['symbol'] | undefined;
  setLoading: (v: boolean) => void;
}

function CurrenciesOrPaymentMethodsPopup(
  props: CurrenciesOrPaymentMethodsPopupProps,
) {
  const {
    children,
    data,
    setSelectedItem,
    selectedItem,
    disabled,
    dataType,
    paymentType,
    currencySymbol,
    setLoading,
  } = props;
  const { t } = useTranslation();
  const router = useRouter();
  const { locale } = router;
  const theme = useMantineTheme();

  const [opened, { open, close }] = useDisclosure(false);

  const [searchKey, setSearchKey] = useState<string | undefined>();

  const viewportRef = useRef<HTMLDivElement>(null);

  const [scrollDirection, setScrollDirection] = useState<'up' | 'down'>('down');

  const isMobileScreen = useMediaQuery(
    `(max-width: ${theme.breakpoints.sm})`,
    true,
    {
      getInitialValueInEffect: false,
    },
  );

  const handleSelectPaymentMethod = (
    v: PaymentMethodsApiResponse['data'][0] | undefined,
  ) => {
    setSelectedItem(v);
    if (v) router.query.method = v.label;
    else delete router.query.method;
    router.push(router);
  };

  // handle item check trigger
  const handleItemClick = (v: string) => {
    const item = data?.filter((i) => i.value === v);
    if (dataType === 'currency') {
      setSelectedItem(item[0]);
    } else {
      setLoading(true);
      setSelectedItem(undefined);
      setTimeout(() => {
        handleSelectPaymentMethod(item[0]);
        setLoading(false);
      }, 1000);
    }
    setSearchKey(undefined);
    close();
  };
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const arrayFilterByValue = (array: any[], query: string | undefined) => {
    if (query) {
      return array?.filter(
        (item) => item?.label?.toLowerCase().includes(query.trim().toLowerCase())
          || getTranslatedTextValue(locale, item?.label, item?.labelAr)
            ?.toLowerCase()
            .includes(query.trim().toLowerCase())
          || item?.code?.toLowerCase().includes(query.trim().toLowerCase()),
      );
    }
    return array;
  };

  const items = arrayFilterByValue(data, searchKey)?.map((item) => (dataType === 'currency' ? (
    <Item
      key={item?.id}
      description={`${currency(item?.amount, {
        symbol: '',
        precision: item?.precision ?? 2,
      })} ${item?.symbol}`}
      image={item?.image ?? ''}
      title={getTranslatedTextValue(locale, item?.label, item?.labelAr)}
      onChange={handleItemClick}
      currencyId={item?.value}
      selected={selectedItem?.value === item?.value}
      fees={undefined}
      symbol=""
    />
  ) : (
    <Item
      key={item?.id}
      description=""
      image={item?.image ?? ''}
      title={getTranslatedTextValue(locale, item?.label, item?.labelAr)}
      onChange={handleItemClick}
      currencyId={item?.value}
      selected={selectedItem?.value === item?.value}
      fees={
          paymentType === 'deposit' && item?.tag === 'internal_agent'
            ? undefined
            : {
              percent:
                  paymentType === 'deposit'
                    ? item.depositFeesPercentage
                    : item.withdrawFeesPercentage,
              fixed:
                  paymentType === 'deposit'
                    ? item.depositFeesFixed
                    : item.withdrawFeesFixed,
            }
        }
      symbol={currencySymbol}
    />
  )));
  // handle scrolling by button
  const handleScroll = () => {
    if (!viewportRef.current) return;

    const itemHeight = 65;
    const scrollAmount = itemHeight * 5;
    const currentScroll = viewportRef.current.scrollTop;
    const maxScroll = viewportRef.current.scrollHeight - viewportRef.current.clientHeight;

    if (scrollDirection === 'down') {
      viewportRef.current.scrollBy({ top: scrollAmount, behavior: 'smooth' });

      // If close to the bottom, switch direction to 'up'
      if (currentScroll + scrollAmount >= maxScroll) {
        setScrollDirection('up');
      }
    } else {
      viewportRef.current.scrollBy({ top: -scrollAmount, behavior: 'smooth' });

      // If close to the top, switch direction to 'down'
      if (currentScroll - scrollAmount <= 0) {
        setScrollDirection('down');
      }
    }
  };
  // return boolean value to handle display scroll button
  const displayScrollButton = () => {
    let showButton = true;
    if (isMobileScreen && items?.length > 5) {
      showButton = true;
    } else if (!isMobileScreen && items?.length > 10) {
      showButton = true;
    } else showButton = false;
    return (
      showButton && (
        <Button onClick={handleScroll} mt="md" fullWidth variant="light">
          {scrollDirection === 'down' ? (
            <IconChevronsDown
              size={24}
              color="gray"
              className={classes.scroll_icon}
            />
          ) : (
            <IconChevronsUp
              size={24}
              color="gray"
              className={classes.scroll_icon}
            />
          )}
        </Button>
      )
    );
  };

  return (
    <>
      <Modal
        opened={opened}
        onClose={close}
        title={
          dataType === 'currency'
            ? t('common:chooseCurrency')
            : t('common:choosePaymentMethod')
        }
        centered
        radius="lg"
        size="auto"
      >
        <TextInput
          placeholder={t('common:search')}
          value={searchKey}
          onChange={(e) => setSearchKey(
            e?.currentTarget?.value === ''
              ? undefined
              : e?.currentTarget?.value,
          )}
          icon={<IconSearch />}
        />
        <ScrollArea
          viewportRef={viewportRef}
          mt="xs"
          miw={{ sm: 640, base: 320 }}
          scrollbarSize={5}
          h={400}
          pr={8}
          mr={-8}
        >
          {items?.length === 0 && (
            <Stack h={360} w="100%" justify="center" align="center">
              <EmptyData message="" />
            </Stack>
          )}
          {items?.length > 0 && (
            <SimpleGrid
              cols={1}
              breakpoints={[
                { maxWidth: 'sm', cols: 1 },
                { minWidth: 'sm', cols: 2 },
              ]}
            >
              {items}
            </SimpleGrid>
          )}
        </ScrollArea>
        {displayScrollButton()}
      </Modal>

      <Group h="100%" position="apart">
        <UnstyledButton
          disabled={disabled}
          display="flex"
          sx={{ justifyContent: 'space-between', alignItems: 'center' }}
          miw={300}
          w="100%"
          onClick={open}
        >
          {children}
          <IconSelector color="red" />
        </UnstyledButton>
      </Group>
    </>
  );
}

export default CurrenciesOrPaymentMethodsPopup;
