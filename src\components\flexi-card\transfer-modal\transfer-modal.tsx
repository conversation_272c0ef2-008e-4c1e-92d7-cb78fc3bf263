import {
  <PERSON><PERSON>, <PERSON><PERSON>, Group, Text, Box, Button, Alert,
  ScrollArea, useMantineTheme, rem,
} from '@mantine/core';
import { IconPlus, IconInfoCircle } from '@tabler/icons-react';

interface Card {
  id: string;
  cardNumber: string;
  cardHolderName: string;
  expiryDate: string;
  balance: number;
  currency: string;
  cardType: string;
  status: string;
  brand: string;
}

interface TransferModalProps {
  opened: boolean;
  onClose: () => void;
  card: Card;
  onApplyNewCard: () => void;
}

export function TransferModal({
  opened,
  onClose,
  card,
  onApplyNewCard,
}: TransferModalProps) {
  const theme = useMantineTheme();

  // Get last 4 digits of card number
  const lastFourDigits = card.cardNumber.slice(-4);

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title=""
      centered
      size="sm"
      radius="xl"
      scrollAreaComponent={ScrollArea.Autosize}
      styles={{
        header: {
          padding: '20px 24px 0 24px',
          borderBottom: 'none',
        },
        body: {
          padding: '0 24px 24px 24px',
        },
        close: {
          border: 'none',
          backgroundColor: theme.colorScheme === 'dark' ? theme.colors.dark[5] : '#f8f9fa',
          borderRadius: '50%',
          width: rem(32),
          height: rem(32),
          color: theme.colorScheme === 'dark' ? theme.white : '#495057',
          '&:hover': {
            backgroundColor: theme.colorScheme === 'dark' ? theme.colors.dark[4] : '#e9ecef',
          },
        },
        content: {
          backgroundColor: theme.colorScheme === 'dark' ? theme.colors.dark[7] : theme.white,
        },
        overlay: {
          backgroundColor: 'rgba(0, 0, 0, 0.4)',
        },
      }}
    >
      <Stack spacing="lg">
        {/* Header */}
        <Group position="apart" align="center">
          <Text size="lg" weight={600}>
            Balance Transfer
          </Text>
        </Group>

        {/* From Card Info */}
        <Box>
          <Group spacing="sm" align="center" mb="xs">
            {/* Card Brand Icon - Using a simple box as placeholder */}
            <Box
              sx={{
                width: 32,
                height: 20,
                backgroundColor: '#1a1a1a',
                borderRadius: 4,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Text size="xs" color="white" weight={700}>
                VISA
              </Text>
            </Box>
            <Text size="sm" weight={500}>
              From
              {' '}
              {card.cardType}
              {' '}
              ••
              {lastFourDigits}
            </Text>
          </Group>
          <Text size="sm" color="dimmed">
            $
            {' '}
            {card.balance.toFixed(2)}
            {' '}
            {card.currency}
          </Text>
        </Box>

        {/* Info Alert */}
        <Alert
          icon={<IconInfoCircle size={16} />}
          color="blue"
          variant="light"
          p={10}
          styles={{
            root: {
              backgroundColor: theme.colorScheme === 'dark' ? theme.colors.blue[9] : '#f0f8ff',
              border: 'none',
            },
            icon: {
              color: theme.colors.blue[6],
            },
            message: {
              color: theme.colorScheme === 'dark' ? theme.colors.blue[2] : theme.colors.blue[7],
            },
          }}
        >
          <Text size="sm">
            You need two cards registered to the same phone number to initiate balance transfer.
          </Text>
        </Alert>

        {/* Illustration Container */}
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            padding: '10px',
            textAlign: 'center',
          }}
        >
          {/* Phone with Card Illustration */}
          <Box
            sx={{
              width: 120,
              height: 160,
              position: 'relative',
              marginBottom: 24,
            }}
          >
            {/* Phone outline */}
            <Box
              sx={{
                width: '100%',
                height: '100%',
                border: '3px solid #e9ecef',
                borderRadius: 20,
                backgroundColor: 'white',
                position: 'relative',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              {/* Screen content */}
              <Box
                sx={{
                  width: '80%',
                  height: '70%',
                  backgroundColor: '#f8f9fa',
                  borderRadius: 8,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  position: 'relative',
                }}
              >
                <Text size="xs" weight={700} color="green">
                  000
                </Text>
              </Box>
            </Box>

            {/* Floating card/money icon */}
            <Box
              sx={{
                position: 'absolute',
                top: -10,
                left: -20,
                width: 40,
                height: 25,
                backgroundColor: '#51cf66',
                borderRadius: 20,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Text size="xs" color="white">💳</Text>
            </Box>
          </Box>

          <Text size="lg" weight={600} mb="xs">
            No valid card available
          </Text>
          <Text size="sm" color="dimmed" mb="xl">
            You do not have a valid second card that can receive transferred balance
          </Text>

          {/* Apply for New Card Button */}
          <Button
            variant="outline"
            color="dark"
            radius="xl"
            size="md"
            leftIcon={<IconPlus size={16} />}
            onClick={onApplyNewCard}
            styles={{
              root: {
                borderColor: theme.colors.gray[4],
                color: theme.colorScheme === 'dark' ? theme.white : theme.colors.dark[6],
                '&:hover': {
                  backgroundColor: theme.colorScheme === 'dark' ? theme.colors.dark[6] : theme.colors.gray[0],
                },
              },
            }}
          >
            Apply for a New Card
          </Button>
        </Box>
      </Stack>
    </Modal>
  );
}
