/* eslint-disable max-lines */
/* eslint-disable no-console */
/* eslint-disable sonarjs/cognitive-complexity */
/* eslint-disable complexity */
import {
  Stack, Group, Text, ScrollArea, Button, UnstyledButton,
  Box,
} from '@mantine/core';
import { Dispatch, SetStateAction, useState } from 'react';
import { useDisclosure } from '@mantine/hooks';
import useTranslation from 'next-translate/useTranslation';
import {
  IconShoppingBag, IconCreditCard, IconArrowUp, IconArrowDown,
  IconSettings, IconFileText, IconInfoCircle,
} from '@tabler/icons-react';
import { TransactionModal, TransactionData } from './transaction-modal';

interface Transaction {
  id: string;
  description: string;
  amount: number;
  date: string;
  time?: string;
  type: string;
  status?: string;
  activity?: string;
  card?: string;
  netAmountCredited?: number;
  generalTxnFee?: number;
  txnRef?: string;
  descriptor?: string;
  privateNote?: string;
}

interface CardTransactionsListProps {
  transactions: Transaction[];
  activeTab: string;
  setActiveTab: Dispatch<SetStateAction<string>>;
}

export function CardTransactionsList({ transactions, activeTab, setActiveTab }: CardTransactionsListProps) {
  const { t } = useTranslation();
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [modalOpened, setModalOpened] = useState(false);
  const [noteModalOpened, { open: openNoteModal, close: closeNoteModal }] = useDisclosure(false);

  const filteredTransactions = transactions.filter((transaction) => {
    if (activeTab === 'all') return true;
    if (activeTab === 'transactions') return transaction.type === 'purchase';
    if (activeTab === 'maintenance') return ['fee', 'topup'].includes(transaction.type);
    return true;
  });

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'purchase':
        return <IconShoppingBag size={18} color="#5C5F66" />;
      case 'topup':
        return <IconArrowDown size={18} color="#5C5F66" />;
      case 'fee':
        return <IconArrowUp size={18} color="#5C5F66" />;
      case 'maintenance':
        return <IconSettings size={18} color="#5C5F66" />;
      case 'other':
        return <IconFileText size={18} color="#5C5F66" />;
      default:
        return <IconCreditCard size={18} color="#5C5F66" />;
    }
  };

  const handleTransactionClick = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    setModalOpened(true);
  };

  const handleCloseModal = () => {
    setModalOpened(false);
    setSelectedTransaction(null);
  };

  return (
    <Stack spacing="md">
      {/* Header */}
      <Text size="lg" weight={700}>
        {t('common:activities')}
      </Text>

      {/* Tabs */}
      <Group
        spacing="xs"
        mb="xs"
        sx={(theme) => ({
          [theme.fn.smallerThan('sm')]: {
            justifyContent: 'space-between',
            gap: '4px',
          },
        })}
      >
        <Button
          variant={activeTab === 'all' ? 'filled' : 'subtle'}
          color={activeTab === 'all' ? 'dark' : 'gray'}
          size="sm"
          radius="lg"
          onClick={() => setActiveTab('all')}
          sx={(theme) => ({
            backgroundColor: activeTab === 'all' ? '#262626ff' : '#25262B',
            color: activeTab === 'all' ? '#fff' : '#c0c0c0ff',
            border: 'none',
            fontWeight: 500,
            '&:hover': {
              backgroundColor: activeTab === 'all' ? '#1f1f1fff' : '#444444ff',
            },
            [theme.fn.smallerThan('sm')]: {
              flex: 1,
              fontSize: '12px',
              padding: '8px 12px',
              minWidth: 'auto',
            },
          })}
        >
          {t('common:all')}
        </Button>
        <Button
          variant={activeTab === 'transactions' ? 'filled' : 'subtle'}
          color={activeTab === 'transactions' ? 'dark' : 'gray'}
          size="sm"
          radius="lg"
          onClick={() => setActiveTab('transactions')}
          sx={(theme) => ({
            backgroundColor: activeTab === 'transactions' ? '#262626ff' : '#25262B',
            color: activeTab === 'transactions' ? '#fff' : '#c0c0c0ff',
            border: 'none',
            fontWeight: 500,
            '&:hover': {
              backgroundColor: activeTab === 'transactions' ? '#1f1f1fff' : '#444444ff',
            },
            [theme.fn.smallerThan('sm')]: {
              flex: 1,
              fontSize: '12px',
              padding: '8px 12px',
              minWidth: 'auto',
            },
          })}
        >
          {t('common:transactions')}
        </Button>
        <Button
          variant={activeTab === 'maintenance' ? 'filled' : 'subtle'}
          color={activeTab === 'maintenance' ? 'dark' : 'gray'}
          size="sm"
          radius="lg"
          onClick={() => setActiveTab('maintenance')}
          sx={(theme) => ({
            backgroundColor: activeTab === 'maintenance' ? '#262626ff' : '#25262B',
            color: activeTab === 'maintenance' ? '#fff' : '#c0c0c0ff',
            border: 'none',
            fontWeight: 500,
            '&:hover': {
              backgroundColor: activeTab === 'maintenance' ? '#1f1f1fff' : '#444444ff',
            },
            [theme.fn.smallerThan('sm')]: {
              flex: 1,
              fontSize: '12px',
              padding: '8px 12px',
              minWidth: 'auto',
            },
          })}
        >
          <Text
            sx={(theme) => ({
              [theme.fn.smallerThan('sm')]: {
                fontSize: '12px',
                lineHeight: 1.2,
                textAlign: 'center',
              },
            })}
          >
            {t('common:cardMaintenance')}
          </Text>
        </Button>
      </Group>

      {/* Transaction Headers */}
      <Group
        position="apart"
        px="sm"
        pb="xs"
        sx={(theme) => ({
          [theme.fn.smallerThan('sm')]: {
            paddingLeft: '8px',
            paddingRight: '8px',
          },
        })}
      >
        <Text
          size="sm"
          weight={500}
          color="dimmed"
          sx={(theme) => ({
            [theme.fn.smallerThan('sm')]: {
              fontSize: '12px',
            },
          })}
        >
          {t('common:transaction')}
        </Text>
        <Text
          size="sm"
          weight={500}
          color="dimmed"
          sx={(theme) => ({
            [theme.fn.smallerThan('sm')]: {
              fontSize: '12px',
            },
          })}
        >
          {t('common:amount')}
        </Text>
      </Group>

      {/* Transaction List */}
      <ScrollArea.Autosize
        mah={400}
        sx={(theme) => ({
          width: '100%',
          [theme.fn.smallerThan('sm')]: {
            maxHeight: 350,
          },
        })}
      >
        <Stack spacing={0}>
          {filteredTransactions.length > 0 ? (
            filteredTransactions.map((transaction) => (
              <UnstyledButton
                key={transaction.id}
                onClick={() => handleTransactionClick(transaction)}
                sx={(theme) => ({
                  width: '100%',
                  padding: '12px 8px',
                  '&:hover': {
                    backgroundColor: theme.colorScheme === 'dark' ? theme.colors.dark[7] : '#f1f3f5',
                  },
                  [theme.fn.smallerThan('sm')]: {
                    padding: '10px 6px',
                  },
                })}
              >
                <Group position="apart" noWrap>
                  <Group spacing="sm" noWrap>
                    <Box
                      sx={(theme) => ({
                        width: 40,
                        height: 40,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderRadius: '4px',
                        [theme.fn.smallerThan('sm')]: {
                          width: 32,
                          height: 32,
                        },
                      })}
                    >
                      {getTransactionIcon(transaction.type)}
                    </Box>
                    <Stack spacing={2}>
                      <Text
                        size="sm"
                        weight={700}
                        sx={(theme) => ({
                          [theme.fn.smallerThan('sm')]: {
                            fontSize: '13px',
                          },
                        })}
                      >
                        {transaction.description}
                      </Text>
                      <Text
                        size="xs"
                        color="dimmed"
                        weight={700}
                        sx={(theme) => ({
                          [theme.fn.smallerThan('sm')]: {
                            fontSize: '11px',
                          },
                        })}
                      >
                        {transaction.date}
                      </Text>
                    </Stack>
                  </Group>
                  <Group spacing={4} noWrap>
                    <Text
                      size="sm"
                      weight={500}
                      color={transaction.amount > 0 ? '#40c057' : '#fa5252'}
                      sx={(theme) => ({
                        [theme.fn.smallerThan('sm')]: {
                          fontSize: '13px',
                        },
                      })}
                    >
                      {transaction.amount > 0 ? '+' : ''}
                      {transaction.amount === 0 ? '-' : `$${Math.abs(transaction.amount).toFixed(2)}`}
                    </Text>
                    {transaction.txnRef && (
                    <IconInfoCircle
                      size={16}
                      color="#adb5bd"
                      style={{ marginLeft: 4 }}
                    />
                    )}
                  </Group>
                </Group>
              </UnstyledButton>
            ))
          ) : (
            <Stack align="center" py="xl">
              <Text size="sm" color="dimmed">
                {t('common:noTransactionsFound')}
              </Text>
            </Stack>
          )}
        </Stack>
      </ScrollArea.Autosize>

      {/* Transaction Modal */}
      {selectedTransaction && (
      <TransactionModal
        opened={modalOpened}
        onClose={handleCloseModal}
        transaction={selectedTransaction as TransactionData}
        onMakeNote={() => {
          // Handle make note functionality
          console.log('Make note for transaction:', selectedTransaction.id);
          openNoteModal();
        }}
        onSaveNote={(note: string) => {
          // Handle save note functionality
          console.log('Save note for transaction:', selectedTransaction.id, 'Note:', note);
          // Here you would typically update the transaction data with the new note
          // For example: updateTransaction(selectedTransaction.id, { privateNote: note });
        }}
        noteModalOpened={noteModalOpened}
        onCloseNoteModal={closeNoteModal}
      />
      )}
    </Stack>
  );
}
