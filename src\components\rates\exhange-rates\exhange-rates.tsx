/* eslint-disable max-lines */
import {
  Text,
  Stack,
  Divider,
  Loader,
  MantineColor,
  Image,
  Grid,
} from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';
import { RatesApiResponse } from '@/store/rate';
import Link from 'next/link';
import { ROUTES } from '@/data';
import { flags } from './rates-flags-mapping';
import RatesItem from './rate-item';

interface RatesProps {
  rates: RatesApiResponse;
  isLoading: boolean;
  forLandingPage: boolean;
  color: MantineColor | undefined;
}

const translateBuy = 'common:buy';
const translateSell = 'common:sell';

export default function ExchangeRatesList({
  rates,
  isLoading,
  forLandingPage,
  color,
}: RatesProps) {
  const { t } = useTranslation();
  return (
    <Stack mb="lg" mx="auto" maw={800}>
      {isLoading && <Loader mx="auto" my={25} />}
      {forLandingPage && !isLoading && (
        <>
          <Text
            mt="xl"
            mb="md"
            size="lg"
            ta="center"
            weight={500}
            color="dimmed"
          >
            {t('common:exchangeRates')}
          </Text>
          <Grid>
            <Grid.Col span={6} sm={forLandingPage ? 8 : 6}>
              <Text color={color} weight={700}>
                {t('common:currency')}
              </Text>
            </Grid.Col>
            <Grid.Col span={3} sm={forLandingPage ? 2 : 3}>
              <Text color={color} weight={700}>
                {t(translateBuy)}
              </Text>
            </Grid.Col>
            <Grid.Col span={3} sm={forLandingPage ? 2 : 3}>
              <Text color={color} weight={700}>
                {t(translateSell)}
              </Text>
            </Grid.Col>
          </Grid>
          <Divider w="100%" />
          <RatesItem
            color={color}
            rateFrom={rates?.rates?.SYP_FROM}
            rateTo={rates?.rates?.SYP_TO}
            code="SYP"
            flag={
              <Image width={30} height="22.7px" radius="sm" src={flags.SYP} />
            }
            forLandingPage
          />
          <Divider w="100%" />
          <RatesItem
            color={color}
            rateFrom={rates?.rates?.AED_FROM}
            rateTo={rates?.rates?.AED_TO}
            code="AED"
            flag={flags.AED}
            forLandingPage
          />

          <Divider w="100%" />
          <RatesItem
            color={color}
            rateFrom={rates?.rates?.EGP_FROM}
            rateTo={rates?.rates?.EGP_TO}
            code="EGP"
            flag={flags.EGP}
            forLandingPage
          />

          <Divider w="100%" />
          <RatesItem
            color={color}
            rateFrom={rates?.rates?.SAR_FROM}
            rateTo={rates?.rates?.SAR_TO}
            code="SAR"
            flag={flags.SAR}
            forLandingPage
          />

          <Divider w="100%" />
          <RatesItem
            color={color}
            rateFrom={rates?.rates?.USDT_FROM}
            rateTo={rates?.rates?.USDT_TO}
            code="USDT"
            flag={<Text size={22}>{flags.CRYPTO}</Text>}
            forLandingPage
          />
        </>
      )}
      {!forLandingPage && !isLoading && (
        <>
          <Text
            mt="xl"
            mb="md"
            size="lg"
            ta="center"
            weight={500}
            color="dimmed"
          >
            {t('common:fiatPriceToUSD')}
          </Text>
          {/* Fiat Currencies Section */}
          <Grid align="center">
            <Grid.Col span={6}>
              <Text size="lg" weight={700} color={color} mt="lg" mb="xs">
                {t('common:fiatCurrencies')}
              </Text>
            </Grid.Col>
            <Grid.Col span={3}>
              <Text color={color} weight={700}>
                {t(translateBuy)}
              </Text>
            </Grid.Col>
            <Grid.Col span={3}>
              <Text color={color} weight={700}>
                {t(translateSell)}
              </Text>
            </Grid.Col>
          </Grid>
          <Divider w="100%" />
          <RatesItem
            color={color}
            rateFrom={rates?.rates?.SYP_FROM}
            rateTo={rates?.rates?.SYP_TO}
            code="SYP"
            flag={
              <Image width={30} height="22.7px" radius="sm" src={flags.SYP} />
            }
          />

          <Divider w="100%" />
          <RatesItem
            color={color}
            rateFrom={rates?.rates?.AED_FROM}
            rateTo={rates?.rates?.AED_TO}
            code="AED"
            flag={flags.AED}
          />

          <Divider w="100%" />
          <RatesItem
            color={color}
            rateFrom={rates?.rates?.USD_FROM}
            rateTo={rates?.rates?.USD_TO}
            code="USD"
            flag={flags.USD}
          />

          <Divider w="100%" />
          <RatesItem
            color={color}
            rateFrom={rates?.rates?.EGP_FROM}
            rateTo={rates?.rates?.EGP_TO}
            code="EGP"
            flag={flags.EGP}
          />

          <Divider w="100%" />
          <RatesItem
            color={color}
            rateFrom={rates?.rates?.EUR_FROM}
            rateTo={rates?.rates?.EUR_TO}
            code="EUR"
            flag={
              <Image width={30} height="22.7px" radius="sm" src={flags.EUR} />
            }
          />

          <Divider w="100%" />
          <RatesItem
            color={color}
            rateFrom={rates?.rates?.RUB_FROM}
            rateTo={rates?.rates?.RUB_TO}
            code="RUB"
            flag={flags.RUB}
          />

          <Divider w="100%" />
          <RatesItem
            color={color}
            rateFrom={rates?.rates?.LBP_FROM}
            rateTo={rates?.rates?.LBP_TO}
            code="LBP"
            flag={flags.LBP}
          />

          <Divider w="100%" />
          <RatesItem
            color={color}
            rateFrom={rates?.rates?.IQD_FROM}
            rateTo={rates?.rates?.IQD_TO}
            code="IQD"
            flag={flags.IQD}
          />

          <Divider w="100%" />
          <RatesItem
            color={color}
            rateFrom={rates?.rates?.JOD_FROM}
            rateTo={rates?.rates?.JOD_TO}
            code="JOD"
            flag={flags.JOD}
          />

          <Divider w="100%" />
          <RatesItem
            color={color}
            rateFrom={rates?.rates?.BDT_FROM}
            rateTo={rates?.rates?.BDT_TO}
            code="BDT"
            flag={flags.BDT}
          />

          <Divider w="100%" />
          <RatesItem
            color={color}
            rateFrom={rates?.rates?.SAR_FROM}
            rateTo={rates?.rates?.SAR_TO}
            code="SAR"
            flag={flags.SAR}
          />

          <Divider w="100%" />
          <RatesItem
            color={color}
            rateFrom={rates?.rates?.TRY_FROM}
            rateTo={rates?.rates?.TRY_TO}
            code="TRY"
            flag={flags.TRY}
          />

          <Divider w="100%" />
          <RatesItem
            color={color}
            rateFrom={rates?.rates?.IDR_FROM}
            rateTo={rates?.rates?.IDR_TO}
            code="IDR"
            flag={flags.IDR}
          />
          {/* Crypto Currencies Section */}
          <Text
            size="lg"
            ta="center"
            weight={500}
            color="dimmed"
            mt={25}
          >
            {t('common:cryptoPriceToUSD')}
          </Text>
          <Grid align="center">
            <Grid.Col span={6}>
              <Text size="lg" weight={700} color={color} mt="md" mb="xs">
                {t('common:cryptoCurrencies')}
                {' '}
              </Text>
            </Grid.Col>
            <Grid.Col span={3}>
              <Text color={color} weight={700}>
                {t(translateBuy)}
              </Text>
            </Grid.Col>
            <Grid.Col span={3}>
              <Text color={color} weight={700}>
                {t(translateSell)}
              </Text>
            </Grid.Col>
          </Grid>
          <Divider w="100%" />
          <RatesItem
            color={color}
            rateFrom={rates?.ratesToUsd?.ETH_FROM}
            rateTo={rates?.ratesToUsd?.ETH_TO}
            code="ETH"
            flag={<Text size={22}>{flags.CRYPTO}</Text>}
            isCrypto
          />

          <Divider w="100%" />
          <RatesItem
            color={color}
            rateFrom={rates?.ratesToUsd?.BTC_FROM}
            rateTo={rates?.ratesToUsd?.BTC_TO}
            code="BTC"
            flag={<Text size={22}>{flags.CRYPTO}</Text>}
            isCrypto
          />

          <Divider w="100%" />
          <RatesItem
            color={color}
            rateFrom={rates?.ratesToUsd?.LTC_FROM}
            rateTo={rates?.ratesToUsd?.LTC_TO}
            code="LTC"
            flag={<Text size={22}>{flags.CRYPTO}</Text>}
            isCrypto
          />

          <Divider w="100%" />
          <RatesItem
            color={color}
            rateFrom={rates?.ratesToUsd?.USDT_FROM}
            rateTo={rates?.ratesToUsd?.USDT_TO}
            code="USDT"
            flag={<Text size={22}>{flags.CRYPTO}</Text>}
            isCrypto
          />

          <Divider w="100%" />
          <RatesItem
            color={color}
            rateFrom={rates?.ratesToUsd?.BNB_FROM}
            rateTo={rates?.ratesToUsd?.BNB_TO}
            code="BNB"
            flag={<Text size={22}>{flags.CRYPTO}</Text>}
            isCrypto
          />

          <Divider w="100%" />
          <RatesItem
            color={color}
            rateFrom={rates?.ratesToUsd?.TRX_FROM}
            rateTo={rates?.ratesToUsd?.TRX_TO}
            code="TRX"
            flag={<Text size={22}>{flags.CRYPTO}</Text>}
            isCrypto
          />

          <Divider w="100%" />
          <RatesItem
            color={color}
            rateFrom={rates?.ratesToUsd?.TON_FROM}
            rateTo={rates?.ratesToUsd?.TON_TO}
            code="TON"
            flag={<Text size={22}>{flags.CRYPTO}</Text>}
            isCrypto
          />

          <Divider w="100%" />
          <RatesItem
            color={color}
            rateFrom={rates?.ratesToUsd?.SOL_FROM}
            rateTo={rates?.ratesToUsd?.SOL_TO}
            code="SOL"
            flag={<Text size={22}>{flags.CRYPTO}</Text>}
            isCrypto
          />

          <Divider w="100%" />
          <RatesItem
            color={color}
            rateFrom={rates?.ratesToUsd?.USDC_FROM}
            rateTo={rates?.ratesToUsd?.USDC_TO}
            code="USDC"
            flag={<Text size={22}>{flags.CRYPTO}</Text>}
            isCrypto
          />
        </>
      )}
      {forLandingPage && (
        <Link
          style={{
            textDecoration: 'none',
            textAlign: 'center',
            width: 130,
            margin: 'auto',
            color: 'gray',
          }}
          href={ROUTES.rates.path}
        >
          {t('common:showMore')}
        </Link>
      )}
    </Stack>
  );
}
