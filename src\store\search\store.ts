/**
 * spotlight store to store las search results in local storage
 */
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

type SearchResult={
  id:string
  title:string
  description?:string
  href?:string
  image?:string
  icon?:string
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data?:any
}

interface State {
  searchResult: SearchResult[] | [];
}
interface Actions {
  setSearchResult: (lastSearch: State['searchResult']|[]) => void;
  addNewSearchResult: (searchResult: State['searchResult'][number]) => void;
}
export const useLastSearch = create(
  persist<State & Actions>(
    (set) => ({
      searchResult: [],
      setSearchResult(lastSearch) {
        if (lastSearch) set(() => ({ searchResult: lastSearch }));
        else set(() => ({ searchResult: [] }));
      },
      addNewSearchResult(newSearchItem) {
        set((state) => ({
          searchResult: [newSearchItem, ...state.searchResult],
        }));
      },
    }),
    { name: 'lastSearch' },
  ),
);
