import { z } from 'zod';
import {
  createMassPayoutApiRequestSchema,
  createMassPayoutBackendRequestSchema,
} from './request-transformer';
import { massPayoutApiResponseSchema } from './responses-transformers';

export type CreateMassPayoutBackendRequest = z.infer<
  typeof createMassPayoutBackendRequestSchema
>;
export type CreateMassPayoutApiRequest = z.infer<
  typeof createMassPayoutApiRequestSchema
>;
export type MassPayoutApiResponse = z.infer<
  typeof massPayoutApiResponseSchema
>;

export type TransferItemType={
  id: number;
  amount: number;
  account: string;
  notes: string;
  success: boolean;
  error: string;
}
