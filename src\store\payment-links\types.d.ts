import { z } from 'zod';
import { createPaymentLinkApiRequestSchema } from './request-transformer';
import { paymentLinksApiResponseSchema } from './responses-transformers';

export type Filter = {
  uid: string;
};
export interface getPaymentLinksQueryProps {
  filters: Filter;
}

export type CreatePaymentLinkApiRequest = z.infer<
  typeof createPaymentLinkApiRequestSchema
>;
export type PaymentLinksApiResponse = z.infer<
  typeof paymentLinksApiResponseSchema
>;
