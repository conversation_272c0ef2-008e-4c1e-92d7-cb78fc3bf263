import { apiEndpoints } from '@/data';
import { ApiClient } from '@/lib';
import { handleApiError } from '@/utils/api-utils/handle-backend-error';
import { CreatePaymentApiRequest, getPaymentsQueryProps } from './types';
import { QueryFunctionContext } from '@tanstack/react-query';

enum queryKeys {
  create = 'create',
  payments = 'payments',
  payment = 'payment',
}

/** ****************************************************** */
/**
 * @description function calls handler in "/payments" api route with "post" method to create payment.
 * If user enabled 2FA will get "qrCode" param if type of the payment is "withdraw" ,
 *  and will check code validity before create the payment.
 * @param "body,qrCode"
 * @returns created payment details or error if failed
 */
const createPaymentRequest = ({
  body,
  qrCode,
  chaKey,
}: {
  body: CreatePaymentApiRequest;
  qrCode?: string;
  chaKey?: string;
}) => ApiClient.post(apiEndpoints.payments(), body, {
  params: {
    qrCode,
    chaKey,
  },
})
  .then((res) => res.data)
  .catch((err) => {
    handleApiError(err);
    throw err;
  });

export const createPaymentMutation = () => ({
  mutationKey: [queryKeys.create],
  mutationFn: ({
    body,
    qrCode,
    chaKey,
  }: {
    body: CreatePaymentApiRequest;
    qrCode?: string;
    chaKey?: string;
  }) => createPaymentRequest({ body, qrCode, chaKey }),
});

/** ****************************************************** */
/**
 * @description function call handler in "/payments" api route with "get" method to fetch user payments.
 * @param props
 * @returns list of payment or error if failed
 */
const getPaymentsRequest = (props: getPaymentsQueryProps) => {
  const { pagination, filters, params } = props;
  return ApiClient.get(apiEndpoints.payments(), {
    params: {
      page: params ? params.pageParam : pagination?.page,
      pageSize: pagination?.pageSize,
      start: pagination?.start,
      limit: pagination?.limit,
      type: filters?.type,
      currencyId: filters?.currencyId,
      currencyUid: filters?.currencyUid,
      search: filters?.search,
      paymentMethod: filters?.paymentMethod,
      status: filters?.status,
      createdFrom: filters?.createdFrom,
      createdTo: filters?.createdTo,
    },
  })
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e, true);
      throw e.response?.data;
    });
};
export const getPaymentsQuery = (props: getPaymentsQueryProps) => ({
  queryKey: [
    queryKeys.payments,
    props?.filters,
    props?.pagination?.page,
    props?.pagination?.pageSize,
  ],
  queryFn: (params: QueryFunctionContext) => getPaymentsRequest({ ...props, params }),
  refetchOnWindowFocus: false,
  // if the last loaded page from pagination pages is less than the pagination page count set next page is last page +1
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  getNextPageParam: (lastPage: any) => lastPage?.pagination?.page < lastPage?.pagination?.pageCount
    && lastPage.pagination.page + 1,
});
const getPaymentRequest = (id: string, props: getPaymentsQueryProps) => {
  const { filters } = props;
  return ApiClient.get(apiEndpoints.paymentByIdAsParam(id), {
    params: {
      type: filters?.type,
    },
  })
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e, true);
      throw e.response?.data;
    });
};
export const getPaymentQuery = (
  id: number | undefined,
  props: getPaymentsQueryProps,
) => ({
  queryKey: [queryKeys.payment],
  // if founded id call get payment function
  queryFn: () => id && getPaymentRequest(`${id}`, props),
  refetchOnWindowFocus: false,
});
