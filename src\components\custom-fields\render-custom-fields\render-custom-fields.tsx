import { TranslatedTextValue } from '@/components/common/translated-text-value';
import { DATE_FORMAT } from '@/data';
import { PaymentsApiResponse } from '@/store/payment';
import { CUSTOM_FIELDS_TYPE } from '@/types/custom-fields-type.type';
import {
  Accordion, Anchor, Group, Image, Stack, Text,
} from '@mantine/core';
import dayjs from 'dayjs';
import useTranslation from 'next-translate/useTranslation';
import React from 'react';

function RenderCustomFields({
  fields,
}: {
  fields: PaymentsApiResponse['data'][0]['fields'] | undefined;
}) {
  const { t } = useTranslation();
  const renderFields = () => fields?.map((item) => {
    if (
      item?.type === CUSTOM_FIELDS_TYPE.string
        || item?.type === CUSTOM_FIELDS_TYPE.number
        || item?.type === CUSTOM_FIELDS_TYPE.list
    ) {
      return (
        <Group key={item?.name}>
          <Text tt="capitalize" color="dimmed" weight={500} miw={130}>
            <TranslatedTextValue
              keyEn={item?.name ?? ''}
              keyAr={item?.nameAr}
            />
          </Text>
          <Text>{`${item?.value}`}</Text>
        </Group>
      );
    }
    if (item?.type === CUSTOM_FIELDS_TYPE.multiple) {
      return (
        <Group align="start" key={item?.name}>
          <Text tt="capitalize" color="dimmed" weight={500} miw={130}>
            <TranslatedTextValue
              keyEn={item?.name ?? ''}
              keyAr={item?.nameAr}
            />
          </Text>
          <Stack>
            {item?.value?.map((i: string) => (
              <Text key={i}>{i}</Text>
            ))}
          </Stack>
        </Group>
      );
    }
    if (item?.type === CUSTOM_FIELDS_TYPE.datetime) {
      return (
        <Group key={item?.name}>
          <Text tt="capitalize" color="dimmed" weight={500} miw={130}>
            <TranslatedTextValue
              keyEn={item?.name ?? ''}
              keyAr={item?.nameAr}
            />
          </Text>
          <Text>{dayjs(`${item?.value}`).format(DATE_FORMAT)}</Text>
        </Group>
      );
    }
    return (
      <Group key={item?.name}>
        <Text tt="capitalize" color="dimmed" weight={500} miw={130} maw={130}>
          <TranslatedTextValue
            keyEn={item?.name ?? ''}
            keyAr={item?.nameAr}
          />
        </Text>
        <Anchor target="_blank" href={item?.value?.url}>
          <Image
            width={100}
            height={100}
            src={item?.value?.url}
            alt="payment-method"
          />
        </Anchor>
      </Group>
    );
  });
  return fields && fields?.length > 0 ? (
    <Accordion defaultValue="">
      <Accordion.Item value="moreDetails">
        <Accordion.Control>{t('common:moreDetails')}</Accordion.Control>
        <Accordion.Panel>
          <Stack>{renderFields()}</Stack>
        </Accordion.Panel>
      </Accordion.Item>
    </Accordion>
  ) : (
    <div />
  );
}

export default RenderCustomFields;
