import {
  Group, Loader, UnstyledButton, useMantineTheme,
} from '@mantine/core';
import { UseInfiniteQueryResult } from '@tanstack/react-query';
import React, { useState } from 'react';
import RenderPaymentsList from '../payments-transactions-list';
import useTranslation from 'next-translate/useTranslation';
import { EmptyData } from '@/components/common/empty-data';

interface Props {
  dataQueryProps: UseInfiniteQueryResult;
  dataType: 'payment' | 'transfer' | 'all';
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  mixData: any[] | null;
  userAccountId: string;
}
function TabDataContents({
  dataQueryProps,
  dataType,
  mixData,
  userAccountId,
}: Props) {
  const {
    data, fetchNextPage, hasNextPage, isFetchingNextPage, status,
  } = dataQueryProps;
  const theme = useMantineTheme();
  const { t } = useTranslation();
  const [keys, setKeys] = useState([1]);

  if (dataType === 'all' && mixData && mixData?.length === 0) {
    return <EmptyData message="" />;
  }
  if (mixData && mixData?.length > 0) {
    return (
      <RenderPaymentsList
        userAccountId={userAccountId}
        mixData={mixData}
        dataPayment={undefined}
        dataTransfer={undefined}
      />
    );
  }
  return (
    <div>
      {status === 'loading' ? (
        <Group position="center">
          <Loader />
        </Group>
      ) : (
        <>
          {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            data?.pages?.map((i: any, index: number) => (i?.data?.length === 0 ? (
              <EmptyData message="" key={keys[index]} />
            ) : (
              <RenderPaymentsList
                mixData={null}
                userAccountId={userAccountId}
                key={keys[index]}
                dataPayment={dataType === 'payment' ? i?.data : undefined}
                dataTransfer={dataType === 'transfer' ? i?.data : undefined}
              />
            )))
          }
          <Group my="md" position="center">
            {isFetchingNextPage ? (
              <Loader />
            ) : (
              <UnstyledButton
                sx={{
                  display: !hasNextPage ? 'none' : '',
                  color: theme.colors.primary[5],
                }}
                onClick={() => {
                  fetchNextPage();
                  keys.push(keys[keys.length - 1] + 1);
                  setKeys(keys);
                }}
              >
                {t('common:showMore')}
              </UnstyledButton>
            )}
          </Group>
        </>
      )}
    </div>
  );
}

export default TabDataContents;
