import { Box } from '@mantine/core';
import { QRCode } from 'react-qrcode-logo';
import { QR_CONFIG, QRSizeConfig } from '../qr-constants';

interface QRCodeDisplayProps {
  value: string;
  sizes: QRSizeConfig;
  qrWrapperRef: React.RefObject<HTMLDivElement>;
}

export function QRCodeDisplay({
  value, sizes, qrWrapperRef,
}: QRCodeDisplayProps) {
  return (
    <Box
      ref={qrWrapperRef}
      sx={{
        width: sizes.qrWrapperSize,
        maxWidth: '85vw',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#ffffff',
        borderRadius: 20,
        boxShadow: '0 8px 32px rgba(0,0,0,0.15)',
        padding: 20,
        flexShrink: 0,
        border: '1px solid #e9ecef',
        gap: 15,
      }}
    >
      <QRCode
        value={value}
        size={Math.min(sizes.qrSize, window.innerWidth * 0.65, window.innerHeight * 0.35)}
        logoImage={QR_CONFIG.LOGO_URL}
        logoWidth={sizes.logoSize}
        logoHeight={sizes.logoSize}
        removeQrCodeBehindLogo
        bgColor="#ffffff"
        fgColor="#000000"
        qrStyle="dots"
        eyeRadius={50}
        quietZone={20}
        ecLevel="H"
        style={{ borderRadius: 16 }}
        logoPaddingStyle="circle"
        logoOpacity={1}
        enableCORS
      />
    </Box>
  );
}
