import { LocaleCookie } from '@/data';
import { Menu } from '@mantine/core';
import { setCookie } from 'cookies-next';
import useTranslation from 'next-translate/useTranslation';
import { useRouter } from 'next/router';
import React, { ReactNode } from 'react';

const languagesList = [
  {
    lang: 'en',
    title: 'english',
  },
  {
    lang: 'ar',
    title: 'arabic',
  },
];

interface Props {
  button: ReactNode;
  setMenuOpened: ((v: boolean) => void) | undefined;
}
function LanguagesMenu({ setMenuOpened, button }: Props) {
  const { t } = useTranslation();
  const router = useRouter();
  const { pathname, asPath, query } = router;
  return (
    <Menu>
      <Menu.Target>{button}</Menu.Target>
      <Menu.Dropdown>
        {languagesList?.map((i) => (
          <Menu.Item
            key={i?.lang}
            onClick={() => {
              setCookie(LocaleCookie, i?.lang);
              router.push({ pathname, query }, asPath, {
                locale: i?.lang,
              });
              if (setMenuOpened) setMenuOpened(false);
            }}
          >
            {t(`common:${i?.title}`)}
          </Menu.Item>
        ))}
      </Menu.Dropdown>
    </Menu>
  );
}

export default LanguagesMenu;
