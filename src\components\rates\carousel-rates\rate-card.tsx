import {
  Group, Paper, Text, Stack, Image,
} from '@mantine/core';
import currency from 'currency.js';

interface RateCardProps {
  title: string;
  icon: string;
  priceUsd: number;
  currencySymbol: string;
  precision: number;
}

export function RateCard({
  icon,
  title,
  priceUsd,
  currencySymbol,
  precision,
}: RateCardProps) {
  return (
    <Paper withBorder px="md" py="xs" radius="xl" key={title}>
      <Group position="apart" spacing={3}>
        <Group align="center" spacing={3}>
          <Image
            src={icon}
            height={30}
            width={30}
            radius={50}
            alt="payment-method"
          />
          <Text weight={500} tt="uppercase" size="xs" color="dimmed">
            {title}
          </Text>
        </Group>
        <Stack>
          <Group align="center" spacing={2}>
            <Text color="dimmed">{currencySymbol}</Text>
            <Text>
              {currency(priceUsd, { symbol: '', precision }).format()}
            </Text>
          </Group>
        </Stack>
        {/* <Stack>
          <Group align="center" spacing={2}>
            <Text color="dimmed">
              SYP
            </Text>
            <Text>
              {currency(priceSyp, { symbol: '' }).format()}
            </Text>
          </Group>
        </Stack> */}
      </Group>
    </Paper>
  );
}
