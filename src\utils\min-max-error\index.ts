// to check amount if it between min and max amount and if it less the balance
// return true if one from the tree conditions return true
// return false if all tree conditions return false

interface Props {
  amount: number | string;
  minAmount: number;
  maxAmount: number | null;
  currencyAmount: number | undefined;
}
export const minMaxError = ({
  amount,
  maxAmount,
  minAmount,
  currencyAmount,
}: Props) => {
  let isError = false;
  if (typeof amount === 'number' && amount > 0) {
    if (minAmount && amount < minAmount) {
      isError = true;
    } else if (maxAmount && amount > maxAmount) {
      isError = true;
    } else if (currencyAmount !== undefined && amount > currencyAmount) isError = true;
    else isError = false;
  }
  return isError;
};
