/**
 * create feedback request schema
 */
import { Translate } from 'next-translate';
import { z } from 'zod';

const required = 'required';
export const createMerchantRequestFormSchema = (t: Translate) => z.object({
  name: z.string().min(1, { message: t(`errors:${required}`) }),
  email: z
    .string()
    .min(1, { message: t(`errors:${required}`) })
    .email({ message: t('errors:invalidEmail') }),
  firstName: z.string().min(1, { message: t(`errors:${required}`) }),
  lastName: z.string().min(1, { message: t(`errors:${required}`) }),
  code: z.string().min(1, { message: t(`errors:${required}`) }),
  phone: z
    .string()
    .min(6, { message: t(`errors:${required}`) })
    .regex(/^\d*$/, t('errors:pleaseEnterValidNumbers')),
  telegramOrWhatsapp: z.string().min(1, { message: t(`errors:${required}`) }),
  websiteOrBot: z.string().min(1, { message: t(`errors:${required}`) }),
  socialMediaLink: z.string().min(1, { message: t(`errors:${required}`) }),
  message: z
    .string()
    .min(1, { message: t(`errors:${required}`) })
    .max(500, { message: t('errors:maxFeedbackLength') }),
});

export const createMerchantRequestApiRequestSchema = z.object({
  name: z.string(),
  email: z.string(),
  firstName: z.string(),
  lastName: z.string(),
  message: z.string(),
  code: z.string(),
  phone: z.string().regex(/^\d*$/, 'Please enter valid numbers'),
  telegramOrWhatsapp: z.string(),
  websiteOrBot: z.string(),
  socialMediaLink: z.string(),
  token: z.string().optional(),
});

export const createMerchantRequestBackendRequestSchema = createMerchantRequestApiRequestSchema.transform((data) => ({
  firstname: {
    rich_text: [
      {
        text: { content: data.firstName },
      },
    ],
  },
  phone: {
    rich_text: [
      {
        text: { content: data.phone },
      },
    ],
  },

  lastname: {
    rich_text: [
      {
        text: { content: data.lastName },
      },
    ],
  },
  message: {
    rich_text: [
      {
        text: { content: data.message },
      },
    ],
  },
  websiteOrBot: {
    rich_text: [
      {
        text: { content: data.websiteOrBot },
      },
    ],
  },

  socialMediaLink: {
    rich_text: [
      {
        text: { content: data.socialMediaLink },
      },
    ],
  },
  telegramOrWhatsapp: {
    rich_text: [
      {
        text: { content: data.telegramOrWhatsapp },
      },
    ],
  },
  email: {
    email: data.email,
  },
  name: {
    title: [
      {
        text: { content: data.name },
      },
    ],
  },
}));
