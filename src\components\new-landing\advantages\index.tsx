import { Icon } from '@/components/common/icon';
import {
  Container,
  SimpleGrid,
  Stack,
  Text,
  useMantineTheme,
} from '@mantine/core';

import useTranslation from 'next-translate/useTranslation';
import { useRouter } from 'next/router';

export default function Advantages() {
  const theme = useMantineTheme();
  const { t } = useTranslation();
  const router = useRouter();
  return (
    <div style={{ paddingTop: 40 }} id="features-section">
      <Container
        py={40}
        px={{
          xs: 20,
          sm: 30,
          lg: 40,
          base: 20,
        }}
        size={1200}
      >
        <Text
          ff={router.locale === 'ar' ? theme.fontFamily : `BauhausC, ${theme.fontFamily}`}
          mb={80}
          ta="center"
          weight="bold"
          size={43}
          color="white"
        >
          {t('common:advantagesLandingTitle')}
          {' '}
          <Text
            component="span"
            inherit
            variant="gradient"
            gradient={{ from: 'gray', to: 'green' }}
          >
            {t('common:kazawallet')}
          </Text>
          {' '}
        </Text>

        <SimpleGrid
          cols={3}
          my="lg"
          spacing="lg"
          breakpoints={[
            { maxWidth: 'md', cols: 2, spacing: 'md' },
            { maxWidth: 'xs', cols: 1, spacing: 'sm' },
          ]}
        >
          <Stack>
            <Icon icon="shield-lock" size={50} color={theme.colors.teal[6]} />
            <div>
              <Text mt="md" color="white" weight="bold" size="xl">
                {t('common:advantagesLandingFeat1Title')}
              </Text>
              <Text mt="md" color="white" weight="bold" size="lg">
                {t('common:advantagesLandingFeat1SubTitle')}
              </Text>
            </div>
            <Text color="dimmed" size="lg" weight={500}>
              {t('common:advantagesLandingFeat1Description')}
            </Text>
          </Stack>
          <Stack>
            <Icon icon="brand-speedtest" size={50} color={theme.colors.teal[6]} />
            <div>
              <Text mt="md" color="white" weight="bold" size="xl">
                {t('common:advantagesLandingFeat2Title')}
              </Text>
              <Text mt="md" color="white" weight="bold" size="lg">
                {t('common:advantagesLandingFeat2SubTitle')}
              </Text>
            </div>
            <Text color="dimmed" size="lg" weight={500}>
              {t('common:advantagesLandingFeat2Description')}
            </Text>
          </Stack>
          <Stack>
            <Icon icon="shield-dollar" size={50} color={theme.colors.teal[6]} />
            <div>
              <Text mt="md" color="white" weight="bold" size="xl">
                {t('common:advantagesLandingFeat3Title')}
              </Text>
              <Text mt="md" color="white" weight="bold" size="lg">
                {t('common:advantagesLandingFeat3SubTitle')}
              </Text>
            </div>
            <Text color="dimmed" size="lg" weight={500}>
              {t('common:advantagesLandingFeat3Description')}
            </Text>
          </Stack>
          <Stack>
            <Icon icon="info-octagon" size={50} color={theme.colors.teal[6]} />
            <div>
              <Text mt="md" color="white" weight="bold" size="xl">
                {t('common:advantagesLandingFeat4Title')}
              </Text>
              <Text mt="md" color="white" weight="bold" size="lg">
                {t('common:advantagesLandingFeat4SubTitle')}
              </Text>
            </div>
            <Text color="dimmed" size="lg" weight={500}>
              {t('common:advantagesLandingFeat4Description')}
            </Text>
          </Stack>
          <Stack>
            <Icon icon="world" size={50} color={theme.colors.teal[6]} />
            <div>
              <Text mt="md" color="white" weight="bold" size="xl">
                {t('common:advantagesLandingFeat5Title')}
              </Text>
              <Text mt="md" color="white" weight="bold" size="lg">
                {t('common:advantagesLandingFeat5SubTitle')}
              </Text>
            </div>
            <Text color="dimmed" size="lg" weight={500}>
              {t('common:advantagesLandingFeat5Description')}
            </Text>
          </Stack>
          <Stack>
            <Icon icon="wallet" size={50} color={theme.colors.teal[6]} />
            <div>
              <Text mt="md" color="white" weight="bold" size="xl">
                {t('common:advantagesLandingFeat6Title')}
              </Text>
              <Text mt="md" color="white" weight="bold" size="lg">
                {t('common:advantagesLandingFeat6SubTitle')}
              </Text>
            </div>
            <Text color="dimmed" size="lg" weight={500}>
              {t('common:advantagesLandingFeat6Description')}
            </Text>
          </Stack>
        </SimpleGrid>
      </Container>
    </div>
  );
}
