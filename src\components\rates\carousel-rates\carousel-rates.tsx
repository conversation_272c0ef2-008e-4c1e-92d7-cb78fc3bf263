import { Carousel } from '@mantine/carousel';
import { useRef } from 'react';
import Autoplay from 'embla-carousel-autoplay';
import { RateCard } from './rate-card';
import { useStyles } from './style';
import { useQuery } from '@tanstack/react-query';
import {
  PaymentMethodsApiResponse,
  getPaymentMethodsQuery,
} from '@/store/payment-methods';
import { RatesApiResponse } from '@/store/rate';
import { RateCardSkeleton } from './rate-card-skeleton';
import { FeesRatesCalculate } from '@/utils/fees-functions/fees-rate';
import { getTranslatedTextValue } from '@/utils';
import { useRouter } from 'next/router';

function RenderItems({
  data,
  rates,
}: {
  data: PaymentMethodsApiResponse['data'][0]['depositCurrencies'];
  rates: RatesApiResponse;
}) {
  const { classes } = useStyles();
  const { locale } = useRouter();
  const autoplay = useRef(Autoplay({ delay: 3000 }));

  return (
    <Carousel
      mt="md"
      classNames={classes}
      slideSize="28%"
      slideGap="md"
      loop
      align="start"
      plugins={[autoplay.current]}
      slidesToScroll={1}
      breakpoints={[
        { maxWidth: 'xl', slideSize: '30%', slideGap: 'sm' },
        { maxWidth: 'lg', slideSize: '32%', slideGap: 'sm' },
        { maxWidth: 'md', slideSize: '37%', slideGap: 'sm' },
        { maxWidth: 'sm', slideSize: '50%', slideGap: 'sm' },
        { maxWidth: 'xs', slideSize: '100%', slideGap: 'sm' },
      ]}
    >
      {rates
        && data?.map((i) => (
          <Carousel.Slide key={i?.id}>
            <RateCard
              precision={i?.precision ?? 2}
              title={getTranslatedTextValue(
                locale,
                i?.paymentMethodTitle,
                i?.paymentMethodTitleAr,
              )}
              icon={i?.paymentMethodIcon ?? ''}
              priceUsd={FeesRatesCalculate(
                i?.PaymentMethodFeesPercentage,
                rates?.rates && rates?.rates[`${i.code}_FROM`] ? rates?.rates[`${i.code}_FROM`] : 0,
              )}
              currencySymbol={i?.symbol ?? ''}
            />
          </Carousel.Slide>
        ))}
    </Carousel>
  );
}

export default function CarouselRates({
  ratesData,
  isLoadingRates,
}: {
  ratesData: RatesApiResponse;
  isLoadingRates: boolean;
}) {
  const { classes } = useStyles();
  const autoplay = useRef(Autoplay({ delay: 3000 }));
  const { data, isLoading } = useQuery(
    getPaymentMethodsQuery({
      populate: {
        depositCurrencies: true,
      },
      filters: {
        showInRatesBar: true,
      },
    }),
  );
  const allPaymentMethods = () => {
    let allCurrencies: PaymentMethodsApiResponse['data'][0]['depositCurrencies'] = [];
    data?.data?.map((i: PaymentMethodsApiResponse['data'][0]) => {
      allCurrencies = [...allCurrencies, ...i.depositCurrencies];
      return allCurrencies;
    });
    return allCurrencies;
  };

  return (
    <div>
      <Carousel
        mt="md"
        classNames={classes}
        slideSize="28%"
        slideGap="md"
        loop
        align="start"
        plugins={[autoplay.current]}
        slidesToScroll={1}
        breakpoints={[
          { maxWidth: 'xl', slideSize: '30%', slideGap: 'sm' },
          { maxWidth: 'lg', slideSize: '32%', slideGap: 'sm' },
          { maxWidth: 'md', slideSize: '37%', slideGap: 'sm' },
          { maxWidth: 'sm', slideSize: '50%', slideGap: 'sm' },
          { maxWidth: 'xs', slideSize: '100%', slideGap: 'sm' },
        ]}
      >
        {(isLoading || isLoadingRates) && (
          <>
            <Carousel.Slide key={1}>
              <RateCardSkeleton />
            </Carousel.Slide>
            <Carousel.Slide key={2}>
              <RateCardSkeleton />
            </Carousel.Slide>
            <Carousel.Slide key={3}>
              <RateCardSkeleton />
            </Carousel.Slide>
            <Carousel.Slide key={4}>
              <RateCardSkeleton />
            </Carousel.Slide>
            <Carousel.Slide key={5}>
              <RateCardSkeleton />
            </Carousel.Slide>
          </>
        )}
      </Carousel>
      <RenderItems data={allPaymentMethods()} rates={ratesData} />
    </div>
  );
}
