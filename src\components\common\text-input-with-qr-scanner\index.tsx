/* eslint-disable react/require-default-props */
import {
  TextInput, ActionIcon, Tooltip, useMantineTheme,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import useTranslation from 'next-translate/useTranslation';
import { Icon } from '@/components/common/icon';
import { QRModal } from '@/components/common/qr-modal';
import { DetectedBarcode } from '@/components/common/qr-modal/qr-helpers';
import { validateQRData, getQRValidationErrorMessage } from '@/utils/qr-validation';

interface TextInputWithQRScannerProps {
  label?: React.ReactNode;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void | Promise<void>;
  onScanComplete?: (scannedValue: string) => void;
  onValidationError?: (error: string) => void;
  error?: React.ReactNode;
  disabled?: boolean;
  radius?: string;
  size?: string;
  accountId?: string;
  currentUserAccountId?: string;
  allowSelfTransfer?: boolean;
  showSuccessAlert?: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}

export function TextInputWithQRScanner({
  label,
  placeholder,
  value,
  onChange,
  onScanComplete,
  onValidationError,
  error,
  disabled,
  radius = 'lg',
  size = 'sm',
  accountId,
  ...otherProps
}: TextInputWithQRScannerProps) {
  const { t } = useTranslation();
  const theme = useMantineTheme();
  const [opened, { open: openQrModal, close: closeQrModal }] = useDisclosure(false);

  // Handle successful QR scan with validation
  const handleQRScanComplete = async (codes: DetectedBarcode[]) => {
    if (!codes || codes.length === 0) {
      throw new Error(t('errors:invalidScanResult'));
    }
    const scannedData = codes[0].rawValue;
    // Validate the QR data
    const validationResult = validateQRData(scannedData);

    if (!validationResult.isValid) {
      const errorMsg = getQRValidationErrorMessage(
        validationResult.errorKey || 'errors:invalidQrCodeFormatInvalidUrl',
        t,
      );
      throw new Error(errorMsg);
    }

    const accountIdValue = validationResult.accountId!;

    // Update the input value
    if (onChange) {
      await Promise.resolve(onChange(accountIdValue));
    }
    if (onScanComplete) {
      onScanComplete(accountIdValue);
    }
    closeQrModal();
  };

  const handleScanError = (errorParam: unknown) => {
    // Let the modal handle the error display
    // Only call onValidationError for external handling if needed
    if (onValidationError) {
      const errorMsg = errorParam instanceof Error
        ? errorParam.message
        : t('errors:unknownScannerError');
      onValidationError(errorMsg);
    }
  };

  const handleClose = () => {
    closeQrModal();
  };

  return (
    <>
      <TextInput
        label={label}
        placeholder={placeholder}
        value={value}
        onChange={(event) => onChange?.(event.currentTarget.value)}
        error={error}
        disabled={disabled}
        radius={radius}
        size={size}
        rightSection={
          !disabled && (
            <Tooltip label={t('common:scanQRCode')} position="top">
              <ActionIcon
                onClick={openQrModal}
                size="lg"
                variant="subtle"
                color="gray"
                sx={() => ({
                  '&:hover': {
                    backgroundColor: theme.colorScheme === 'dark'
                      ? theme.fn.rgba(theme.colors.dark[5], 0.5)
                      : theme.fn.rgba(theme.colors.gray[2], 0.5),
                  },
                })}
              >
                <Icon icon="scan" size="1.2rem" color="gray" />
              </ActionIcon>
            </Tooltip>
          )
        }
        {...otherProps}
      />

      <QRModal
        opened={opened}
        onClose={handleClose}
        accountId={accountId}
        mode="both"
        onScanComplete={handleQRScanComplete}
        onScanError={handleScanError}
        showGenerateButton={!!accountId}
        showCloseButton
      />
    </>
  );
}
