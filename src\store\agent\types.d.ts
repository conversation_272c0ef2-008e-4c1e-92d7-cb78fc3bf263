import { z } from 'zod';
import {
  createAgentPaymentApiRequestSchema,
  createAgentPaymentBackendRequestSchema,
  createAgentRequestApiRequestSchema,
} from './request-transformer';
import { agentPaymentApiResponseSchema } from './responses-transformers';

export type CreateAgentPaymentBackendRequest = z.infer<
  typeof createAgentPaymentBackendRequestSchema
>;
export type CreateAgentPaymentApiRequest = z.infer<
  typeof createAgentPaymentApiRequestSchema
>;

export type AgentPaymentApiResponse = z.infer<
  typeof agentPaymentApiResponseSchema
>;

export type CreateAgentRequestApiRequest = z.infer<
  typeof createAgentRequestApiRequestSchema
>;
