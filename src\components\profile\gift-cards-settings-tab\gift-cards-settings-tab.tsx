import { CreateGiftCardForm } from '@/components/gift-cards/create-gift-card-form';
import { ListGifts } from '@/components/gift-cards/list-gifts';
import { RedeemGiftCardForm } from '@/components/gift-cards/redeem-gift-card-form';
import { getUserQuery } from '@/store/user';
import { Box, Stack, Text } from '@mantine/core';
import { useQuery } from '@tanstack/react-query';
import useTranslation from 'next-translate/useTranslation';
import React from 'react';

interface GiftCardsSettingsTabProps {
  isMerchant: boolean;
}
function GiftCardsSettingsTab({ isMerchant }: GiftCardsSettingsTabProps) {
  const { t } = useTranslation();
  const { data } = useQuery({ ...getUserQuery({}), enabled: false });
  return (
    <Stack spacing={0} align="stretch" m="auto" w="100%" maw={650}>
      <Box>
        <Text size="lg" mb="md">
          {t('common:redeemGiftCard')}
        </Text>
        <RedeemGiftCardForm />
      </Box>
      {isMerchant && (
        <>
          <Box mt={20}>
            <Text size="lg" mb="md">
              {t('common:createGiftCard')}
            </Text>
            <CreateGiftCardForm userData={data} />
          </Box>
          <Box my={20}>
            <Text size="lg" mb="md">
              {t('common:giftsList')}
            </Text>
            <ListGifts />
          </Box>
        </>
      )}
    </Stack>
  );
}

export default GiftCardsSettingsTab;
