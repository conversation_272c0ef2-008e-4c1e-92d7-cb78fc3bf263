// InstallButton.js

import { usePwa } from '@/store/pwa/store';
import { Box } from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';
import React from 'react';

function InstallPWA() {
  const { t } = useTranslation();
  const deferredPrompt = usePwa((state) => state.deferredPrompt);
  const setDeferredPrompt = usePwa((state) => state.setDeferredPrompt);

  const handleInstallClick = () => {
    if (deferredPrompt) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const promptEvent = deferredPrompt as any;
      promptEvent.prompt();
      promptEvent.userChoice.then(() => {
        // The PWA installation process has completed
        setDeferredPrompt(null);
      });
    }
  };

  return (
    <Box onClick={handleInstallClick} w="100%">
      {t('common:installPwa')}
    </Box>
  );
}

export default InstallPWA;
