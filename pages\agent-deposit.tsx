/**
 * This component renders a deposit page for agent role.
 *
 * @description
 * Tn this page we have a form to create payment type "deposit"
 */
import classes from './style.module.scss';
import MetaTags from '@/components/common/meta-tags';
import { Layout } from '@/components/layout/layout';
import {
  Group,
  Paper,
  SimpleGrid,
  Stack,
  Text,
  TextInput,
  useMantineTheme,
} from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';
import React, { useEffect, useState } from 'react';
import { PageTitle } from '@/components/common/page-title';
import { useMutation, useQuery } from '@tanstack/react-query';
import {
  AgentPaymentApiResponse,
  getAgentPaymentMutation,
} from '@/store/agent';
import { AgentPaymentDetails } from '@/components/agents/agent-payment-details';
import { AgentPaymentForm } from '@/components/agents/agent-payment-form';
import { FeesTransferCalculate } from '@/utils/fees-functions/fees-transfer-withdraw-deposit';
import { useForm, zodResolver } from '@mantine/form';
import { z } from 'zod';

import { useRouter } from 'next/router';
import SubmitButton from '@/components/common/submit-button';
import { useCaptcha } from '@/hooks';
import { Captcha } from '@/components/common/captcha';
import { getUserQuery } from '@/store/user';

function AgentDeposit() {
  const { t } = useTranslation();
  const { colorScheme, colors } = useMantineTheme();
  const { locale, replace } = useRouter();
  const { execute, reCaptchaRef, reset } = useCaptcha();
  const { data: userData } = useQuery({ ...getUserQuery({}), enabled: false });
  useEffect(() => {
    if (userData && userData?.role !== 'agent') {
      replace('404');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userData]);

  const schema = z.object({
    uid: z.string().regex(/^\d{7}$/, t('errors:pleaseEnterValidNumbers')),
  });

  const [data, setData] = useState<AgentPaymentApiResponse>();

  const { mutate, isLoading } = useMutation({
    ...getAgentPaymentMutation(),
    onSuccess(res) {
      setData(res);
      reset();
    },
    onError() {
      reset();
    },
  });

  const form = useForm({
    initialValues: {
      uid: '',
    },
    validate: zodResolver(schema),
  });
  const submit = ({ uid }: { uid: string }) => {
    if (reCaptchaRef.current) {
      execute((token) => mutate({
        uid: 'wi-'.concat(uid),
        chaKey: token,
      }));
    } else {
      mutate({
        uid: 'wi-'.concat(uid),
      });
    }
  };
  // return fees after calculate it
  const fees = data
    ? FeesTransferCalculate(
      data.paymentMethod?.depositFeesFixed,
      data.paymentMethod?.depositFeesPercentage,
      data.paymentMethod?.depositFeesMin,
      data.paymentMethod?.depositFeesMax,
      data.actualAmount,
    )
    : 0;

  // to be deposit amount
  const toBeDeposit = (data?.actualAmount ?? 0) + fees;

  // action to handle uid value after deposit complete and closing popup success
  const onSuccess = () => {
    form.reset();
  };

  useEffect(() => {
    setData(undefined);
  }, [form.values.uid]);

  const hasCorrectUidValue = form.isValid();

  const isRtl = locale === 'ar';

  const textColor = colorScheme === 'dark' ? colors.dark[0] : colors.dark[9];

  return (
    <div>
      <MetaTags title={t('common:agentDeposit')} />
      <Layout>
        <PageTitle title="agentDeposit" />
        <Stack spacing="md" align="center" maw={900} mx="auto">
          <Paper w="100%" withBorder p="md" radius={20}>
            <form onSubmit={form.onSubmit(submit)}>
              <Group position="apart" align="end">
                <TextInput
                  className={classes.agentDepositInput}
                  rightSection={isRtl && <Text color={textColor}>-wi</Text>}
                  icon={!isRtl && <Text color={textColor}>wi-</Text>}
                  label={t('common:enterOperationNo')}
                  placeholder="xxxxxxx"
                  radius="lg"
                  miw={300}
                  w={{ md: '73%', xs: '66%', base: '100%' }}
                  {...form.getInputProps('uid')}
                />
                <SubmitButton
                  disabled={!hasCorrectUidValue}
                  loading={isLoading}
                  w={{ md: '25%', xs: '30%', base: '100%' }}
                  radius="lg"
                  type="submit"
                >
                  {t('common:showInfo')}
                </SubmitButton>
              </Group>
            </form>
          </Paper>

          <SimpleGrid
            w="100%"
            cols={2}
            spacing="lg"
            breakpoints={[
              { maxWidth: 'md', cols: 2, spacing: 'sm' },
              { maxWidth: 'sm', cols: 1, spacing: 'sm' },
            ]}
          >
            {hasCorrectUidValue && data && (
              <AgentPaymentDetails
                fees={fees}
                toBeDeposit={toBeDeposit}
                agentPaymentData={data}
              />
            )}
            {data && (
              <AgentPaymentForm
                transactionId={data?.transactionId ?? ''}
                onSuccess={onSuccess}
                justSuccessPopupDisplaying={!hasCorrectUidValue}
                fees={fees}
                toBeDeposit={toBeDeposit}
                agentPayment={data}
              />
            )}
          </SimpleGrid>
        </Stack>
      </Layout>
      <Captcha reCaptchaRef={reCaptchaRef} />
    </div>
  );
}

export default AgentDeposit;
export async function getServerSideProps() {
  return { props: {} };
}
