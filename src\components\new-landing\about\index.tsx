import { assetBaseUrl } from '@/data';
import {
  Container,
  Text,
  SimpleGrid,
  Stack,
  Avatar,
  Image,
  useMantineTheme,
} from '@mantine/core';

import useTranslation from 'next-translate/useTranslation';
import { useRouter } from 'next/router';

export default function About() {
  const { t } = useTranslation();
  const theme = useMantineTheme();
  const router = useRouter();

  return (
    <Container p={40} size={1200}>
      <SimpleGrid
        cols={2}
        breakpoints={[
          { maxWidth: 'md', cols: 2, spacing: 'md' },
          { maxWidth: 'sm', cols: 1, spacing: 'sm' },
          { maxWidth: 'xs', cols: 1, spacing: 'sm' },
        ]}
      >
        <Stack justify="center">
          <Text
            ff={router.locale === 'ar' ? theme.fontFamily : `BauhausC, ${theme.fontFamily}`}
            size={40}
            weight="bold"
            color="white"
          >
            {t('common:aboutLandingTitle')}
          </Text>
          <Text my="xl" color="dimmed" size="lg" weight={500} maw={600}>
            {t('common:aboutLandingDescription')}
          </Text>
          <Avatar.Group spacing={5}>
            <Image
              alt="aed-currenncy"
              width={40}
              height={40}
              radius={40}
              src={`${assetBaseUrl}/assets/new-landing/currencies/aed.png`}
            />
            <Image
              alt="syp-currenncy"
              ml={-5}
              width={40}
              height={40}
              radius={40}
              src={`${assetBaseUrl}/assets/new-landing/currencies/syp.png`}
            />
            <Image
              alt="usd-currenncy"
              ml={-5}
              width={40}
              height={40}
              radius={40}
              src={`${assetBaseUrl}/assets/new-landing/currencies/usd.png`}
            />
            <Image
              alt="eur-currenncy"
              ml={-5}
              width={40}
              height={40}
              radius={40}
              src={`${assetBaseUrl}/assets/new-landing/currencies/eur.png`}
            />
            <Image
              alt="btc-currenncy"
              ml={-5}
              width={40}
              height={40}
              radius={40}
              src={`${assetBaseUrl}/assets/new-landing/currencies/btc.png`}
            />
            <Avatar
              sx={{
                backgroundColor: '#000D23',
                border: '2px solid #1C3A56',
                color: '#569245',
                '.mantine-ltr-Avatar-placeholder , .mantine-rtl-Avatar-placeholder':
                  {
                    backgroundColor: '#000D23',
                  },
              }}
              size={40}
              radius={40}
              alt="number"
            >
              +5
            </Avatar>
          </Avatar.Group>
        </Stack>
        <Stack align="end" justify="center" h="100%">
          <Image
            style={{
              // maxWidth: videoWidth(),
              margin: 'auto',
              borderRadius: 15,
              marginTop: 60,
            }}
            src={`${assetBaseUrl}/assets/new-landing/kazawallet.webp`}
          />
        </Stack>
      </SimpleGrid>
    </Container>
  );
}
