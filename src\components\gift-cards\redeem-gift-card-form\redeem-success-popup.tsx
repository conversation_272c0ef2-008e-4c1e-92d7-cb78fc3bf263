import { ROUTES } from '@/data';
import { CurrenciesApiResponse } from '@/store/currencies';
import {
  Box,
  Button,
  Group,
  Modal,
  Text,
  useMantineTheme,
} from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import { IconCircleCheck } from '@tabler/icons-react';
import currency from 'currency.js';

import useTranslation from 'next-translate/useTranslation';
import Link from 'next/link';
import React from 'react';

interface RedeemSuccessPopupProps {
  opened: boolean;
  setOpened: (v: boolean) => void;
  giftCard: {
    amount: number;
    currency: CurrenciesApiResponse['data'][0];
  };
}
function RedeemSuccessPopup(props: RedeemSuccessPopupProps) {
  const { opened, setOpened, giftCard } = props;
  const { t } = useTranslation();
  const close = () => setOpened(false);
  const theme = useMantineTheme();

  const isMobileScreen = useMediaQuery(
    `(max-width: ${theme.breakpoints.sm})`,
    true,
    {
      getInitialValueInEffect: false,
    },
  );

  return (
    <Modal radius="lg" centered opened={opened} onClose={close}>
      <Box mb="md">
        <Group position="center">
          <IconCircleCheck size={48} color="green" />
        </Group>
        <Text fw={700} size="lg" ta="center">
          {t('common:success')}
        </Text>
      </Box>
      <Text ta="center">
        {t('common:giftCardRedeemSuccessfully', {
          value:
            `${currency(giftCard?.amount, {
              precision: giftCard?.currency?.precision ?? 0,
              symbol: '',
            })
            } ${
              giftCard?.currency?.symbol ?? ''}`,
        })}
      </Text>

      <Group mt="lg" position="center">
        <Button
          w={isMobileScreen ? '100%' : '48%'}
          radius="lg"
          variant="filled"
          component={Link}
          href={`${ROUTES.wallets.path}/${giftCard?.currency?.code}`}
        >
          {t('common:goToWallet')}
        </Button>
        <Button
          w={isMobileScreen ? '100%' : '48%'}
          radius="lg"
          variant="outline"
          onClick={close}
        >
          {t('common:redeemNewGiftCard')}
        </Button>
      </Group>
    </Modal>
  );
}

export default RedeemSuccessPopup;
