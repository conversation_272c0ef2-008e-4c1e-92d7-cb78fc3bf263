/* eslint-disable sonarjs/no-duplicate-string */
/* eslint-disable no-nested-ternary */
/* eslint-disable max-len */
import {
  Box, Group, Text, Tooltip, Stack, useMantineTheme,
} from '@mantine/core';
import { Icon<PERSON>ey, IconCreditCard, IconCheck } from '@tabler/icons-react';
import { useState } from 'react';
import useTranslation from 'next-translate/useTranslation';
import { useFlipStyles } from './style';

type Card = {
  cardNumber: string;
  cardHolderName: string;
  expiryDate: string;
  cvv: string;
  cardType: string;
  brand: string;
};

type Props = {
  card: Card;
  flipped: boolean;
  showCredentialsOnCard: boolean;
  credentialsTimer: number;
  onToggle: () => void;
  onViewCredentials: () => void;
};

// eslint-disable-next-line sonarjs/cognitive-complexity
export function FlipPaymentCard({
  card, flipped, showCredentialsOnCard, credentialsTimer, onToggle, onViewCredentials,
}: Props) {
  const { t } = useTranslation();
  const { classes, cx } = useFlipStyles();
  const theme = useMantineTheme();
  const last4 = card.cardNumber.replace(/\s/g, '').slice(-4);
  const [copiedField, setCopiedField] = useState<string | null>(null);

  const handleCopyToClipboard = async (text: string, fieldName: string, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card flip
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(fieldName);
      setTimeout(() => {
        setCopiedField((current) => (current === fieldName ? null : current));
      }, 2000);
    } catch {
      // fail silently or show error toast
    }
  };

  return (
    <Box className={classes.root}>
      <Box
        role="button"
        tabIndex={0}
        aria-pressed={flipped}
        aria-label={flipped ? 'Hide card credentials' : 'View card credentials'}
        onClick={onToggle}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onToggle();
          }
        }}
        className={cx(classes.card, { [classes.flipped]: flipped })}
      >
        {/* FRONT (Default view - shows full credentials) */}
        <Box className={cx(classes.face, classes.front)}>
          <div>
            <Text className={classes.brand}>FLEXI</Text>
            <Text className={classes.sub}>Card credentials</Text>
          </div>

          <Stack spacing="xs" mb={20}>
            <div className={classes.row}>
              <Group spacing={8}>
                <IconCreditCard size={18} />
                <Text className={classes.mono}>
                  {' '}
                  •••• •••• ••••
                  {' '}
                  {last4}
                </Text>
              </Group>
            </div>
          </Stack>
        </Box>

        {/* BACK (When flipped - shows credentials or View Credentials button) */}
        <Box className={cx(classes.face, classes.back)}>
          <div>
            <Text className={classes.brand}>FLEXI</Text>
            <Text className={classes.sub}>{card.cardType}</Text>
          </div>

          {/* Show credentials when showCredentialsOnCard is true */}
          {showCredentialsOnCard ? (
            <Stack spacing="xs" mb={20}>
              <div className={classes.row}>
                <Group spacing={8}>
                  <IconCreditCard size={18} />
                  <Tooltip label={t('common:clickToCopyCardNumber')} withArrow>
                    <Text
                      className={classes.mono}
                      onClick={(e) => handleCopyToClipboard(card.cardNumber, 'cardNumber', e)}
                      style={{
                        cursor: 'pointer',
                        padding: '2px 4px',
                        borderRadius: '4px',
                        backgroundColor: copiedField === 'cardNumber'
                          ? (theme.colorScheme === 'dark' ? theme.colors.blue[9] : theme.colors.blue[5])
                          : 'transparent',
                        transition: 'background-color 0.2s',
                      }}
                    >
                      {card.cardNumber}
                      {copiedField === 'cardNumber' && (
                        <IconCheck size={14} style={{ marginLeft: '4px', display: 'inline' }} />
                      )}
                    </Text>
                  </Tooltip>
                </Group>
              </div>
              <div className={classes.row}>
                <Group spacing={8}>
                  <Tooltip label={t('common:clickToCopyExpiryDate')} withArrow>
                    <Text
                      className={classes.mono}
                      onClick={(e) => handleCopyToClipboard(card.expiryDate, 'expiryDate', e)}
                      style={{
                        cursor: 'pointer',
                        padding: '2px 4px',
                        borderRadius: '4px',
                        backgroundColor: copiedField === 'expiryDate'
                          ? (theme.colorScheme === 'dark' ? theme.colors.blue[9] : theme.colors.blue[5])
                          : 'transparent',
                        transition: 'background-color 0.2s',
                      }}
                    >
                      {t('common:exp')}
                      :
                      {' '}
                      {card.expiryDate}
                      {copiedField === 'expiryDate' && (
                        <IconCheck size={14} style={{ marginLeft: '4px', display: 'inline' }} />
                      )}
                    </Text>
                  </Tooltip>
                  <Text className={classes.mono}>|</Text>
                  <Tooltip label={t('common:clickToCopyCvv')} withArrow>
                    <Text
                      className={classes.mono}
                      onClick={(e) => handleCopyToClipboard(card.cvv, 'cvv', e)}
                      style={{
                        cursor: 'pointer',
                        padding: '2px 4px',
                        borderRadius: '4px',
                        backgroundColor: copiedField === 'cvv'
                          ? (theme.colorScheme === 'dark' ? theme.colors.blue[9] : theme.colors.blue[5])
                          : 'transparent',
                        transition: 'background-color 0.2s',
                      }}
                    >
                      {t('common:cvv')}
                      :
                      {' '}
                      {card.cvv}
                      {copiedField === 'cvv' && (
                        <IconCheck size={14} style={{ marginLeft: '4px', display: 'inline' }} />
                      )}
                    </Text>
                  </Tooltip>
                </Group>
              </div>
              <div className={classes.row}>
                <Text size="xs" color="dimmed">
                  {t('common:hidingIn')}
                  {' '}
                  {credentialsTimer}
                  {t('common:seconds')}
                </Text>
              </div>
            </Stack>
          ) : (
            /* Center hint bar - shows when flipped but credentials not shown */
            flipped && (
              <Tooltip label={t('common:clickToViewCredentials')} withArrow>
                <Box
                  className={classes.hintBar}
                  onClick={(e) => {
                    e.stopPropagation();
                    onViewCredentials();
                  }}
                >
                  <IconKey size={16} />
                  <Text>{t('common:viewCredentials')}</Text>
                </Box>
              </Tooltip>
            )
          )}
        </Box>
      </Box>
    </Box>
  );
}
