import React from 'react';
import {
  Box, Group, Text, useMantineTheme,
} from '@mantine/core';

export function CardVisual() {
  const theme = useMantineTheme();

  return (
    <Box
      sx={{
        width: '100%',
        height: 120,
        background: 'linear-gradient(135deg, #2c3e50 0%, #34495e 100%)',
        borderRadius: 12,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        padding: 16,
        color: 'white',
        position: 'relative',
        overflow: 'hidden',
        marginBottom: 16,
        [theme.fn.smallerThan('sm')]: {
          height: 100,
          padding: 12,
        },
      }}
    >
      {/* Background pattern */}
      <Box
        sx={{
          position: 'absolute',
          top: -20,
          right: -20,
          width: 80,
          height: 80,
          borderRadius: '50%',
          background: 'rgba(255, 255, 255, 0.1)',
          [theme.fn.smallerThan('sm')]: {
            width: 60,
            height: 60,
            top: -15,
            right: -15,
          },
        }}
      />

      <Box>
        <Text size="xs" weight={600} opacity={0.8}>
          FLEXI
        </Text>
      </Box>

      <Group position="apart" align="end">
        <Box>
          <Text size="xs" opacity={0.7}>
            •••• •••• •••• ••••
          </Text>
        </Box>
        <Text size="sm" weight={700}>
          VISA
        </Text>
      </Group>
    </Box>
  );
}
