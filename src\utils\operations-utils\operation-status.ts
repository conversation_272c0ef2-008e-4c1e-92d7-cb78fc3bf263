import { PaymentsApiResponse } from '@/store/payment';

export const statusCases = ({ status }:{status:PaymentsApiResponse['data'][0]['status']|undefined}) => {
  if (status === 'pending') return { color: 'blue', icon: 'dots' };
  if (status === 'approved') return { color: 'green', icon: 'check' };
  if (status === 'processing') return { color: 'yellow', icon: 'dots' };
  return { color: 'red', icon: 'x' };
};
