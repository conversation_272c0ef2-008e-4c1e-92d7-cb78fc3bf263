/**
 * This component renders a Mass Payout page.
 *
 * @description
 * This page used to create many transfers in one api call.
 * There are to sections the first one contain file input and currency select and button to download
 *  csv file to show the format of the file should user upload it to create many transfer.
 * After select the currency to transfer from it ,should upload the file and click "check file data" button to to call "check mass payout api" to check
 *  if transfers are ready to execute and display an error to user for each transfer if found before complete transfer.
 * User should fix the uploaded file items errors and upload file again and check file, when file items don't have errors then user can complete transfer.
 * After upload the file will display list of the items gets from the file and show the transfers details.
 * After complete the transfers will display same list items after get them from the response and display "success" and
 *  "failures" items with the reason of the failing.
 * To change the currency there is reset button ,on click it will reset the form and the user can select currency and upload new file.
 */
import MetaTags from '@/components/common/meta-tags';
import { Layout } from '@/components/layout/layout';
import {
  Alert, Paper, SimpleGrid, Stack, Text,
} from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';
import React, { useState } from 'react';

import ImportFileCard from '@/components/mass-payout/import-file-card';
import StepDescriptionCard from '@/components/mass-payout/step-description-card';

import { UserApiResponse, getUserQuery } from '@/store/user';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/router';

import MyBalanceSkeleton from '@/components/balance/my-balance/my-balance-skeleton';
import CurrencyTitleSkeleton from '@/components/balance/currency-title-skeleton';
import { MyBalance } from '@/components/balance/my-balance';
import { PageTitle } from '@/components/common/page-title';

import { MassPayoutApiResponse, TransferItemType } from '@/store/mass-payout';
import { TransferInlineList } from '@/components/mass-payout/transfer-inline-list';
import { SelectCurrencyCard } from '@/components/currency/select-currency-card';

function MassPayout() {
  const { t } = useTranslation();
  const { query } = useRouter();
  // stats data and errors
  const [fileDataChecked, setFileDataChecked] = useState<TransferItemType[]>(
    [],
  );
  const [disabled, setDisabled] = useState(false);
  const [selectedItem, setSelectedItem] = useState<UserApiResponse['currencies'][0]>();
  const [responseData, setResponseData] = useState<MassPayoutApiResponse['data']>();
  const [generalError, setGeneralError] = useState('');
  // Call get user api to get user currencies
  const { data, isLoading } = useQuery({
    ...getUserQuery({}),
    refetchOnWindowFocus: false,
    onSuccess(res) {
      // get user currencies.
      let currenciesItems = res?.currencies;
      // set "currenciesItems" is selected currency if user select another currency after filter user currencies
      //  by selectedItem.
      if (selectedItem) {
        currenciesItems = res?.currencies?.filter(
          (i) => i?.value === selectedItem?.value,
        );
        // set selected item after get it from user currencies response.
        setSelectedItem(currenciesItems[0]);
      }
    },
  });
  // Set initial currency select if there is a "curr" route query
  const initCurrency = query?.currency
    ? data?.currencies?.filter((i) => i.uid === query?.currency)
    : data?.currencies;
  // currency item
  const item = selectedItem ?? (initCurrency && initCurrency[0]);

  // render boolean to check if the checked items are ready to transfer ot there are errors
  const hasErrorInCheckedItems = () => {
    let hasError = false;
    // if check mass payout api return general error
    if (generalError) hasError = true;
    // if transfer items have error
    fileDataChecked.map((i) => {
      if (!i?.success) hasError = true;
      return hasError;
    });
    return hasError;
  };
  return (
    <div>
      <MetaTags title={t('common:massPayout')} />
      <Layout>
        <PageTitle title="massPayout" />
        <SimpleGrid
          my="md"
          cols={3}
          breakpoints={[
            { maxWidth: 'md', cols: 3, spacing: 'md' },
            { maxWidth: 'sm', cols: 1, spacing: 'sm' },
            { maxWidth: 'xs', cols: 1, spacing: 'sm' },
          ]}
        >
          <StepDescriptionCard
            title={t('common:firstStep')}
            description={t('common:massPayoutFirstStep')}
          />
          <StepDescriptionCard
            title={t('common:secondStep')}
            description={t('common:massPayoutSecondStep')}
          />
          <StepDescriptionCard
            title={t('common:thirdStep')}
            description={t('common:massPayoutThirdStep')}
          />
        </SimpleGrid>
        <ImportFileCard
          setCurrenciesSelectDisabled={setDisabled}
          disabled={!item}
          setFileDataChecked={setFileDataChecked}
          setResponseData={setResponseData}
          selectedCurrencyId={item?.id ?? ''}
          setGeneralError={setGeneralError}
        >
          <Stack mb="lg" align="center">
            {item?.label ? (
              <Text tt="capitalize" size="lg" weight={500}>
                {t('common:selectCurrency')}
              </Text>
            ) : (
              <CurrencyTitleSkeleton />
            )}
            <Paper withBorder py="xs" px="md" radius={20}>
              <SelectCurrencyCard
                type="popup"
                disabled={disabled}
                data={data?.currencies ?? []}
                setSelectedItem={setSelectedItem}
                selectedItem={item}
              >
                {isLoading ? (
                  <MyBalanceSkeleton />
                ) : (
                  <MyBalance
                    precision={item?.precision ?? 2}
                    icon={item?.image}
                    balance={item?.amount ? +item.amount : 0}
                    symbol={undefined}
                    symbolCurrency={item?.symbol}
                    currency={item?.id}
                  />
                )}
              </SelectCurrencyCard>
            </Paper>
          </Stack>
        </ImportFileCard>
        {hasErrorInCheckedItems() && (
          <Alert radius="lg" mb="lg" variant="filled" color="red">
            <Text>{t('errors:shouldEditFile')}</Text>
            {generalError && (
              <Text>
                {t('common:error')}
                {': '}
                {t(`errors:${generalError}`)}
              </Text>
            )}
          </Alert>
        )}
        <TransferInlineList
          hasError={hasErrorInCheckedItems()}
          responseData={responseData}
          setResponseData={setResponseData}
          selectedCurrency={item}
          fileDataChecked={fileDataChecked}
        />
      </Layout>
    </div>
  );
}

export default MassPayout;

export async function getServerSideProps() {
  return { props: {} };
}
