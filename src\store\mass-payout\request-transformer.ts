/**
 * mass payout request schema
 */
import { z } from 'zod';

export const createMassPayoutApiRequestSchema = z.object({
  currency: z.string().or(z.number()),
  transfers: z.array(
    z.object({
      amount: z.string(),
      account: z.string(),
      notes: z.string(),
    }),
  ),
});

export const createMassPayoutBackendRequestSchema = createMassPayoutApiRequestSchema.transform((data) => ({
  currency: data.currency,
  transfers: data.transfers,
}));
