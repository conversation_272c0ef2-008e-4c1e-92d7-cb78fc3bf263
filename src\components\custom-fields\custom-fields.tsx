import {
  NumberInput, Select, Stack, Text, Textarea,
} from '@mantine/core';
import React from 'react';

import { NewDropzoneWithMutate } from '../new-dropzone';
import { UseFormReturnType } from '@mantine/form';
import { DatePickerInput } from '@mantine/dates';
import useTranslation from 'next-translate/useTranslation';
import MultipleFieldsForm from './custom-multiple-field-form';
import { CUSTOM_FIELDS_TYPE } from '@/types/custom-fields-type.type';
import { TranslatedTextValue } from '../common/translated-text-value';
import { getTranslatedTextValue } from '@/utils';
import { useRouter } from 'next/router';

interface CustomFieldsFormProps {
  fields:
    | (
        | {
            type?: CUSTOM_FIELDS_TYPE | null | undefined;
            name?: string | null | undefined;
            nameAr?: string | null | undefined;
            placeholder?: string | null | undefined;
            placeholderAr?: string | null | undefined;
            regex?: string | null | undefined;
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            value: any;
            listItems?: string[] | null;
          }
        | undefined
      )[]
    | undefined;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  form: UseFormReturnType<any>;
}

function CustomFieldsForm(props: CustomFieldsFormProps) {
  const { fields, form } = props;
  const { t } = useTranslation();
  const { locale } = useRouter();

  const renderError = (index: number) => {
    const error = form?.errors[`fields.${index}`];
    return form?.errors[`fields.${index}`] ? t(`errors:${error}`) : '';
  };
  //* *******************Utils Functions************************************* */
  const returnPlaceholder = (
    placeholder: string | null | undefined,
    placeholderAr: string | null | undefined,
  ) => getTranslatedTextValue(locale, placeholder ?? '', placeholderAr);
  const renderDynamicFields = (fieldsParam: typeof fields) => {
    if (!fieldsParam) return [];
    return fieldsParam.map((v, index) => {
      if (v?.type === CUSTOM_FIELDS_TYPE.number) {
        return (
          <div key={v.name}>
            <NumberInput
              step={0}
              radius="lg"
              label={(
                <Text tt="capitalize">
                  <TranslatedTextValue
                    keyEn={v?.name ?? ''}
                    keyAr={v?.nameAr}
                  />
                </Text>
              )}
              placeholder={returnPlaceholder(v?.placeholder, v?.placeholderAr)}
              type="number"
              {...form.getInputProps(`fields.${index}.value`)}
            />
            <Text color="red" size="xs" mx={7}>
              {renderError(index)}
            </Text>
          </div>
        );
      }
      if (v?.type === CUSTOM_FIELDS_TYPE.string) {
        return (
          <div key={v.name}>
            <Textarea
              minRows={1}
              autosize
              radius="lg"
              label={(
                <Text tt="capitalize">
                  {' '}
                  <TranslatedTextValue
                    keyEn={v?.name ?? ''}
                    keyAr={v?.nameAr}
                  />
                </Text>
              )}
              placeholder={returnPlaceholder(v?.placeholder, v?.placeholderAr)}
              {...form.getInputProps(`fields.${index}.value`)}
            />
            <Text color="red" size="xs" mx={7}>
              {renderError(index)}
            </Text>
          </div>
        );
      }
      if (v?.type === CUSTOM_FIELDS_TYPE.datetime) {
        return (
          <div key={v.name}>
            <DatePickerInput
              radius="lg"
              label={(
                <Text tt="capitalize">
                  {' '}
                  <TranslatedTextValue
                    keyEn={v?.name ?? ''}
                    keyAr={v?.nameAr}
                  />
                </Text>
              )}
              placeholder={returnPlaceholder(v?.placeholder, v?.placeholderAr)}
              {...form.getInputProps(`fields.${index}.value`)}
            />
            <Text color="red" size="xs" mx={7}>
              {renderError(index)}
            </Text>
          </div>
        );
      }
      if (v?.type === CUSTOM_FIELDS_TYPE.multiple) {
        return (
          <div key={v?.name}>
            <MultipleFieldsForm fieldIndex={index} form={form} key={v.name} />
            <Text color="red" size="xs" mx={7}>
              {renderError(index)}
            </Text>
          </div>
        );
      }
      if (v?.type === CUSTOM_FIELDS_TYPE.list) {
        return (
          <div key={v?.name}>
            <Select
              searchable
              clearable
              data={v?.listItems ?? []}
              radius="lg"
              label={(
                <Text tt="capitalize">
                  <TranslatedTextValue
                    keyEn={v?.name ?? ''}
                    keyAr={v?.nameAr}
                  />
                </Text>
              )}
              placeholder={returnPlaceholder(v?.placeholder, v?.placeholderAr)}
              {...form.getInputProps(`fields.${index}.value`)}
            />
            <Text color="red" size="xs" mx={7}>
              {renderError(index)}
            </Text>
          </div>
        );
      }
      return (
        <NewDropzoneWithMutate
          dropzoneProps={{
            multiple: false,
            sx: { cursor: 'pointer' },
          }}
          onUploadFinish={(file) => form.setFieldValue(`fields.${index}.value`, file[0])}
          title={getTranslatedTextValue(locale, v?.name ?? '', v?.nameAr)}
          key={v?.name}
          onDelete={() => {
            form.setFieldValue(`fields.${index}.value`, '');
          }}
          error={renderError(index) as string}
          deleteAllSelectedFiles={v?.value === ''}
          description={[returnPlaceholder(v?.placeholder, v?.placeholderAr)]}
          files={v?.value && v?.value !== '' ? [v?.value] : undefined}
        />
      );
    });
  };

  return <Stack spacing="xl">{renderDynamicFields(fields)}</Stack>;
}

export default CustomFieldsForm;
