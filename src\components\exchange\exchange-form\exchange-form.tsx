/* eslint-disable max-lines */
/**
 * This component renders a Exchange form.
 *
 * @description
 * This form used to convert between two currencies and display the rate and exchange fee.
 * In this form there are to currencies selects,the first one is to select "give" currency and the second one is to select "get" currency.
 * When submitting the form will create "transfer" with "exchange" type.
 * To create transfer should select "give and get" currencies and fill the amount for each one and click to "exchange" button.
 * There is a switch button to replace between the selected currencies.
 *
 */
import { Button, Stack } from '@mantine/core';
import { useForm } from '@mantine/form';
import React, { useEffect, useState } from 'react';

import { useDisclosure } from '@mantine/hooks';
import useTranslation from 'next-translate/useTranslation';
import { UserApiResponse, getUserQuery } from '@/store/user';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/router';
import { createTransferMutation } from '@/store/transfer';

import { FeesExchangeCalculate } from '@/utils/fees-functions/fee-exchange';
import { RatesApiResponse } from '@/store/rate';
import { minMaxError } from '@/utils/min-max-exchange-function';
import { getCurrenciesQuery } from '@/store/currencies';
import { eqBy, prop, unionWith } from 'ramda';
import { TransfersApiResponse } from '@/store/transfer/types';

import SwitchCurrenciesBtn from './switch-currencies-btn';
import ExchangeCurrencyItem from './exchange-currency-item';
import ExchangeRateAndFees from './exchange-rate';
import { ChipGroupPercentage } from '@/components/chip-group-percentage';
import { ConfirmModal } from '@/components/common/confirm-modal';
import { PopUpSuccess } from '@/components/pop-up-success';
import SubmitButton from '@/components/common/submit-button';
import { TransactionConfirmModal } from '@/components/common/transaction-confirm-modal';
import ExchangeDetails from '@/components/pop-up-success/exchange-details';
import { Captcha } from '@/components/common/captcha';
import { useCaptcha } from '@/hooks';
import { playCaptcha, ROUTES } from '@/data';
import { ErrorFrontendType } from '@/types';
import { LimitsErrorKeys } from '@/types/error.type';
import { ErrorPopup } from '@/components/common/errorr-poup';
import Link from 'next/link';
import { fmt } from './helper';

/**
 * @description
 * This function returns initial get or give currency item if founded in the route query.
 * And it returns merge of all currencies with user currencies to
 *  get the all currencies with the user balance amount for each currency
 */
const getInitialGetGiveCurrency = ({
  currencies,
  allCurrencies,
  fromCurrency,
  toCurrency,
}: {
  currencies: UserApiResponse['currencies'] | undefined;
  allCurrencies: UserApiResponse['currencies'];
  fromCurrency: string | string[] | undefined;
  toCurrency: string | string[] | undefined;
}) => {
  const mergedCurrencies = unionWith(
    eqBy(prop('id')),
    currencies ?? [],
    allCurrencies ?? [],
  );
  const initCurrency = fromCurrency
    ? mergedCurrencies?.filter((i) => i?.uid === fromCurrency)
    : undefined;
  const initCurrencyTo = toCurrency
    ? mergedCurrencies?.filter((i) => i?.uid === toCurrency)
    : undefined;
  return {
    initFromCurency: initCurrency ? initCurrency[0] : undefined,
    initToCurency: initCurrencyTo ? initCurrencyTo[0] : undefined,
    mergedCurrencies,
  };
};
interface Props {
  rates: RatesApiResponse;
}
// eslint-disable-next-line complexity
export function ExchangeForm(props: Props) {
  const { t } = useTranslation();
  const { query, push, pathname } = useRouter();
  const [opened, { open, close }] = useDisclosure(false);
  const [fees, setFees] = useState(0);
  const [giveAmount, setGiveAmount] = useState<number | '' | undefined>();
  const [getAmount, setGetAmount] = useState<number | '' | undefined>();
  const [selectedGet, setSelectedGet] = useState<UserApiResponse['currencies'][0]>();
  const [selectedGive, setSelectedGive] = useState<UserApiResponse['currencies'][0]>();
  const [responseData, setResponseData] = useState<TransfersApiResponse['data'][0]>();
  const [isLoading, setLoading] = useState(false);
  const [openLimitPopup, setLimitPopup] = useState(false);

  const { execute, reCaptchaRef, reset } = useCaptcha();
  const allCurrencies = useQuery(getCurrenciesQuery({}));

  const handleUserDataSuccess = (res: UserApiResponse) => {
    if (selectedGive) {
      const newItem = res?.currencies?.filter((i) => i?.value === selectedGive?.value);
      setSelectedGive(newItem[0]);
    }
    if (selectedGet) {
      const newItem = res?.currencies?.filter((i) => i?.value === selectedGet?.value);
      setSelectedGet(newItem[0]);
    }
  };

  const { data } = useQuery({
    ...getUserQuery({}),
    onSuccess: handleUserDataSuccess,
  });

  const rerunCurrencies = getInitialGetGiveCurrency({
    allCurrencies: allCurrencies?.data?.data,
    currencies: data?.currencies,
    fromCurrency: query?.currency,
    toCurrency: query?.to,
  });

  const currencyGiveItem = selectedGive ?? rerunCurrencies?.initFromCurency;
  const currencyGetItem = selectedGet ?? rerunCurrencies?.initToCurency;

  const calculateFees = (amount: number, isReverse = false) => FeesExchangeCalculate(
    currencyGiveItem?.code,
    currencyGetItem,
    props?.rates,
    amount,
    isReverse,
  );

  const queryClient = useQueryClient();
  const form = useForm({
    initialValues: {
      amount: undefined as number | undefined,
      type: 'exchange',
    },
  });

  const handleMutationSuccess = (res: TransfersApiResponse['data'][0]) => {
    setResponseData(res);
    queryClient.invalidateQueries({ queryKey: ['user'] });
    close();
    form.reset();
    setGetAmount('');
    setGiveAmount('');
    setSelectedGive(undefined);
    setSelectedGet(undefined);
    reset();
    setLoading(false);
    // Clear amount from URL after successful transaction
    const newQuery = { ...query };
    delete newQuery.amount;
    push({ pathname, query: newQuery }, undefined, { shallow: true });
  };

  const handleMutationError = (error: ErrorFrontendType) => {
    if (error?.response?.data?.message?.key === LimitsErrorKeys.ERROR_PASS_THE_LIMIT) {
      setLimitPopup(true);
    }
    close();
    reset();
    setLoading(false);
  };

  const { mutate } = useMutation(
    createTransferMutation().mutationFn,
    {
      onSuccess: handleMutationSuccess,
      onError: handleMutationError,
    },
  );

  useEffect(() => {
    const fromUid = query.currency as string | undefined;
    const toUid = query.to as string | undefined;

    if (!fromUid && !toUid) {
      if (selectedGive || selectedGet || giveAmount || getAmount) {
        setSelectedGive(undefined);
        setSelectedGet(undefined);
        setGiveAmount('');
        setGetAmount('');
        setFees(0);
        form.reset();
      }
      return;
    }

    if (fromUid && (!selectedGive || selectedGive.uid !== fromUid) && rerunCurrencies?.initFromCurency) {
      setSelectedGive(rerunCurrencies.initFromCurency);
    }
    if (toUid && (!selectedGet || selectedGet.uid !== toUid) && rerunCurrencies?.initToCurency) {
      setSelectedGet(rerunCurrencies.initToCurency);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    query.currency,
    query.to,
    rerunCurrencies?.initFromCurency,
    rerunCurrencies?.initToCurency,
  ]);

  // Load amount from URL when URL amount changes
  useEffect(() => {
    const urlAmount = query.amount;
    if (urlAmount && !Number.isNaN(Number(urlAmount))) {
      const amount = Number(urlAmount);
      // Only update if the current form amount is different
      if (form.values.amount !== amount) {
        setGiveAmount(amount);
        form.setValues({ amount });
        // Calculate fees for the loaded amount
        if (currencyGiveItem && currencyGetItem) {
          const { commission } = calculateFees(amount);
          setFees(commission);
        }
      }
    } else if (!urlAmount && form.values.amount) {
      // Clear amount if not in URL
      setGiveAmount('');
      setGetAmount('');
      form.setValues({ amount: undefined });
      setFees(0);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [query.amount, currencyGiveItem, currencyGetItem]);

  // Update URL when selected currencies change
  useEffect(() => {
    if (currencyGiveItem && currencyGetItem) {
      push(
        {
          pathname,
          query: { ...query, currency: currencyGiveItem.uid, to: currencyGetItem.uid },
        },
        undefined,
        { shallow: true },
      );
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currencyGiveItem?.uid, currencyGetItem?.uid]);

  /* recompute fees when either currency or amount changes */
  useEffect(() => {
    if (currencyGiveItem && currencyGetItem && typeof form.values.amount === 'number') {
      const { commission } = calculateFees(form.values.amount);
      setFees(commission);
    } else {
      setFees(0);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currencyGiveItem?.uid, currencyGetItem?.uid, form.values.amount]);

  const submitTransfer = () => {
    setLoading(true);
    const body = {
      body: {
        ...form.values,
        currencyFrom: currencyGiveItem?.value ?? '',
        currencyTo: currencyGetItem?.value ?? '',
        userTo: data?.accountId ?? '',
      },
    };

    if (reCaptchaRef.current) {
      execute((token) => mutate({ ...body, chaKey: token }));
    } else {
      mutate(body);
    }
  };

  const handlePercentChange = (v: string) => {
    const newAmount = currencyGiveItem ? (+currencyGiveItem.amount * +v) / 100 : 0;
    setGetAmount(undefined);
    setGiveAmount(newAmount);
    const { commission } = calculateFees(newAmount);
    setFees(commission);
    form.setValues({ amount: newAmount });
    const newQuery = { ...query };
    if (newAmount > 0) newQuery.amount = String(fmt(newAmount, currencyGiveItem));
    else delete newQuery.amount;

    push({ pathname, query: newQuery }, undefined, { shallow: true });
  };

  const handleGiveAmountInputChange = (v: number | '') => {
    if (v !== '') {
      setGetAmount(undefined);
      setGiveAmount(+v);
      form.setValues({ amount: +v });
      const { commission } = calculateFees(+v);
      setFees(commission);
      // Save amount to URL
      const newQuery = { ...query, amount: String(fmt(+v, currencyGiveItem)) };
      push({ pathname, query: newQuery }, undefined, { shallow: true });
    } else {
      setFees(0);
      setGetAmount('');
      setGiveAmount('');
      form.setValues({ amount: undefined });
      // Clear amount from URL
      const newQuery = { ...query };
      delete newQuery.amount;
      push({ pathname, query: newQuery }, undefined, { shallow: true });
    }
  };

  const handleGetAmountInputChange = (v: number | '') => {
    if (v !== '') {
      setGetAmount(+v);
      setGiveAmount(undefined);
    } else {
      setGetAmount(undefined);
      setGiveAmount('');
    }
    const { calculatedValue, commission } = calculateFees(+v, true);
    form.setValues({ amount: calculatedValue });
    setFees(commission);

    const newQuery = { ...query };
    if (typeof calculatedValue === 'number' && calculatedValue > 0) {
      newQuery.amount = String(fmt(calculatedValue, currencyGiveItem));
    } else {
      delete newQuery.amount;
    }
    push({ pathname, query: newQuery }, undefined, { shallow: true });
  };

  const { calculatedValue: getSideCalc } = calculateFees(getAmount as number, true);
  const isMinMaxError = minMaxError(currencyGiveItem, giveAmount, getAmount, getSideCalc);
  const giveCurrenciesItems = rerunCurrencies?.mergedCurrencies?.filter(
    (i) => i.allowExchangeTo && i.value !== currencyGiveItem?.value,
  );

  const getCurrenciesItems = rerunCurrencies?.mergedCurrencies
    .filter((i) => i.allowExchangeFrom && i.value !== currencyGetItem?.value)
    .sort((a, b) => b.amount - a.amount);

  const { amount } = form.values;
  const isSubmitButtonDisabled = !currencyGiveItem
  || !currencyGetItem
  || !(typeof amount === 'number' && amount > 0)
  || isMinMaxError;

  const { exchangeRate } = calculateFees(1);

  return (
    <div>
      <form>
        <Stack my="md" mx="auto">
          <ExchangeCurrencyItem
            currencyDataArray={getCurrenciesItems}
            setSelectedItem={setSelectedGive}
            currencyItem={currencyGiveItem}
            inputValue={
              giveAmount
              ?? (typeof getAmount === 'number'
                ? calculateFees(getAmount, true).calculatedValue
                : '')
            }
            isMinMaxError={isMinMaxError}
            onChangeInputValue={handleGiveAmountInputChange}
            inputType="give"
          />
          <SwitchCurrenciesBtn
            form={form}
            selectedGet={selectedGet}
            selectedGive={selectedGive}
            setGetAmount={setGetAmount}
            setGiveAmount={setGiveAmount}
            setSelectedGet={setSelectedGet}
            setSelectedGive={setSelectedGive}
          />
          <ExchangeCurrencyItem
            currencyDataArray={giveCurrenciesItems}
            setSelectedItem={setSelectedGet}
            currencyItem={currencyGetItem}
            inputValue={
              getAmount
              ?? (typeof giveAmount === 'number' && giveAmount > 0
                ? calculateFees(giveAmount).calculatedValue
                : '')
            }
            isMinMaxError={isMinMaxError}
            onChangeInputValue={handleGetAmountInputChange}
            inputType="get"
          />
          <ExchangeRateAndFees
            currencyGetItem={currencyGetItem}
            currencyGiveItem={currencyGiveItem}
            dataRates={props?.rates}
            fees={fees}
          />
          <ChipGroupPercentage
            disabled={!currencyGiveItem}
            handlePercentChange={handlePercentChange}
          />
          <SubmitButton
            disabled={isSubmitButtonDisabled}
            onClick={open}
            fullWidth
          >
            {t('common:exchange')}
          </SubmitButton>
        </Stack>
      </form>
      <ConfirmModal
        close={close}
        openedDefault={opened}
        isLoading={isLoading}
        onClick={submitTransfer}
        message={<div>{t('common:completeOperation')}</div>}
      />
      <TransactionConfirmModal
        close={close}
        openedDefault={opened}
        isLoading={isLoading}
        onClick={submitTransfer}
        operationType="exchange"
      >
        <ExchangeDetails
          currencyCode={currencyGiveItem?.code}
          currencyToCode={currencyGetItem?.code}
          currencySymbol={currencyGiveItem?.symbol}
          currencyToSymbol={currencyGetItem?.symbol}
          amount={form.values?.amount as number}
          actualAmount={
            (form?.values?.amount ? form.values.amount : 0) * exchangeRate
            - fees
          }
          precision={currencyGiveItem?.precision ?? 2}
          currencyToPrecision={currencyGetItem?.precision ?? 2}
          fees={fees}
          rate={exchangeRate}
        />
      </TransactionConfirmModal>
      {responseData && (
        <PopUpSuccess
          operationType="exchange"
          currencySymbol={responseData?.snapshot?.Currency?.symbol}
          currencyToSymbol={responseData?.snapshot?.exchangedCurrency?.symbol}
          setResponseData={setResponseData}
          operationId={responseData?.transactionId}
          amount={responseData?.amount}
          actualAmount={responseData?.actualAmount}
          fees={responseData?.snapshot?.fees}
          status="approved"
          note={undefined}
          date={responseData?.createdAt}
          sender={undefined}
          receiver={undefined}
          currencyCode={responseData?.snapshot?.Currency?.code}
          currencyToCode={responseData?.snapshot?.exchangedCurrency?.code}
          paymentMethod={undefined}
          fields={undefined}
          rate={responseData?.snapshot?.rate}
          precision={responseData?.currencyFrom?.precision}
          currencyToPrecision={responseData?.currencyTo?.precision}
          gif={null}
          cryptoGetaway={undefined}
          originPaymentAmount={undefined}
          isLoading={false}
          adminMessage={undefined}
        />
      )}
      <ErrorPopup
        open={openLimitPopup}
        setOpen={setLimitPopup}
        message={t('errors:limit-error')}
        actionButton={(
          <Button
            fullWidth
            radius="lg"
            component={Link}
            href={`${ROUTES.myAccount.path}?page=limits`}
          >
            {t('common:goToLimits')}
          </Button>
        )}
      />
      <Captcha reCaptchaRef={reCaptchaRef} active={playCaptcha} />
    </div>
  );
}
