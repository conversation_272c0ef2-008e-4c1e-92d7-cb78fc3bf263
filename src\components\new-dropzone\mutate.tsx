/* eslint-disable react/require-default-props */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { ComponentProps, useEffect, useState } from 'react';
import NewDropzone, { FileType } from './new-dropzone';
import { useMutation } from '@tanstack/react-query';
import { addFileMutation } from '@/store/file';

interface MutateProps extends ComponentProps<typeof NewDropzone> {
  onUploadFinish?: (newFiles: FileType[], oldFiles: FileType[]) => void;
  deleteAllSelectedFiles: boolean;
  iconSrc?: string;
  iconSize?: number;
}
function Mutate(props: MutateProps) {
  const {
    onUploadFinish,
    deleteAllSelectedFiles,
    files: externalFiles,
    ...rest
  } = props;
  const [files, setFiles] = useState<FileType[]>(externalFiles || []);
  const { mutate, isLoading } = useMutation({
    ...addFileMutation(),
    onSuccess(data) {
      if (onUploadFinish) onUploadFinish(data, files);
      if (rest.dropzoneProps.multiple) setFiles([...data, ...files]);
      else setFiles(data);
    },
  });
  const handleSelectFile = (data: any) => {
    const formData = new FormData();
    data.map((file: any) => formData.append('files', file));
    mutate(formData);
  };
  const deleteAllSelectedFilesFun = () => {
    setFiles([]);
  };
  useEffect(() => {
    if (deleteAllSelectedFiles) deleteAllSelectedFilesFun();
  }, [deleteAllSelectedFiles]);
  useEffect(() => {
    if (externalFiles) setFiles(externalFiles);
  }, [externalFiles]);
  return (
    <NewDropzone
      {...rest}
      uploadFile={handleSelectFile}
      deleteFile={(id) => {
        if (!rest.dropzoneProps.disabled) {
          const newFiles = files.filter((v) => v.id !== id);
          setFiles(newFiles);
        }
      }}
      loading={isLoading}
      files={files}
    />
  );
}

export default Mutate;
