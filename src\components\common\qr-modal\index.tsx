import { useRef } from 'react';
import {
  Modal, Box, ActionIcon, useMantineTheme, Tooltip,
} from '@mantine/core';
import { Icon } from '@/components/common/icon';
import { useQRScanner } from '@/components/common/qr-modal/hooks/useQRScanner';
import { useQRGenerator } from '@/components/common/qr-modal/hooks/useQRGenerator';
import { useResponsiveQRSizes } from '@/components/common/qr-modal/hooks/useResponsiveQRSizes';
import { QRScannerView } from '@/components/common/qr-modal/view/QRScannerView';
import { QRCodeView } from '@/components/common/qr-modal/view/QRCodeView';
import { getModalStyles } from '@/components/common/qr-modal/qr-modal-styles';
import useTranslation from 'next-translate/useTranslation';
import { useRouter } from 'next/router';
import { DetectedBarcode } from '@/components/common/qr-modal/qr-helpers';

export interface QRModalProps {
  opened: boolean;
  onClose: () => void;
  // eslint-disable-next-line react/require-default-props
  accountId?: string;
  // eslint-disable-next-line react/require-default-props
  mode?: 'scanner' | 'generator' | 'both';
  // eslint-disable-next-line react/require-default-props
  onScanComplete?: (codes: DetectedBarcode[]) => void | Promise<void>;
  // eslint-disable-next-line react/require-default-props
  onScanError?: (error: unknown) => void;
  // eslint-disable-next-line react/require-default-props
  showGenerateButton?: boolean;
  // eslint-disable-next-line react/require-default-props
  showCloseButton?: boolean;
}

// eslint-disable-next-line complexity, sonarjs/cognitive-complexity
export function QRModal({
  opened,
  onClose,
  accountId,
  mode = 'both',
  onScanComplete,
  onScanError,
  showGenerateButton = true,
  showCloseButton = true,
}: QRModalProps) {
  const theme = useMantineTheme();
  const qrWrapperRef = useRef<HTMLDivElement>(null);
  const { t } = useTranslation();
  const router = useRouter();
  const isRTL = router.locale === 'ar';

  // Custom hooks
  const { sizes, isMobile } = useResponsiveQRSizes();
  const {
    isScanning,
    showScanner,
    scanError,
    onScanComplete: defaultOnScanComplete,
    onScanError: defaultOnScanError,
    handleImageUpload,
    resetToScanner,
    setIsScanning,
    setScanError,
  } = useQRScanner(opened, onClose);

  const {
    generatedQR,
    generateQR,
    handleDownload,
    handleShare,
    resetQR,
  } = useQRGenerator(accountId);

  // Wrap scan complete handler to catch validation errors
  const handleScanComplete = onScanComplete
    ? async (codes: DetectedBarcode[]) => {
      // Clear any previous errors when starting a new scan
      setScanError(null);

      try {
        await Promise.resolve(onScanComplete(codes));
      } catch (error) {
        // For validation errors, set the error directly to show in modal
        const errorToHandle = error instanceof Error ? error : new Error('Unknown scan error');
        setScanError(errorToHandle);

        // Also call custom error handler if provided
        if (onScanError) {
          onScanError(errorToHandle);
        }
      }
    }
    : defaultOnScanComplete;

  const handleScanError = onScanError || defaultOnScanError;

  const handleClose = () => {
    resetToScanner();
    resetQR();
    onClose();
  };

  const handleGenerateQR = () => {
    setIsScanning(false);
    generateQR();
  };

  // Determine initial mode
  const shouldStartWithScanner = mode === 'scanner' || (mode === 'both' && isScanning);
  const shouldShowGenerator = mode === 'generator' || mode === 'both';

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      centered
      size="md"
      styles={getModalStyles()}
      withCloseButton={false}
      scrollAreaComponent={undefined}
    >
      {shouldStartWithScanner ? (
        <Box
          sx={{
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden',
          }}
        >
          <QRScannerView
            showScanner={showScanner}
            scanError={scanError}
            sizes={sizes}
            isMobile={isMobile}
            onScanComplete={handleScanComplete}
            onScanError={handleScanError}
            onImageUpload={handleImageUpload}
            onGenerateQR={shouldShowGenerator && showGenerateButton ? handleGenerateQR : () => {}}
            onClose={showCloseButton ? handleClose : () => {}}
          />
        </Box>
      ) : (
        <Box
          sx={{
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden',
            position: 'relative',
          }}
        >
          {/* Top action buttons row for QR view */}
          <Box
            sx={{
              position: 'absolute',
              top: theme.spacing.md,
              left: theme.spacing.md,
              right: theme.spacing.md,
              zIndex: 10,
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            {/* Back button - only show if both modes are available */}
            {mode === 'both' ? (
              <Tooltip label={t('common:backToScanner')} position={isRTL ? 'left' : 'right'} withArrow>
                <ActionIcon
                  onClick={resetToScanner}
                  size="lg"
                  variant="subtle"
                  color="gray"
                  sx={() => ({
                    backgroundColor: theme.colorScheme === 'dark'
                      ? theme.fn.rgba(theme.colors.dark[6], 0.8)
                      : theme.fn.rgba(theme.colors.gray[5], 0.8),
                    '&:hover': {
                      backgroundColor: theme.colorScheme === 'dark'
                        ? theme.fn.rgba(theme.colors.dark[5], 0.9)
                        : theme.fn.rgba(theme.colors.gray[6], 0.9),
                    },
                  })}
                >
                  <Icon icon="chevron-left" size="1.2rem" color="white" />
                </ActionIcon>
              </Tooltip>
            ) : (
              <Box />
            )}

            {/* Right side buttons */}
            <Box sx={{ display: 'flex', gap: theme.spacing.xs }}>
              {/* Download button */}
              {generatedQR && (
                <Tooltip label={t('common:downloadQrAsImage')} position={isRTL ? 'right' : 'left'} withArrow>
                  <ActionIcon
                    onClick={() => handleDownload(qrWrapperRef)}
                    size="lg"
                    variant="subtle"
                    color="gray"
                    sx={() => ({
                      backgroundColor: theme.colorScheme === 'dark'
                        ? theme.fn.rgba(theme.colors.dark[6], 0.8)
                        : theme.fn.rgba(theme.colors.gray[5], 0.8),
                      '&:hover': {
                        backgroundColor: theme.colorScheme === 'dark'
                          ? theme.fn.rgba(theme.colors.dark[5], 0.9)
                          : theme.fn.rgba(theme.colors.gray[6], 0.9),
                      },
                    })}
                  >
                    <Icon icon="download" size="1.2rem" color="white" />
                  </ActionIcon>
                </Tooltip>
              )}

              {/* Close button */}
              {showCloseButton && (
                <Tooltip label={t('common:close')} position={isRTL ? 'right' : 'left'} withArrow>
                  <ActionIcon
                    onClick={handleClose}
                    size="lg"
                    variant="subtle"
                    color="gray"
                    sx={() => ({
                      backgroundColor: theme.colorScheme === 'dark'
                        ? theme.fn.rgba(theme.colors.dark[6], 0.8)
                        : theme.fn.rgba(theme.colors.gray[5], 0.8),
                      '&:hover': {
                        backgroundColor: theme.colorScheme === 'dark'
                          ? theme.fn.rgba(theme.colors.dark[5], 0.9)
                          : theme.fn.rgba(theme.colors.gray[6], 0.9),
                      },
                    })}
                  >
                    <Icon icon="x" size="1.2rem" color="white" />
                  </ActionIcon>
                </Tooltip>
              )}
            </Box>
          </Box>

          <QRCodeView
            generatedQR={generatedQR}
            accountId={accountId}
            sizes={sizes}
            isMobile={isMobile}
            qrWrapperRef={qrWrapperRef}
            onShare={handleShare}
          />
        </Box>
      )}
    </Modal>
  );
}
