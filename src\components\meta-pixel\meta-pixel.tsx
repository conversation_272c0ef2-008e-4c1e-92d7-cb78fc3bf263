/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @next/next/no-before-interactive-script-outside-document */
import Script from 'next/script';

declare global {
  interface Window {
    fbq?: {
      (...args: any[]): void;
      q?: any[];
    };
  }
}

const PIXEL_ID = '1312303213823053';

export default function MetaPixel() {
  return (
    <>
      <Script id="fbq-stub" strategy="beforeInteractive">
        {`
          window.fbq = window.fbq || function() {
            (window.fbq.q = window.fbq.q || []).push(arguments);
          };
        `}
      </Script>

      {/* Load SDK and initialize */}
      <Script
        id="fb-pixel"
        src="https://connect.facebook.net/en_US/fbevents.js"
        strategy="afterInteractive"
        onReady={() => {
          if (typeof window.fbq === 'function') {
            window.fbq('init', PIXEL_ID);
            window.fbq('track', 'PageView');
          }
        }}
      />
    </>
  );
}
