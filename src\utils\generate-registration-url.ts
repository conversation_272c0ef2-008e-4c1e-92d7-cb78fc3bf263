// this function use to generate registration link
export async function generateRegistrationLink(
  ref: string | string[] | undefined,
) {
  const state = crypto.randomUUID();
  const nonce = crypto.randomUUID();
  const redirectUri = encodeURIComponent('https://kazawallet.com');
  const baseUrl = 'https://auth.kasroad.com/realms/kasroad/protocol/openid-connect/auth';
  const scope = 'openid email profile';

  function generateCodeVerifier(len: number) {
    const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const randomData = crypto.getRandomValues(new Uint8Array(len));
    const chars = new Array(len);
    // eslint-disable-next-line no-plusplus
    for (let i = 0; i < len; i++) {
      chars[i] = alphabet.charAt(randomData[i] % alphabet.length);
    }
    return chars.join('');
  }

  async function generatePkceChallenge(codeVerifier: string) {
    const encoder = new TextEncoder();
    const data = encoder.encode(codeVerifier);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const base64String = btoa(String.fromCharCode.apply(null, hashArray));
    return base64String
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  }

  const codeVerifier = generateCodeVerifier(96);
  const pkceChallenge = await generatePkceChallenge(codeVerifier);

  return `${baseUrl}?client_id=${encodeURIComponent(
    'kazawallet',
  )}&redirect_uri=${redirectUri}&state=${encodeURIComponent(
    state,
  )}&response_mode=${encodeURIComponent(
    'fragment',
  )}&response_type=${encodeURIComponent('code')}&scope=${encodeURIComponent(
    scope,
  )}&nonce=${encodeURIComponent(nonce)}&code_challenge=${encodeURIComponent(
    pkceChallenge,
    // eslint-disable-next-line sonarjs/no-nested-template-literals
  )}&code_challenge_method=S256${ref ? `&ref=${ref}` : ''}`;
}
