import { useLastSearch } from '@/store/search/store';
import {
  Box,
  Button,
  Center,
  Group,
  Loader,
  ScrollArea,
  Text,
} from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';
import React, { ReactNode } from 'react';
import { Icon } from '../common/icon';

export const ActionsWrapper = ({
  isLoading,
  withClearHistoryBtn,
  showTitle,
}: {
  isLoading: boolean;
  withClearHistoryBtn: boolean;
  showTitle: boolean;
}) => {
  const { t } = useTranslation();
  const setSearchResults = useLastSearch((state) => state.setSearchResult);
  return (cbProps: { children: ReactNode }) => {
    const { children } = cbProps;
    if (showTitle) {
      if (withClearHistoryBtn) {
        return (
          <>
            <Group position="apart" pl="sm">
              <Text weight={700} color="dimmed">
                {t('common:lastSearch')}
              </Text>
              <Button
                variant="subtle"
                onClick={() => {
                  setSearchResults([]);
                }}
              >
                {t('common:clearSearch')}
              </Button>
            </Group>
            <ScrollArea mah={300} scrollHideDelay={0}>
              {children}
            </ScrollArea>
          </>
        );
      }
      return (
        <Group h={150} position="center">
          <Icon icon="search" color="gray" size={32} />
        </Group>
      );
    }
    return (
      <Box>
        <ScrollArea mah={400} scrollHideDelay={0}>
          {children}
          {isLoading && (
          <Center p="xl">
            <Loader />
          </Center>
          )}
        </ScrollArea>
      </Box>
    );
  };
};
