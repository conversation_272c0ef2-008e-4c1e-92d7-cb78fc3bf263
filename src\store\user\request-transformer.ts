/**
 * update user request schema
 */
import { Translate } from 'next-translate';
import { z } from 'zod';
import { KYC_STATUS } from './response-transformer';

const stringError = 'errors:required';
export const updateUserKYCFormSchema = (t: Translate) => z.object({
  kyc: z
    .object({
      status: z.enum([
        KYC_STATUS.Approved,
        KYC_STATUS.Disabled,
        KYC_STATUS.Pending,
        KYC_STATUS.Rejected,
      ]),
      mobile: z
        .string()
        .min(6, { message: t(stringError) })
        .regex(/^\d*$/, t('errors:pleaseEnterValidNumbers')),
      preferredCommunicationMethod: z.enum(['telegram', 'whatsapp']),
      country: z.string().min(1, { message: t(stringError) }),
      id1: z.string().min(4, { message: t(stringError) }),
      id2: z.string().min(4, { message: t(stringError) }),
      selfie: z.string().min(4, { message: t(stringError) }),
      agreetc: z
        .boolean()
        .refine((value) => !!value, { message: t(stringError) }),
    })
    .optional(),
});
export const updateUserApiRequestSchema = z.object({
  authenticatorEnabled: z.boolean().nullable().optional(),
  authenticatorToken: z.string().optional(),
  avatar: z.string().or(z.number()).nullable().optional(),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  country: z.string().nullable().optional(),
  webhookUrl: z.string().nullable().optional(),
  favoriteCurrencies: z.array(z.string().or(z.number())).optional(),
  sendVerificationConditions: z.string().nullable().optional(),
  kyc: z
    .object({
      status: z.enum([
        KYC_STATUS.Approved,
        KYC_STATUS.Disabled,
        KYC_STATUS.Pending,
        KYC_STATUS.Rejected,
      ]),
      mobile: z.string().regex(/^\d*$/, 'Please enter valid numbers'),
      preferredCommunicationMethod: z.enum(['telegram', 'whatsapp']),
      country: z.string().min(1, { message: 'required' }),
      id1: z.string().min(4, { message: 'required' }),
      id2: z.string().min(4, { message: 'required' }),
      selfie: z.string().min(4, { message: 'required' }),
      agreetc: z.boolean().refine((value) => !!value, { message: 'required' }),
    })
    .optional(),
});

export const updateUserBackendRequestSchema = updateUserApiRequestSchema.transform((data) => ({
  data: {
    avatar: data?.avatar,
    authenticator_enabled: data?.authenticatorEnabled,
    authenticator_token: data?.authenticatorToken,
    first_name: data?.firstName,
    last_name: data?.lastName,
    country: data?.country,
    webhook_url: data?.webhookUrl,
    favorite_currencies: data?.favoriteCurrencies,
    send_verification_conditions: data?.sendVerificationConditions,
    ...(data?.kyc && {
      kyc: {
        ...data.kyc,
        mobile: data.kyc.mobile ? `+${data.kyc.mobile}` : undefined,
        preferredCommunicationMethod: data.kyc.preferredCommunicationMethod,
      },
    }),
  },
}));
