import { TranslatedTextValue } from '@/components/common/translated-text-value';
import { UserApiResponse } from '@/store/user';
import { getTranslatedTextValue } from '@/utils';
import {
  Box, Group, Image, Modal, Text, UnstyledButton,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { useRouter } from 'next/router';
import React from 'react';
import MarkdownView from 'react-showdown';

interface BadgeItemProps {
  badge: UserApiResponse['badges'][0];
}
function BadgeItem({ badge }: BadgeItemProps) {
  const { locale } = useRouter();
  const [opened, { open, close }] = useDisclosure(false);
  return (
    <>
      <Modal
        opened={opened}
        onClose={close}
        title={(
          <Text fw={700}>
            <TranslatedTextValue
              keyEn={badge?.title ?? ''}
              keyAr={badge?.titleAr}
            />
          </Text>
        )}
        radius="lg"
        centered
      >
        <Group>
          <Image
            sx={{ objectFit: 'contain' }}
            width={100}
            height={100}
            radius={100}
            src={badge?.image}
          />
          <Box>
            <MarkdownView
              markdown={getTranslatedTextValue(
                locale,
                badge?.description ?? '',
                badge?.descriptionAr,
              )}
              options={{ tables: true, emoji: true }}
            />
          </Box>
        </Group>
      </Modal>

      <UnstyledButton onClick={open}>
        <Image
          sx={{ objectFit: 'contain' }}
          width={50}
          height={50}
          radius={50}
          src={badge?.image}
        />
      </UnstyledButton>
    </>
  );
}

export default BadgeItem;
