import { Select } from '@mantine/core';
import { UseFormReturnType } from '@mantine/form';
import useTranslation from 'next-translate/useTranslation';
import React from 'react';
import { getOptionsByTag } from './categories-data';
import currency from 'currency.js';
import { PaymentMethodsApiResponse } from '@/store/payment-methods';

interface Props {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  form: UseFormReturnType<any>;
  company: PaymentMethodsApiResponse['data'][0]['tag']
}
function SyriatelMtnCategorySelect(props: Props) {
  const { form, company } = props;
  const { t } = useTranslation();
  const selectOptions = (
    getOptionsByTag(company) as string[]
  ).map((i) => ({
    value: i,
    label: currency(i, { symbol: '', precision: 0 }).format(),
  }));
  return (
    <Select
      data={selectOptions}
      radius="lg"
      size="sm"
      placeholder={t('common:selectCategory')}
      label={t('common:toBeReceivedAmount')}
      value={`${form.values?.amount}`}
      onChange={(v) => v && form.setFieldValue('amount', +v)}
    />
  );
}

export default SyriatelMtnCategorySelect;
