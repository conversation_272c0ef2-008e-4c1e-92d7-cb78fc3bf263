import { Container, Table, Text } from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';
import React from 'react';

const elements = [
  {
    currency: 'الدولار الأمريكي',
    totalFee: '2.0%',
    minimumFeePerOrder: '2$',
    agentCommission: '1.0%',
    minimumAgentCommissionPerOrder: '1$',
    kazawalletCommission: '1.0%',
  },
  {
    currency: 'الليرة السورية',
    totalFee: '2.0%',
    minimumFeePerOrder: '25,000',
    agentCommission: '1.0%',
    minimumAgentCommissionPerOrder: '10,000',
    kazawalletCommission: '1.0%',
  },
  {
    currency: 'الدرهم الاماراتي',
    totalFee: '2.0%',
    minimumFeePerOrder: '7',
    agentCommission: '1.0%',
    minimumAgentCommissionPerOrder: '3.5',
    kazawalletCommission: '1.0%',
  },
  {
    currency: 'دينار عراقي',
    totalFee: '2.0%',
    minimumFeePerOrder: '7',
    agentCommission: '1.0%',
    minimumAgentCommissionPerOrder: '3.5',
    kazawalletCommission: '1.0%',
  },
  {
    currency: 'اليورو',
    totalFee: '3.0%',
    minimumFeePerOrder: '2',
    agentCommission: '1.5%',
    minimumAgentCommissionPerOrder: '1',
    kazawalletCommission: '1.5%',
  },
];

function AgentFees() {
  const { t } = useTranslation();
  const rows = (
    <>
      <tr style={{ color: '#fff' }}>
        <th style={{ color: '#fff' }}>{t('common:totalFee')}</th>
        <td>{elements[0].totalFee}</td>
        <td>{elements[1].totalFee}</td>
        <td>{elements[2].totalFee}</td>
        <td>{elements[3].totalFee}</td>
        <td>{elements[4].totalFee}</td>
      </tr>
      <tr style={{ color: '#fff' }}>
        <th style={{ color: '#fff' }}>{t('common:minimumFeePerOrder')}</th>
        <td>{elements[0].minimumFeePerOrder}</td>
        <td>{elements[1].minimumFeePerOrder}</td>
        <td>{elements[2].minimumFeePerOrder}</td>
        <td>{elements[3].minimumFeePerOrder}</td>
        <td>{elements[4].minimumFeePerOrder}</td>
      </tr>
      <tr style={{ color: '#fff' }}>
        <th style={{ color: '#fff' }}>{t('common:agentCommission')}</th>
        <td>{elements[0].agentCommission}</td>
        <td>{elements[1].agentCommission}</td>
        <td>{elements[2].agentCommission}</td>
        <td>{elements[3].agentCommission}</td>
        <td>{elements[4].agentCommission}</td>
      </tr>
      <tr style={{ color: '#fff' }}>
        <th style={{ color: '#fff' }}>
          {t('common:minimumAgentCommissionPerOrder')}
        </th>
        <td>{elements[0].minimumAgentCommissionPerOrder}</td>
        <td>{elements[1].minimumAgentCommissionPerOrder}</td>
        <td>{elements[2].minimumAgentCommissionPerOrder}</td>
        <td>{elements[3].minimumAgentCommissionPerOrder}</td>
        <td>{elements[4].minimumAgentCommissionPerOrder}</td>
      </tr>
      <tr style={{ color: '#fff' }}>
        <th style={{ color: '#fff' }}>{t('common:kazawalletCommission')}</th>
        <td>{elements[0].kazawalletCommission}</td>
        <td>{elements[1].kazawalletCommission}</td>
        <td>{elements[2].kazawalletCommission}</td>
        <td>{elements[3].kazawalletCommission}</td>
        <td>{elements[4].kazawalletCommission}</td>
      </tr>
    </>
  );

  return (
    <Container
      py={40}
      px={{
        xs: 20,
        sm: 30,
        lg: 40,
        base: 20,
      }}
      size={1200}
    >
      <Text
        ta="center"
        ff="BauhausC,__Cairo_0685b6,__Cairo_Fallback_0685b6"
        size={43}
        weight="bold"
        color="white"
      >
        {t('common:commissionStructureForKazawalletAgents')}
      </Text>
      <Table mt="md" verticalSpacing="xs">
        <thead>
          <tr>
            <th style={{ color: '#fff' }}>{t('common:currency')}</th>
            <th style={{ color: '#fff' }}>USD</th>
            <th style={{ color: '#fff' }}>SYP</th>
            <th style={{ color: '#fff' }}>AED</th>
            <th style={{ color: '#fff' }}>IQD</th>
            <th style={{ color: '#fff' }}>EUR</th>
          </tr>
        </thead>
        <tbody>{rows}</tbody>
      </Table>
    </Container>
  );
}

export default AgentFees;
