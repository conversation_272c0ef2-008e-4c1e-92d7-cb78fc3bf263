import { apiEndpoints } from '@/data';
import { ApiClient } from '@/lib';

enum queryKeys {
  setting = 'setting',
}
/**
 * @description function calls handler in "/setting" api route to return agents and announcements
 * @returns array of agents and array of announcements
 */
const getSettingRequest = () => ApiClient.get(apiEndpoints.setting())
  .then((res) => res?.data)
  .catch((e) => {
    // handleApiError(e, true);
    throw e.response?.data;
  });

export const getSettingQuery = () => ({
  queryKey: [queryKeys.setting],
  queryFn: () => getSettingRequest(),
  refetchOnWindowFocus: false,
});
