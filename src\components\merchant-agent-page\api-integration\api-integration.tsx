import { assetBaseUrl, ROUTES } from '@/data';
import {
  BackgroundImage,
  Center,
  Container,
  SimpleGrid,
  Stack,
  Text,
  useMantineTheme,
} from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';
import Link from 'next/link';
import React from 'react';
import styles from '../styles.module.scss';
import { Icon } from '@/components/common/icon';

function ApiIntegration() {
  const { t } = useTranslation();
  const theme = useMantineTheme();
  return (
    <Container
      pb={40}
      px={{
        xs: 20,
        sm: 30,
        lg: 40,
        base: 20,
      }}
      size={1200}
    >
      <BackgroundImage radius="xl" py={80} px={30} src={`${assetBaseUrl}/assets/merchant/api-integration-bg.png`}>
        <Stack spacing="xl" align="center">
          <Text
            ta="center"
            maw={350}
            ff="BauhausC,__Cairo_0685b6,__Cairo_Fallback_0685b6"
            size={43}
            weight="bold"
            color="white"
          >
            {t('common:apiIntegrationMerchantTitle')}
          </Text>
          <Text maw={500} ta="center" color="dimmed" size="lg" weight={500} mb="lg">
            {t('common:apiIntegrationMerchantDescription')}
          </Text>
          <Link
            href={ROUTES.paymentLinkApiUsage.path}
            style={{
              textDecoration: 'none',
              color: theme.colors.primary[7],
              width: 261,
            }}
            className={styles.aHero}
          >
            {t('common:exploreApiDocumentation')}
          </Link>
          <SimpleGrid
            mt="md"
            cols={4}
            spacing="sm"
            breakpoints={[
              { maxWidth: 'md', cols: 2, spacing: 'md' },
              { maxWidth: 'xs', cols: 1, spacing: 'sm' },
            ]}
          >
            <Stack>
              <div>
                <Center>
                  <Icon
                    icon="file-invoice"
                    size={50}
                    color={theme.colors.teal[6]}
                  />
                </Center>
                <Text color="white" weight="bold" size="xl">
                  {t('common:apiIntegrationMerchantFeat1Title')}
                </Text>
              </div>
              <Text maw={220} color="dimmed" size="lg" weight={500}>
                {t('common:apiIntegrationMerchantFeat1Description')}
              </Text>
            </Stack>
            <Stack>
              <div>
                <Center>
                  <Icon
                    icon="credit-card"
                    size={50}
                    color={theme.colors.teal[6]}
                  />
                </Center>
                <Text color="white" weight="bold" size="xl">
                  {t('common:apiIntegrationMerchantFeat2Title')}
                </Text>
              </div>
              <Text maw={200} color="dimmed" size="lg" weight={500}>
                {t('common:apiIntegrationMerchantFeat2Description')}
              </Text>
            </Stack>
            <Stack>
              <div>
                <Center>
                  <Icon
                    icon="arrows-exchange"
                    size={50}
                    color={theme.colors.teal[6]}
                  />
                </Center>
                <Text color="white" weight="bold" size="xl">
                  {t('common:apiIntegrationMerchantFeat3Title')}
                </Text>
              </div>
              <Text maw={200} color="dimmed" size="lg" weight={500}>
                {t('common:apiIntegrationMerchantFeat3Description')}
              </Text>
            </Stack>
            <Stack>
              <div>
                <Center>
                  <Icon
                    icon="cash-banknote"
                    size={50}
                    color={theme.colors.teal[6]}
                  />
                </Center>
                <Text color="white" weight="bold" size="xl">
                  {t('common:apiIntegrationMerchantFeat4Title')}
                </Text>
              </div>

              <Text maw={260} color="dimmed" size="lg" weight={500}>
                {t('common:apiIntegrationMerchantFeat4Description')}
              </Text>
            </Stack>
          </SimpleGrid>
        </Stack>
      </BackgroundImage>
    </Container>
  );
}

export default ApiIntegration;
