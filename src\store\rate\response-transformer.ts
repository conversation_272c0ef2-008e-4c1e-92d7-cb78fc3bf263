/**
 * rates response schema
 */
import { z } from 'zod';

export const ratesBackendSchema = z.object({
  data: z.object({
    id: z.number(),
    attributes: z.object({
      rates: z.any(),
      rates_to_usd: z.any(),
      createdAt: z.string(),
      updatedAt: z.string(),
    }),
  }),
});

export const ratesApiResponseSchema = z
  .object({
    data: ratesBackendSchema,
  })
  .transform((data) => ({
    rates: data.data.data.attributes?.rates,
    ratesToUsd: data.data.data.attributes?.rates_to_usd,
    createdAt: data.data.data.attributes.createdAt,
    updatedAt: data.data.data.attributes.updatedAt,
  }));
