/**
 * request params to get gift cards api call
 */
import { NextApiRequest } from 'next';

export const returnGiftsParams = (
  req: NextApiRequest,
  email: string | null | undefined,
) => {
  const params = req.query;
  return {
    'pagination[page]': params.page,
    'pagination[pageSize]': params.pageSize,
    'pagination[start]': params.start,
    'pagination[limit]': params.limit,
    'populate[currency][populate]': '*',
    'filters[merchant][email][$eq]': email,
    'filters[code][$eq]': params?.code,
    sort: 'createdAt:desc',
  };
};
