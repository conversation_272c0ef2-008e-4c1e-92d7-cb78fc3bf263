/**
 * This handler to send email to the user contain a code sent to user to generate new 2fa qr code.
 */
import { httpCode } from '@/data';
import { BackendClient } from '@/lib';
import createApiError from '@/utils/api-utils/create-api-error';
import { getJwt } from '@/utils/api-utils/jwt';
import { setCookie } from 'cookies-next';
import { NextApiRequest, NextApiResponse } from 'next';

async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  const { token } = await getJwt(req);
  if (req.method === 'POST') {
    // generate code from 6 digits and send it to the user email
    const generateCode = Math.floor(100000 + Math.random() * 900000);
    // save generated code in cookie to compare it with then code user will enter it.
    setCookie('email-code', generateCode, { httpOnly: true, req, res });

    try {
      const { data } = await BackendClient(req).post(
        '/email',
        { ...req.body, text: `your code is ${generateCode}` },
        {
          headers: {
            authorization: token,
          },
        },
      );

      return res.status(httpCode.SUCCESS).json(data);
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }
  const error = createApiError({ error: '' });
  return res.status(httpCode.INTERNAL_SERVER_ERROR).json(error);
}
export default handler;
