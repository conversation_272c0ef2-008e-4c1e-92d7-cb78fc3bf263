/**
 * payment method response schema
 */
import { z } from 'zod';
import { globalPaginationBackendSchema } from '@/utils';
import {
  currencyApiResponseSchema,
  currencyBackendSchema,
} from '../currencies';
import { imageBackedSchema } from '../currencies/responses-transformers';
import { CUSTOM_FIELDS_TYPE } from '@/types/custom-fields-type.type';

export const customFieldsSchema = z
  .object({
    name: z.string().nullable(),
    name_ar: z.string().nullable(),
    type: z.nativeEnum(CUSTOM_FIELDS_TYPE).nullable(),
    placeholder: z.string().nullable(),
    placeholder_ar: z.string().nullable(),
    regex: z.string().nullable(),
    list_items: z.array(z.string()).nullable(),
  })
  .optional();
export const customFieldsApiSchema = (
  i: z.infer<typeof customFieldsSchema>,
) => ({
  name: i?.name,
  nameAr: i?.name_ar,
  placeholder: i?.placeholder,
  placeholderAr: i?.placeholder_ar,
  regex: i?.regex,
  type: i?.type,
  listItems: i?.list_items,
});
export const paymentMethodBackendSchema = z.object({
  id: z.number(),
  attributes: z.object({
    title: z.string(),
    title_ar: z.string().nullable(),
    tag: z.enum([
      'cryptocurrency',
      'ccpayment',
      'bank',
      'e-payment',
      'payeer',
      'perfect_money',
      'syriatel_credit',
      'syriatel_cash',
      'syriatel_invoice',
      'mtn_credit',
      'mtn_cash',
      'mtn_invoice',
      'syriatel_two_credit',
      'syriatel_two_cash',
      'syriatel_two_invoice',
      'mtn_two_credit',
      'mtn_two_cash',
      'mtn_two_invoice',
      'haram_glue',
      'haram',
      'haram_usd_cash',
      'haram_usd_cash_hama',
      'haram_usd_cash_homs',
      'haram_cash_damascus_zahera',
      'coinex',
      'cwallet',
      'binance',
      'bemo',
      'agent',
      'internal_agent',
    ]),
    deposit_fees_fixed: z.string(),
    deposit_fees_percentage: z.string(),
    withdraw_fees_fixed: z.string(),
    withdraw_fees_percentage: z.string(),
    deposit_amount_min: z.string(),
    deposit_amount_max: z.string().nullable(),
    withdraw_amount_min: z.string(),
    withdraw_amount_max: z.string().nullable(),
    deposit_fees_min: z.string(),
    deposit_fees_max: z.string().nullable(),
    withdraw_fees_min: z.string(),
    withdraw_fees_max: z.string().nullable(),
    show_in_rates_bar: z.boolean().nullable(),
    deposit_address: z.string().nullable(),
    short_description_withdraw: z.string().nullable(),
    short_description_withdraw_ar: z.string().nullable(),
    description_withdraw: z.string().nullable(),
    description_withdraw_ar: z.string().nullable(),
    short_description_deposit: z.string().nullable(),
    short_description_deposit_ar: z.string().nullable(),
    description_deposit: z.string().nullable(),
    description_deposit_ar: z.string().nullable(),
    deposit_currencies: z
      .object({
        data: z.array(
          z.object({
            id: z.number(),
            attributes: currencyBackendSchema,
          }),
        ),
      })
      .optional(),
    withdraw_currencies: z
      .object({
        data: z.array(
          z.object({
            id: z.number(),
            attributes: currencyBackendSchema,
          }),
        ),
      })
      .optional(),
    icon: z
      .object({
        data: z
          .object({
            id: z.number(),
            attributes: imageBackedSchema,
          })
          .nullable(),
      })
      .optional(),
    createdAt: z.string(),
    updatedAt: z.string(),
    deposit_custom_fields: z.array(customFieldsSchema).optional(),
    withdraw_custom_fields: z.array(customFieldsSchema).optional(),
    additional_fields: z
      .object({
        step: z.number().nullable().optional(),
      })
      .nullable(),
    order: z.number().nullable(),
  }),
});

export const paymentMethodsBackendSchema = z.array(paymentMethodBackendSchema);

export const paymentMethodsApiResponseSchema = z
  .object({
    data: paymentMethodsBackendSchema,
    meta: globalPaginationBackendSchema,
  })
  .transform(({ data, meta }) => ({
    data: data?.map((item) => ({
      id: item.id,
      value: `${item.id}`,
      label: item.attributes.title,
      labelAr: item.attributes?.title_ar,
      image:
        item.attributes?.icon?.data?.attributes?.formats?.thumbnail.url
        ?? item.attributes?.icon?.data?.attributes.url,
      tag: item.attributes.tag,
      depositFeesFixed: +item.attributes.deposit_fees_fixed,
      depositFeesPercentage: +item.attributes.deposit_fees_percentage,
      withdrawFeesFixed: +item.attributes.withdraw_fees_fixed,
      withdrawFeesPercentage: +item.attributes.withdraw_fees_percentage,
      depositAmountMin: +item.attributes.deposit_amount_min,
      depositAmountMax: item.attributes?.deposit_amount_max
        ? +item.attributes.deposit_amount_max
        : null,
      withdrawAmountMin: +item.attributes.withdraw_amount_min,
      withdrawAmountMax: item.attributes?.withdraw_amount_max
        ? +item.attributes.withdraw_amount_max
        : null,
      depositFeesMin: +item.attributes.deposit_fees_min,
      depositFeesMax: item.attributes.deposit_fees_max
        ? +item.attributes.deposit_fees_max
        : null,
      withdrawFeesMin: +item.attributes.withdraw_fees_min,
      withdrawFeesMax: item.attributes.withdraw_fees_max
        ? +item.attributes.withdraw_fees_max
        : null,
      showInRatesBar: item.attributes?.show_in_rates_bar,
      depositAddress: item.attributes?.deposit_address,
      shortDescriptionDeposit: item.attributes?.short_description_deposit,
      shortDescriptionDepositAr: item.attributes?.short_description_deposit_ar,
      descriptionDeposit: item.attributes?.description_deposit,
      descriptionDepositAr: item.attributes?.description_deposit_ar,
      shortDescriptionWithdraw: item.attributes?.short_description_withdraw,
      shortDescriptionWithdrawAr:
        item.attributes?.short_description_withdraw_ar,
      descriptionWithdraw: item.attributes?.description_withdraw,
      descriptionWithdrawAr: item.attributes?.description_withdraw_ar,
      depositCurrencies:
        item.attributes?.deposit_currencies?.data?.map((i) => ({
          id: i.id,
          paymentMethodTitle: item.attributes.title,
          paymentMethodTitleAr: item.attributes.title_ar,
          paymentMethodIcon:
            item.attributes?.icon?.data?.attributes?.formats?.thumbnail.url
            ?? item.attributes?.icon?.data?.attributes.url,
          PaymentMethodFeesFixed: +item.attributes.deposit_fees_fixed,
          PaymentMethodFeesPercentage: +item.attributes.deposit_fees_percentage,
          ...currencyApiResponseSchema(i.attributes),
        })) ?? [],
      withdrawCurrencies:
        item.attributes?.withdraw_currencies?.data?.map((i) => ({
          id: i.id,
          paymentMethodTitle: item.attributes.title,
          paymentMethodTitleAr: item.attributes.title_ar,
          paymentMethodIcon:
            item.attributes?.icon?.data?.attributes?.formats?.thumbnail.url
            ?? item.attributes?.icon?.data?.attributes.url,
          PaymentMethodFeesFixed: +item.attributes.deposit_fees_fixed,
          PaymentMethodFeesPercentage: +item.attributes.deposit_fees_percentage,
          ...currencyApiResponseSchema(i?.attributes),
        })) ?? [],
      createdAt: item.attributes.createdAt,
      updatedAt: item.attributes.updatedAt,
      depositCustomFields: item.attributes?.deposit_custom_fields?.map((i) => customFieldsApiSchema(i)),
      withdrawCustomFields: item.attributes?.withdraw_custom_fields?.map((i) => customFieldsApiSchema(i)),
      additionalFields: item.attributes?.additional_fields,
      order: item.attributes?.order,
    })),
    pagination: {
      page: meta.pagination?.page,
      pageSize: meta.pagination?.pageSize,
      pageCount: meta.pagination?.pageCount,
      total: meta.pagination.total,
    },
  }));
