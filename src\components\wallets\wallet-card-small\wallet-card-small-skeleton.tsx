import { GlobalCard } from '@/components/common/global-card';
import {
  Stack, Group, Skeleton,
} from '@mantine/core';

export function WalletCardSmallSkeleton() {
  return (
    <GlobalCard
      props={{}}
      onClick={() => {}}
    >
      <Group position="apart">
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
          <Skeleton height={40} width={40} circle />
          <div style={{ textAlign: 'start' }}>
            <Skeleton mb="xs" height={20} width={80} />
            <Skeleton height={20} width={90} />
          </div>
        </div>
        <Stack align="end" spacing={2}>
          <Skeleton mb="xs" height={20} width={80} />
          <Skeleton height={20} width={90} />
        </Stack>
      </Group>
    </GlobalCard>
  );
}
