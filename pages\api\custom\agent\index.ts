/**
 * This handler to get total user balance.
 */
import { apiEndpoints, httpCode, playCaptcha } from '@/data';
import { BackendClient } from '@/lib';
import { createAgentPaymentBackendRequestSchema } from '@/store/agent';
import { agentPaymentApiResponseSchema } from '@/store/agent/responses-transformers';
import { paymentApiResponseSchema } from '@/store/payment';
import createApiError from '@/utils/api-utils/create-api-error';
import createApiResponse from '@/utils/api-utils/create-api-response';
import { getJwt } from '@/utils/api-utils/jwt';
import { captchaValidator } from '@/utils/captcha-validator';
import { NextApiRequest, NextApiResponse } from 'next';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token } = await getJwt(req);
  const { id } = req.query;
  if (req.method === 'POST' && id) {
    try {
      const chaKey = req?.query?.chaKey;
      await captchaValidator({
        token: chaKey as string,
        secret: process.env.RECAPTCHA_SECRET_KEY as string,
        active: playCaptcha,
      });
      const { data } = await BackendClient(req).get(
        `${apiEndpoints.agent}/get-withdraw-payment?related_withdraw_id=${id}`,
        {
          headers: {
            authorization: token,
          },
        },
      );
      return createApiResponse(res, agentPaymentApiResponseSchema, {
        data: data?.payment,
      });
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  } else if (req.method === 'POST') {
    try {
      // get token "chaKey" returned from captha and pass it to "captchaValidator" to check toke validity
      // if validation success will call  api,else return error.
      const chaKey = req?.query?.chaKey;
      await captchaValidator({
        token: chaKey as string,
        secret: process.env.RECAPTCHA_SECRET_KEY as string,
        active: playCaptcha,
      });

      // create payment function
      const { data } = await BackendClient(req).post(
        apiEndpoints.payments(),
        createAgentPaymentBackendRequestSchema.parse(req.body),
        {
          headers: {
            authorization: token,
          },
          params: {
            'populate[currency][populate]': '*',
            'populate[payment_method][populate]': '*',
            'populate[custom_fields][populate]': '*',
          },
        },
      );
      return res
        .status(httpCode.SUCCESS)
        .json(paymentApiResponseSchema(data?.data));
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }
  const error = createApiError({ error: '' });
  return res.status(httpCode.INTERNAL_SERVER_ERROR).json(error);
}

export default handler;
