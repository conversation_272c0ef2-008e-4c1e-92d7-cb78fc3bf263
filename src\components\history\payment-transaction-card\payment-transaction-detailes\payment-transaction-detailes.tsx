import { PaymentsApiResponse } from '@/store/payment';
import { TransfersApiResponse } from '@/store/transfer/types';
import {
  Badge,
  Group,
  Modal,
  ScrollArea,
  Stack,
  Text,
  UnstyledButton,
  useMantineTheme,
} from '@mantine/core';
import { useDisclosure, useInterval, useMediaQuery } from '@mantine/hooks';
import dayjs from 'dayjs';
import useTranslation from 'next-translate/useTranslation';
import { ReactNode, useEffect } from 'react';
import ExchangeDetails from './exchange-details';
import TransferDetails from './transfer-details';
import WithdrawDepositDetails from './withdraw-deposit-details';
import { CustomCopyButton } from '@/components/common/custom-copy-button';
import { Icon } from '@/components/common/icon';
import { Item } from '@/components/pop-up-success/pop-up-item';
import { RenderCustomFields } from '@/components/custom-fields/render-custom-fields';
import { CryptoPaymentDetails } from '@/components/crypto-payment-details';
import { useQueryClient } from '@tanstack/react-query';
import { TextRenderHtml } from '@/components/common/text-with-html-value';
import classes from './style.module.css';

interface Props {
  children: ReactNode;
  id: string | null;
  amount: number;
  actualAmount: PaymentsApiResponse['data'][0]['actualAmount'];
  status: PaymentsApiResponse['data'][0]['status'] | undefined;
  note: string | undefined;
  gif: string | null;
  userFrom:
    | PaymentsApiResponse['data'][0]['user']
    | TransfersApiResponse['data'][0]['userFrom']
    | undefined;
  userTo: TransfersApiResponse['data'][0]['userTo'] | undefined;
  currencyFrom:
    | PaymentsApiResponse['data'][0]['currency']
    | TransfersApiResponse['data'][0]['currencyFrom']
    | undefined;
  currencyTo: TransfersApiResponse['data'][0]['currencyTo'] | undefined;
  paymentMethod: PaymentsApiResponse['data'][0]['paymentMethod'] | undefined;
  date: string;
  operationType:
    | 'deposit'
    | 'transfer'
    | 'withdraw'
    | 'exchange'
    | 'received'
    | string;
  actualType:
    | 'deposit'
    | 'transfer'
    | 'withdraw'
    | 'exchange'
    | 'received'
    | 'payment_link'
    | 'gift_card'
    | string;
  operationIcon: string;
  statusIcon: { color: string; icon: string };
  fees: number | undefined;
  fields: PaymentsApiResponse['data'][0]['fields'] | undefined;
  rate: number;
  adminMessage: string | null;
  cryptoGetaway: PaymentsApiResponse['data'][0]['cryptoGetaway'] | undefined;
  originPaymentAmount:
    | PaymentsApiResponse['data'][0]['originPaymentAmount']
    | undefined;
}
// eslint-disable-next-line complexity
export default function PaymentDetails({
  children,
  id,
  status,
  note,
  userFrom,
  userTo,
  currencyFrom,
  currencyTo,
  actualAmount,
  amount,
  paymentMethod,
  date,
  operationIcon,
  operationType,
  statusIcon,
  fees,
  fields,
  rate,
  adminMessage,
  actualType,
  gif,
  cryptoGetaway,
  originPaymentAmount,
}: Props) {
  const [opened, { open, close }] = useDisclosure(false);
  const { t } = useTranslation();
  const theme = useMantineTheme();
  const queryClient = useQueryClient();
  const matches = useMediaQuery('(max-width: 34em)');
  const interval = useInterval(
    () => queryClient.invalidateQueries(['payments']),
    30 * 1000,
  );
  // stop interval when close page
  useEffect(
    () => interval.stop,
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [],
  );
  // boolean return true if type of payment currency is "crypto" ,used to display crypto payment details
  const isCryptoPayment = paymentMethod?.tag === 'cryptocurrency'
    && operationType === 'deposit'
    && cryptoGetaway;
  // boolean return true if the operation type is "transfer" or "received" or "payment_link"
  // used to display details for these operations types
  const isTransfer = operationType === 'transfer'
    || operationType === 'received'
    || operationType === 'payment_link';

  // onClick to transaction card will oped transaction details popup
  // if type or transaction is "deposit" or "withdraw" will start interval to refetch payments every 30 second.
  // else just open transaction details popup.
  const onClickToTransactionCard = () => {
    open();
    if (!isTransfer) interval.start();
  };
  // onCloses transaction details popup will stop interval and close popup
  const onCloseDetailsModal = () => {
    interval.stop();
    close();
  };
  return (
    <div>
      <UnstyledButton w="100%" onClick={onClickToTransactionCard}>
        {children}
      </UnstyledButton>
      <Modal
        fullScreen={matches}
        zIndex={100}
        radius={30}
        opened={opened}
        onClose={onCloseDetailsModal}
        scrollAreaComponent={ScrollArea.Autosize}
        title=""
        centered
        classNames={{
          content: matches ? classes.content : '',
        }}
        sx={{
          '& .mantine-ltr-Modal-close ,& .mantine-rtl-Modal-close': {
            border: '1px solid',
            borderRadius: 8,
            width: 25,
            height: 25,
            color: theme.colorScheme === 'dark' ? '#FFF' : '#000',
          },
        }}
      >
        <Stack pl={matches ? 'md' : '0'}>
          {isCryptoPayment && (
            <CryptoPaymentDetails
              amount={cryptoGetaway?.amount}
              currencyCode={cryptoGetaway?.currency}
              address={cryptoGetaway?.address}
              network={cryptoGetaway?.network}
              memo={cryptoGetaway?.memo}
              validityDate={cryptoGetaway?.validity}
              qrCode={cryptoGetaway?.qrCode}
              status={status}
              createdAt={date}
            />
          )}

          {isTransfer && (
            <TransferDetails
              actualAmount={actualAmount}
              amount={amount}
              currencyFrom={currencyFrom}
              fees={fees}
              operationType={operationType}
              userFrom={userFrom}
              userTo={userTo}
              gif={gif}
            />
          )}
          {operationType === 'exchange' && (
            <ExchangeDetails
              actualAmount={actualAmount}
              amount={amount}
              currencyFrom={currencyFrom}
              currencyTo={currencyTo}
              fees={fees}
              rate={rate}
            />
          )}
          {(operationType === 'withdraw' || operationType === 'deposit') && (
            <WithdrawDepositDetails
              adminMessage={adminMessage}
              actualAmount={actualAmount}
              amount={amount}
              currencyFrom={currencyFrom}
              fees={fees}
              operationType={operationType}
              paymentMethod={paymentMethod}
              originPaymentAmount={originPaymentAmount}
            />
          )}
          <Group>
            <Item
              align="center"
              name="operationNo"
              content={<Text tt="uppercase">{id}</Text>}
            />
            <CustomCopyButton value={id ?? ''} />
          </Group>
          <Item
            align="center"
            name="operationType"
            content={(
              <>
                <Icon icon={operationIcon} size={24} color="gray" />
                <Text weight={500} tt="uppercase">
                  {t(
                    `common:${
                      operationType === 'payment_link'
                        ? 'transfer'
                        : operationType
                    }`,
                  )}
                </Text>
              </>
            )}
          />
          <Item
            align="center"
            name="status"
            content={(
              <>
                <Badge
                  p={0}
                  w={25}
                  h={25}
                  sx={{ borderWidth: '0.15rem' }}
                  variant="outline"
                  radius={50}
                  color={statusIcon?.color}
                >
                  <Group>
                    <Icon icon={statusIcon?.icon} size={16} color="dark" />
                  </Group>
                </Badge>
                <Badge color={statusIcon?.color}>{t(`common:${status}`)}</Badge>
              </>
            )}
          />
          <Item
            align="center"
            name="date"
            content={
              <Text size="sm">{dayjs(date).format('YY-MM-DD hh:mm:ss A')}</Text>
            }
          />
          {note && (
            <Item
              align="start"
              name="note"
              content={<TextRenderHtml text={note} />}
            />
          )}
          {actualType === 'payment_link' && (
            <Text>
              {t('common:byPaymentLink')}
              {' '}
              <span
                style={{
                  textTransform: 'capitalize',
                  fontSize: 'lg',
                  fontWeight: 500,
                  color: '#79CA53',
                }}
              >
                {userTo?.fullName ?? ''}
              </span>
            </Text>
          )}

          <RenderCustomFields fields={fields} />
        </Stack>
      </Modal>
    </div>
  );
}
