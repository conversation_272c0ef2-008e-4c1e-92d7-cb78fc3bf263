/**
 * This handler to resend otp to user email to unblock his account.
 */
import { apiEndpoints, httpCode } from '@/data';
import { BackendClient } from '@/lib';
import createApiError from '@/utils/api-utils/create-api-error';
import { getJwt } from '@/utils/api-utils/jwt';
import { NextApiRequest, NextApiResponse } from 'next';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token } = await getJwt(req);
  if (req.method === 'POST') {
    try {
      const { data } = await BackendClient(req).post(
        apiEndpoints.resendUnblockAccountOtpCode(),
        {},
        {
          headers: {
            Authorization: token,
          },
        },
      );
      return res.status(httpCode.SUCCESS).json(data);
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }
  const error = createApiError({ error: '' });
  return res.status(httpCode.INTERNAL_SERVER_ERROR).json(error);
}

export default handler;
