/**
 * request params to get payments api call
 */
import { NextApiRequest } from 'next';

export const returnPaymentsParams = (
  req: NextApiRequest,
  email: string | null | undefined,
) => {
  const params = req.query;
  return {
    'pagination[page]': params.page,
    'pagination[pageSize]': params.pageSize,
    'pagination[start]': params.start,
    'pagination[limit]': params.limit,
    'filters[type][$eq]': params?.type,
    'filters[status][$eq]': params?.status,
    'filters[currency][id][$eq]': params?.currencyId,
    'filters[currency][uid][$eq]': params?.currencyUid,
    'filters[payment_method][id][$eq]': params?.paymentMethod,
    'populate[currency][populate]': '*',
    'populate[payment_method][populate]': '*',
    'populate[custom_fields][populate]': '*',
    'filters[user][email][$eq]': email,
    'filters[transaction_id][$containsi]': params?.search,
    'filters[createdAt][$gte]':
      typeof params?.createdFrom === 'string' && params?.createdFrom !== ''
        ? params?.createdFrom
        : null,
    'filters[createdAt][$lte]':
      typeof params?.createdTo === 'string' && params?.createdTo !== ''
        ? params?.createdTo
        : null,
    sort: 'createdAt:desc',
    publicationState: 'preview',
  };
};
