/* eslint-disable max-lines */
import {
  Stack,
  Textarea,
  TextInput,
  SimpleGrid,
  Text,
  Accordion,
  rem,
  Button,
  Alert,
  Group,
  ActionIcon,
} from '@mantine/core';
import { useForm, zodResolver } from '@mantine/form';
import { useDisclosure } from '@mantine/hooks';
import React, { useState } from 'react';
import useTranslation from 'next-translate/useTranslation';
import { IconEye, IconAlertCircle } from '@tabler/icons-react';

import currency from 'currency.js';
import { PaymentMethodsApiResponse } from '@/store/payment-methods';
import { useMutation, useQuery } from '@tanstack/react-query';
import { RatesApiResponse } from '@/store/rate';
import { UserApiResponse } from '@/store/user';
import { FeesTransferCalculate } from '@/utils/fees-functions/fees-transfer-withdraw-deposit';
import {
  PaymentsApiResponse,
  createPaymentApiRequestSchema,
  createPaymentMutation,
  getPaymentQuery,
} from '@/store/payment';

import { customFieldsErrorValidate } from '@/utils/validate-custom-fields-error';
import MarkdownView from 'react-showdown';
import {
  getTranslatedTextValue,
  minMaxError,
  validateAmountStep,
} from '@/utils';
import MinMaxAmount from './min-max';
import { CustomCopyButton } from '@/components/common/custom-copy-button';
import { Icon } from '@/components/common/icon';
import { CustomFieldsForm } from '@/components/custom-fields';
import { PopUpSuccess } from '@/components/pop-up-success';

import { AmountInputDepositWithdraw } from '@/components/amount-input-deposit-withdraw';
import SubmitButton from '@/components/common/submit-button';
import { TransactionConfirmModal } from '@/components/common/transaction-confirm-modal';
import WithdrawDepositDetails from '@/components/history/payment-transaction-card/payment-transaction-detailes/withdraw-deposit-details';
import { RenderCustomFields } from '@/components/custom-fields/render-custom-fields';
import { Captcha } from '@/components/common/captcha';
import { useCaptcha } from '@/hooks';
import { returnDefaultCustomFieldValue } from '@/components/custom-fields/render-custom-fileds-value';
import { LimitsErrorKeys } from '@/types/error.type';

import { ErrorPopup } from '@/components/common/errorr-poup';
import Link from 'next/link';
import { playCaptcha, ROUTES } from '@/data';
import { AgentsList } from '@/components/agents/agents-list';
import { TranslatedTextValue } from '@/components/common/translated-text-value';
import { useRouter } from 'next/router';

interface Props {
  selectedPaymentMethod: PaymentMethodsApiResponse['data'][0];
  selectedCurrency: UserApiResponse['currencies'][0];
  rates: RatesApiResponse['rates'];
}
// eslint-disable-next-line complexity
function DepositFormInner({
  selectedPaymentMethod,
  selectedCurrency,
  rates,
}: Props) {
  const { t } = useTranslation();
  const { locale } = useRouter();
  const [opened, { open, close }] = useDisclosure(false);
  const [openLimitPopup, setLimitPopup] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const [responseData, setResponseData] = useState<PaymentsApiResponse['data'][0]>();
  const [showDepositAddress, setShowDepositAddress] = useState(false);
  // boolean to return true if payment method has "internal_agent" tag to handle list agents viewing and form actions
  const isInternalAgentPaymentMethod = selectedPaymentMethod?.tag === 'internal_agent';

  const { execute, reCaptchaRef, reset } = useCaptcha();

  // create payment form type"deposit"
  const form = useForm({
    initialValues: {
      amount: '' as number | string,
      note: '',
      status: 'pending',
      currencyId: '',
      paymentMethodId: '',
      type: 'deposit',
      // return payment method deposit custom fields to initial "fields" in form
      fields: selectedPaymentMethod.depositCustomFields?.map((i) => ({
        type: i?.type,
        name: i?.name,
        nameAr: i?.nameAr,
        // this function to initial value by type of custom field
        value: returnDefaultCustomFieldValue(i?.type),
      })),
    },
    validate: zodResolver(createPaymentApiRequestSchema),
  });
  // create payment mutation
  const { mutate } = useMutation(createPaymentMutation().mutationFn, {
    onSuccess: (data) => {
      setResponseData(data);
      close();
      form.reset();
      reset();
      setLoading(false);
    },
    onError: ({ response }) => {
      if (
        response?.data?.message?.key === LimitsErrorKeys.ERROR_PASS_THE_LIMIT
      ) {
        setLimitPopup(true);
      }
      close();
      reset();
      setLoading(false);
    },
  });
  // return boolean to enabled or disabled get payment fetch
  // to return true should be payment created to get id,type of currency is "crypto" and status of payment is "pending" or"processing
  const isEnabled = !!responseData?.id
    && responseData?.currency?.type === 'crypto'
    && (responseData?.status === 'pending'
      || responseData.status === 'processing');
  // fetch payment by id
  // this function called to check payment status every 30 second
  // will be enabled just if the payment currency type was "crypto" ,and after complete the payment form submitting
  const { isFetching: loadingPaymentData } = useQuery({
    ...getPaymentQuery(responseData?.id, {
      filters: {
        type: 'deposit',
      },
    }),
    enabled: isEnabled,
    refetchInterval: 30 * 1000, // refetch payment data each 30 second
    onSuccess(response) {
      setResponseData(response);
    },
  });

  // submit function
  function submit() {
    setLoading(true);
    const body = {
      body: {
        ...form.values,
        currencyId: selectedCurrency?.id ?? '',
        paymentMethodId: selectedPaymentMethod?.id,
      },
    };
    if (reCaptchaRef.current) {
      execute((token) => mutate({
        ...body,
        chaKey: token,
      }));
    } else {
      mutate(body);
    }
  }

  // boolean return true if the amount greater than max or less than min
  const isMinMaxError = minMaxError({
    amount: form?.values?.amount,
    maxAmount: selectedPaymentMethod.depositAmountMax,
    minAmount: selectedPaymentMethod?.depositAmountMin,
    currencyAmount: undefined,
  });

  // return fees after calculate it
  const fees = FeesTransferCalculate(
    selectedPaymentMethod?.depositFeesFixed,
    selectedPaymentMethod?.depositFeesPercentage,
    selectedPaymentMethod?.depositFeesMin,
    selectedPaymentMethod?.depositFeesMax,
    +form.values.amount,
  );
  // boolean return true if amount step deferent about the payment method step
  const isAmountStepError = validateAmountStep(
    selectedPaymentMethod?.additionalFields?.step,
    form?.values?.amount,
  );
  // return currency precision
  const precision = selectedCurrency?.precision ?? 2;
  // determine if currency is crypto
  const isCrypto = ['crypto', 'crypto_stable'].includes(selectedCurrency?.type);
  // get the appropriate rate based on currency type
  const rateKey = isCrypto
    ? `${selectedCurrency.code}_TO`
    : `${selectedCurrency.code}_FROM`;
  // amount usd price
  const usdPrice = rates[rateKey]
    ? currency((1 / rates[rateKey]) * +form.values.amount, {
      symbol: '',
    }).format()
    : 0;

  // to be deposit amount
  const toBeDeposit = currency(+form.values.amount - fees, {
    symbol: '',
    precision,
  }).format();
  // this boolean will return true if the deposit amount is less than 0
  // to be deposit amount = amount - fee
  const isToBeDepositError = typeof form.values.amount === 'number' && form.values.amount - fees <= 0;
  // return selected payment method custom fields
  const returnPaymentMethodCustomFields = selectedPaymentMethod?.depositCustomFields?.map((i, index) => ({
    ...i,
    value: form.values?.fields ? form.values?.fields[index]?.value : '',
  }));
  // conditions to disabled and enabled submit button
  const isSubmitButtonDisabled = typeof form.values.amount !== 'number'
    || form?.values?.amount <= 0
    || form.values.amount - fees <= 0
    || isMinMaxError
    || isAmountStepError;

  // onSubmit function
  // validate custom fields errors
  // open confirm popup
  const onSubmit = () => {
    customFieldsErrorValidate(
      form,
      selectedPaymentMethod?.depositCustomFields,
      open,
    );
  };

  return (
    <>
      <form>
        <Stack my="md" mx="auto">
          <MinMaxAmount
            minAmount={selectedPaymentMethod?.depositAmountMin}
            maxAmount={selectedPaymentMethod?.depositAmountMax}
            symbol={selectedCurrency?.symbol}
            currencyPrecision={selectedCurrency?.precision}
          />
          {!isInternalAgentPaymentMethod && (
            <>
              <AmountInputDepositWithdraw
                operationType="deposit"
                form={form}
                isMinMaxError={isMinMaxError}
                precision={precision}
                selectedPaymentMethod={selectedPaymentMethod}
              />
              <SimpleGrid cols={3} spacing="xs">
                <TextInput
                  disabled
                  readOnly
                  radius="lg"
                  size="sm"
                  label={t('common:usdPrice')}
                  value={usdPrice}
                />
                <TextInput
                  disabled
                  readOnly
                  radius="lg"
                  size="sm"
                  value={currency(fees, { symbol: '', precision }).format()}
                  label={
                    <Text tt="capitalize">{t('common:paymentMethodFees')}</Text>
                  }
                />
                <div>
                  <TextInput
                    readOnly
                    radius="lg"
                    size="sm"
                    value={toBeDeposit}
                    label={
                      <Text tt="capitalize">{t('common:toBeDeposit')}</Text>
                    }
                  />
                  {isToBeDepositError && (
                    <Text mx={2} color="red" size="sm">
                      {t('common:toBeDepositError')}
                    </Text>
                  )}
                </div>
              </SimpleGrid>
            </>
          )}
          {selectedPaymentMethod?.depositAddress && (
            <Stack spacing="sm">
              <Alert
                icon={<IconAlertCircle size="1rem" />}
                color="yellow"
                radius="lg"
              >
                <Group position="apart" align="flex-start" spacing="md">
                  <Text size="sm" style={{ flex: 1, lineHeight: 1.4 }}>
                    {t('common:depositAddressWarning')}
                  </Text>
                  {!showDepositAddress && (
                    <ActionIcon
                      variant="outline"
                      color="blue"
                      size="sm"
                      onClick={() => setShowDepositAddress(true)}
                      style={{ flexShrink: 0, marginTop: 2 }}
                    >
                      <IconEye size="0.875rem" />
                    </ActionIcon>
                  )}
                </Group>
              </Alert>
              {showDepositAddress && (
                <TextInput
                  readOnly
                  radius="lg"
                  size="sm"
                  value={selectedPaymentMethod?.depositAddress}
                  rightSection={(
                    <CustomCopyButton
                      value={selectedPaymentMethod?.depositAddress}
                    />
                  )}
                  label={t('common:depositAddress')}
                />
              )}
            </Stack>
          )}
          {!isInternalAgentPaymentMethod && (
            <CustomFieldsForm
              form={form}
              fields={returnPaymentMethodCustomFields}
            />
          )}
          <Text>
            <TranslatedTextValue
              keyEn={selectedPaymentMethod?.shortDescriptionDeposit ?? ''}
              keyAr={selectedPaymentMethod?.shortDescriptionDepositAr}
            />
          </Text>
          <Accordion radius="lg" variant="contained" defaultValue="description">
            <Accordion.Item value="description">
              <Accordion.Control
                icon={<Icon icon="info-circle" size={rem(20)} color="gray" />}
              >
                {t('common:description')}
              </Accordion.Control>
              <Accordion.Panel>
                <MarkdownView
                  markdown={getTranslatedTextValue(
                    locale,
                    selectedPaymentMethod?.descriptionDeposit ?? '',
                    selectedPaymentMethod?.descriptionDepositAr,
                  )}
                  options={{ tables: true, emoji: true }}
                />
              </Accordion.Panel>
            </Accordion.Item>
          </Accordion>

          {isInternalAgentPaymentMethod && (
            <AgentsList
              withDescription={false}
              viewAs="list"
              currencyCode={selectedCurrency?.code}
            />
          )}
          {!isInternalAgentPaymentMethod && (
            <>
              <Textarea
                size="sm"
                radius="lg"
                placeholder={t('common:note') ?? ''}
                label={<Text tt="capitalize">{t('common:note')}</Text>}
                {...form.getInputProps('note')}
              />
              <SubmitButton
                disabled={isSubmitButtonDisabled}
                fullWidth
                onClick={onSubmit}
              >
                {t('common:deposit')}
              </SubmitButton>
            </>
          )}
        </Stack>
        <TransactionConfirmModal
          close={close}
          openedDefault={opened}
          isLoading={isLoading}
          onClick={() => submit()}
          operationType="deposit"
        >
          <WithdrawDepositDetails
            adminMessage={null}
            actualAmount={toBeDeposit}
            amount={form.values.amount as number}
            currencyFrom={selectedCurrency}
            fees={fees}
            operationType="deposit"
            paymentMethod={selectedPaymentMethod}
            originPaymentAmount={undefined}
          />
          <RenderCustomFields fields={form.values.fields} />
        </TransactionConfirmModal>
        {responseData && (
          <PopUpSuccess
            currencySymbol={responseData?.currency?.symbol}
            currencyToSymbol=""
            operationType="deposit"
            setResponseData={setResponseData}
            operationId={responseData?.transactionId}
            amount={responseData?.amount}
            actualAmount={responseData?.actualAmount}
            fees={responseData?.totalFees}
            status={responseData?.status}
            note={responseData?.note}
            date={responseData?.createdAt}
            sender={undefined}
            receiver={undefined}
            currencyCode={responseData?.currency?.code}
            currencyToCode={undefined}
            paymentMethod={responseData?.paymentMethod}
            fields={responseData?.fields}
            rate={0}
            precision={responseData?.currency?.precision}
            currencyToPrecision={2}
            gif={null}
            cryptoGetaway={responseData?.cryptoGetaway}
            originPaymentAmount={responseData?.originPaymentAmount}
            isLoading={loadingPaymentData}
            adminMessage={responseData?.adminMessage}
          />
        )}

        <ErrorPopup
          open={openLimitPopup}
          setOpen={setLimitPopup}
          message={t('errors:limit-error')}
          actionButton={(
            <Button
              fullWidth
              radius="lg"
              component={Link}
              href={`${ROUTES.myAccount.path}?page=limits`}
            >
              {t('common:goToLimits')}
            </Button>
          )}
        />
      </form>
      <Captcha reCaptchaRef={reCaptchaRef} active={playCaptcha} />
    </>
  );
}

// Wrapper that uses key to force remount when payment method changes
export default function DepositForm({
  selectedPaymentMethod,
  selectedCurrency,
  rates,
}: Props) {
  // Use payment method ID as key to force component remount when payment method changes
  return (
    <DepositFormInner
      key={selectedPaymentMethod?.id}
      selectedPaymentMethod={selectedPaymentMethod}
      selectedCurrency={selectedCurrency}
      rates={rates}
    />
  );
}
