/**
 * This component is same history page but this route we can use it in iframe
 *
 */
import MetaTags from '@/components/common/meta-tags';

import {
  Container,
  MantineTheme,
  Stack,
  Tabs,
  TabsValue,
  Text,
} from '@mantine/core';

import React, { useState } from 'react';
import useTranslation from 'next-translate/useTranslation';

import { useInfiniteQuery, useQuery } from '@tanstack/react-query';
import { getPaymentsQuery } from '@/store/payment';
import TabDataContents from '@/components/history/tab-data-contents/tab-data-contents';

import { operationIcon } from '@/utils/operations-utils/operation-icons';
import { getUserQuery } from '@/store/user';
import { Icon } from '@/components/common/icon';
import { PageTitle } from '@/components/common/page-title';
import { useRouter } from 'next/router';
import { EmptyData } from '@/components/common/empty-data';

function History() {
  const { t } = useTranslation();
  const { query } = useRouter();
  const { data } = useQuery(getUserQuery({}));

  const [paymentTypeFilter, setPaymentTypeFilter] = useState<
    'deposit' | 'withdraw' | null
  >(null);
  const paymentsDataQuery = useInfiniteQuery(
    getPaymentsQuery({
      pagination: {
        pageSize: 10,
      },
      filters: {
        type: paymentTypeFilter,
        currencyUid: query?.currency,
      },
    }),
  );

  const handleTabsChange = (v: TabsValue) => {
    if (v === 'deposit') {
      setPaymentTypeFilter('deposit');
    } else if (v === 'withdraw') {
      setPaymentTypeFilter('withdraw');
    } else if (v === 'all') {
      setPaymentTypeFilter(null);
    }
  };
  // common style for all tabs
  const tabsStyle = (theme: MantineTheme) => ({
    [theme.fn.smallerThan('xs')]: {
      '& .mantine-ltr-Tabs-tabLabel ,& .mantine-rtl-Tabs-tabLabel': {
        display: 'none',
      },
      '& .mantine-ltr-Tabs-tabIcon ,& .mantine-rtl-Tabs-tabIcon': {
        margin: 'auto',
      },
      '& .mantine-ltr-UnstyledButton-root ,& .mantine-rtl-UnstyledButton-root':
        {
          padding: 10,
        },
    },
  });
  if (!query?.currency && !paymentsDataQuery.isLoading) {
    return (
      <Stack h="100vh" justify="center">
        <EmptyData message="" />
      </Stack>
    );
  }
  return (
    <div>
      <MetaTags title={t('common:history')} />
      <Container pt="xl" pb="md" px="sm" size="lg">
        <PageTitle title="history" />
        <Stack mt="md" mx="auto" maw={800}>
          <Tabs
            defaultValue="all"
            onTabChange={handleTabsChange}
            sx={tabsStyle}
          >
            <Tabs.List>
              <Tabs.Tab
                value="all"
                icon={(
                  <Icon
                    icon="baseline-density-small"
                    size="1.2rem"
                    color="dark"
                  />
                )}
              >
                {t('common:all')}
              </Tabs.Tab>
              <Tabs.Tab
                value="deposit"
                icon={(
                  <Icon
                    icon={operationIcon({ operationType: 'deposit' })}
                    color="gray"
                    size="1.3rem"
                  />
                )}
              >
                {t('common:deposit')}
              </Tabs.Tab>
              <Tabs.Tab
                value="withdraw"
                icon={(
                  <Icon
                    icon={operationIcon({ operationType: 'withdraw' })}
                    color="gray"
                    size="1.3rem"
                  />
                )}
              >
                {t('common:withdraw')}
              </Tabs.Tab>
            </Tabs.List>
            <Tabs.Panel value="all" pt="xs">
              <Text mb="xs" tt="capitalize">
                {t('common:recentTransactions')}
              </Text>
              <TabDataContents
                userAccountId={data?.accountId ?? ''}
                dataType="payment"
                dataQueryProps={paymentsDataQuery}
                mixData={null}
              />
            </Tabs.Panel>
            <Tabs.Panel value="deposit" pt="xs">
              <Text mb="xs" tt="capitalize">
                {t('common:recentDepositTransactions')}
              </Text>
              <TabDataContents
                userAccountId={data?.accountId ?? ''}
                dataType="payment"
                dataQueryProps={paymentsDataQuery}
                mixData={null}
              />
            </Tabs.Panel>
            <Tabs.Panel value="withdraw" pt="xs">
              <Text mb="xs" tt="capitalize">
                {t('common:recentWithdrawTransactions')}
              </Text>
              <TabDataContents
                userAccountId={data?.accountId ?? ''}
                dataType="payment"
                dataQueryProps={paymentsDataQuery}
                mixData={null}
              />
            </Tabs.Panel>
          </Tabs>
        </Stack>
      </Container>
    </div>
  );
}

export default History;

export async function getServerSideProps() {
  return { props: {} };
}
