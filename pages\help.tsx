/**
 * This component renders help page
 * @description

 */

import MetaTags from '@/components/common/meta-tags';
import HeroHelp from '@/components/help/hero';
import { Layout } from '@/components/layout/layout';

import useTranslation from 'next-translate/useTranslation';

import { useRouter } from 'next/router';
import Script from 'next/script';
import React, { useEffect } from 'react';

function Help() {
  const { t } = useTranslation();
  const { pathname } = useRouter();

  useEffect(() => {
    const chatElement = document.getElementById('trengo-web-widget');
    if (chatElement) chatElement.style.display = 'block';
    return () => {
      const newChatElement = document.getElementById('trengo-web-widget');
      if (newChatElement) newChatElement.style.display = 'none';
    };
  }, [pathname]);
  return (
    <>
      <MetaTags title={t('common:helpCenter')} />
      <Script async id="trengo" strategy="lazyOnload">
        {`
          window.Trengo = window.Trengo || {};
          window.Trengo.key = '7aCoJRh6wGgPFnYTBiVs';
          window.Trengo.render = 'true';

          (function(d, script, t) {
              script = d.createElement('script');
              script.type = 'text/javascript';
              script.async = true;
              script.src = 'https://static.widget.trengo.eu/embed.js';
              d.getElementsByTagName('head')[0].appendChild(script);
          }(document));
        `}
      </Script>

      <Layout>
        <HeroHelp />
      </Layout>
    </>
  );
}

export default Help;
