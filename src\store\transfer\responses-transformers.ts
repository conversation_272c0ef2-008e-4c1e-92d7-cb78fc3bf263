/**
 * transfer response schema
*/
import { globalPaginationBackendSchema } from '@/utils';
import { z } from 'zod';
import {
  currencyBackendSchema,
  currencyTransfersPaymentsApiResponseSchema,
} from '../currencies';

export const transferBackendResponseSchema = z.object({
  id: z.number(),
  attributes: z.object({
    snapshot: z.any().nullable(),
    type: z.enum(['transfer', 'exchange', 'payment_link', 'gift_card']),
    meta: z.any().nullable(),
    amount: z.string(),
    actual_amount: z.string(),
    notes: z.string().nullable().optional(),
    transaction_id: z.string(),
    createdAt: z.string(),
    updatedAt: z.string(),
    gif: z.string().nullable(),
    custom_populate: z
      .object({
        user_to: z.object({
          email: z.string(),
          account_id: z.string(),
          firstName: z.string().optional(),
          lastName: z.string().optional(),
        }),
        user_from: z.object({
          email: z.string(),
          account_id: z.string(),
          firstName: z.string().optional(),
          lastName: z.string().optional(),
        }),
      })
      .nullable(),
    currency_from: z.object({
      data: z.object({
        id: z.number(),
        attributes: currencyBackendSchema,
      }),
    }),
    currency_to: z.object({
      data: z.object({
        id: z.number(),
        attributes: currencyBackendSchema,
      }),
    }),
  }),
});

export const transfersBackendResponseSchema = z.array(
  transferBackendResponseSchema,
);

const extractUserFields = (user: { account_id: string } | null) => {
  if (!user) return null;
  return {
    account_id: user.account_id,
  };
};

const extractCurrencyFields = (currency: { symbol: string; code: string; }) => {
  if (!currency) return null;
  return {
    symbol: currency.symbol,
    code: currency.code,
  };
};

export const transferApiResponseSchema = (
  item: z.infer<typeof transferBackendResponseSchema>,
) => ({
  id: item.id,
  snapshot: {
    fees: item.attributes?.snapshot?.fees ? +item.attributes.snapshot.fees : 0,
    rate: item.attributes?.snapshot?.rate ? +item.attributes.snapshot.rate : 0,
    sender: item?.attributes?.snapshot?.sender ? extractUserFields(item.attributes.snapshot.sender) : null,
    receiver: item?.attributes?.snapshot?.receiver ? extractUserFields(item.attributes.snapshot.receiver) : null,
    currency: item?.attributes?.snapshot?.currency ? extractCurrencyFields(item.attributes.snapshot.currency) : null,
    Currency: item?.attributes?.snapshot?.Currency ? extractCurrencyFields(item.attributes.snapshot.Currency) : null,
    exchangedCurrency: item?.attributes?.snapshot?.exchangedCurrency
      ? extractCurrencyFields(item?.attributes?.snapshot?.exchangedCurrency)
      : null,
  },
  type: item.attributes.type,
  amount: +item.attributes.amount,
  note: item.attributes?.notes,
  actualAmount: +item.attributes.actual_amount,
  transactionId: item.attributes.transaction_id,
  createdAt: item.attributes.createdAt,
  gif: item.attributes.gif,
  currencyFrom: {
    ...currencyTransfersPaymentsApiResponseSchema(
      item.attributes.currency_from.data.attributes,
    ),
  },
  currencyTo: {
    ...currencyTransfersPaymentsApiResponseSchema(
      item.attributes.currency_to.data.attributes,
    ),
  },
  userFrom: {
    accountId: item.attributes?.custom_populate?.user_from?.account_id,
    email: item.attributes?.custom_populate?.user_from?.email,
    fullName: `${
      item.attributes?.custom_populate?.user_from?.firstName ?? ''
    } ${item.attributes?.custom_populate?.user_from?.lastName ?? ''}`,
  },
  userTo: {
    accountId: item.attributes?.custom_populate?.user_to?.account_id,
    email: item.attributes?.custom_populate?.user_to?.email,
    fullName: `${item.attributes?.custom_populate?.user_to?.firstName ?? ''} ${
      item.attributes?.custom_populate?.user_to?.lastName ?? ''
    }`,
  },
});

export const transfersApiResponseSchema = z
  .object({
    data: transfersBackendResponseSchema,
    meta: globalPaginationBackendSchema,
  })
  .transform(({ data, meta }) => ({
    data: data.map(transferApiResponseSchema),
    pagination: {
      page: meta.pagination?.page,
      pageSize: meta.pagination?.pageSize,
      pageCount: meta.pagination?.pageCount,
      total: meta.pagination.total,
    },
  }));
