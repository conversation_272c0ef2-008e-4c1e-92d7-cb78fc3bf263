import { z } from 'zod';
import {
  createGiftCardApiRequestSchema,
  createGiftCardBackendRequestSchema,
  redeemGiftCardApiRequestSchema,
  redeemGiftCardBackendRequestSchema,
} from './request-transformer';
import { giftCardsApiResponseSchema } from './responses-transformers';
import { Pagination } from '@/types';
import { QueryFunctionContext } from '@tanstack/react-query';

export type Filter = {
  code?: string;
};
export interface getGiftCardsQueryProps {
  populate?: {};
  pagination?: Pagination;
  filters?: Filter;
  params?: QueryFunctionContext;
}

export type CreateGiftCardBackendRequest = z.infer<
  typeof createGiftCardBackendRequestSchema
>;
export type CreateGiftCardApiRequest = z.infer<
  typeof createGiftCardApiRequestSchema
>;
export type RedeemGiftCardBackendRequest = z.infer<
  typeof redeemGiftCardBackendRequestSchema
>;
export type RedeemGiftCardApiRequest = z.infer<
  typeof redeemGiftCardApiRequestSchema
>;

export type GiftCardsApiResponse = z.infer<typeof giftCardsApiResponseSchema>;
