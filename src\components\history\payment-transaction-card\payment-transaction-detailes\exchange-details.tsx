import { Item } from '@/components/pop-up-success/pop-up-item';
import { PaymentsApiResponse } from '@/store/payment';
import { TransfersApiResponse } from '@/store/transfer/types';
import { Group, Text } from '@mantine/core';
import currency from 'currency.js';
import React from 'react';

interface Props {
  amount: number;
  actualAmount: TransfersApiResponse['data'][0]['actualAmount'];
  currencyFrom:
    | PaymentsApiResponse['data'][0]['currency']
    | TransfersApiResponse['data'][0]['currencyFrom']
    | undefined;
  currencyTo: TransfersApiResponse['data'][0]['currencyTo'] | undefined;
  fees: number | undefined;
  rate: number;
}
function ExchangeDetails({
  amount,
  actualAmount,
  currencyFrom,
  currencyTo,
  fees,
  rate,
}: Props) {
  return (
    <>
      <Item
        align="center"
        name="from"
        content={(
          <Group miw={100} position="apart">
            <Text weight={500} color="red" span>
              {currency(amount, {
                symbol: '',
                precision: currencyFrom?.precision ?? 2,
              }).format()}
            </Text>
            <Text color="red" weight={500} span>
              {currencyFrom?.code}
            </Text>
          </Group>
        )}
      />
      <Item
        align="center"
        name="to"
        content={(
          <Group miw={100} position="apart">
            <Text weight={500} color="green" span>
              {currency(`${actualAmount}`, {
                symbol: '',
                precision: currencyTo?.precision ?? 2,
              }).format()}
            </Text>
            <Text color="green" weight={500} span>
              {currencyTo?.code}
            </Text>
          </Group>
        )}
      />
      <Item
        align="center"
        name="fees"
        content={(
          <Group miw={100} position="apart">
            <Text weight={500} span>
              {currency(`${fees}`, {
                symbol: '',
                precision: currencyTo?.precision ?? 2,
              }).format()}
            </Text>
            <Text color="dimmed" weight={500} span>
              {currencyTo?.code}
            </Text>
          </Group>
        )}
      />

      <Item
        align="center"
        name="rate"
        content={(
          <Group spacing={2}>
            <div>
              <Text weight={500} size="sm" span>
                1
              </Text>
              <Text weight={500} size="sm" mx={2} span>
                {currencyFrom?.symbol}
              </Text>
              =
            </div>
            <div>
              <Text weight={500} size="sm" span>
                {`${rate}`.slice(0, 10)}
              </Text>
              <Text weight={500} size="sm" mx={2} span>
                {currencyTo?.symbol}
              </Text>
            </div>
          </Group>
        )}
      />
    </>
  );
}

export default ExchangeDetails;
