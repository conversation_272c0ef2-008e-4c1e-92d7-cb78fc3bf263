import { z } from 'zod';
import { userApiResponseSchema } from './response-transformer';
import { updateUserApiRequestSchema } from './request-transformer';

export interface getUserQueryProps {
  populate?: {
    userCurrencies?: boolean;
    favoriteCurrencies?: boolean;
  };
  loggedIn?: boolean;
}

export type UserApiResponse = z.infer<typeof userApiResponseSchema>;

export type UpdateUserApiRequest = z.infer<typeof updateUserApiRequestSchema>;
