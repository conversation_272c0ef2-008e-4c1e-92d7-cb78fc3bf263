import { Captcha } from '@/components/common/captcha';
import { ErrorPopup } from '@/components/common/errorr-poup';
import SubmitButton from '@/components/common/submit-button';
import { TransactionConfirmModal } from '@/components/common/transaction-confirm-modal';
import { CustomFieldsForm } from '@/components/custom-fields';
import { RenderCustomFields } from '@/components/custom-fields/render-custom-fields';
import { returnDefaultCustomFieldValue } from '@/components/custom-fields/render-custom-fileds-value';
import WithdrawDepositDetails from '@/components/history/payment-transaction-card/payment-transaction-detailes/withdraw-deposit-details';
import { PopUpSuccess } from '@/components/pop-up-success';
import { ROUTES } from '@/data';
import { useCaptcha } from '@/hooks';
import {
  AgentPaymentApiResponse,
  createAgentPaymentApiRequestSchema,
  createAgentPaymentMutation,
} from '@/store/agent';
import { PaymentsApiResponse } from '@/store/payment';

import { LimitsErrorKeys } from '@/types/error.type';
import { customFieldsErrorValidate } from '@/utils/validate-custom-fields-error';
import {
  Button, Paper, Stack, Text, Textarea,
} from '@mantine/core';
import { useForm, zodResolver } from '@mantine/form';
import { useDisclosure } from '@mantine/hooks';
import { useMutation } from '@tanstack/react-query';

import useTranslation from 'next-translate/useTranslation';
import Link from 'next/link';
import React, { useState } from 'react';

interface AgentPaymentFormProps {
  transactionId: string;
  onSuccess: () => void;
  justSuccessPopupDisplaying: boolean;
  toBeDeposit: number;
  fees: number;
  agentPayment: AgentPaymentApiResponse;
}
function AgentPaymentForm(props: AgentPaymentFormProps) {
  const {
    transactionId,
    onSuccess,
    justSuccessPopupDisplaying,
    toBeDeposit,
    fees,
    agentPayment,
  } = props;
  const { t } = useTranslation();
  const [opened, { open, close }] = useDisclosure(false);
  const [openLimitPopup, setLimitPopup] = useState(false);

  const [isLoading, setLoading] = useState(false);
  const [responseData, setResponseData] = useState<PaymentsApiResponse['data'][0]>();

  const { execute, reCaptchaRef, reset } = useCaptcha();

  const { depositCustomFields } = agentPayment.paymentMethod;

  // create payment form type"deposit"
  const form = useForm({
    initialValues: {
      transactionId,
      note: '',
      type: 'deposit',
      // return payment method deposit custom fields to initial "fields" in form
      fields: depositCustomFields?.map((i) => ({
        type: i?.type,
        name: i?.name,
        nameAr: i?.nameAr,
        // this function to initial value by type of custom field
        value: returnDefaultCustomFieldValue(i?.type),
      })),
    },
    validate: zodResolver(createAgentPaymentApiRequestSchema),
  });
  // create payment mutation
  const { mutate } = useMutation(createAgentPaymentMutation().mutationFn, {
    onSuccess: (data) => {
      setResponseData(data);
      close();
      form.reset();
      reset();
      setLoading(false);
    },
    onError: ({ response }) => {
      if (
        response?.data?.message?.key === LimitsErrorKeys.ERROR_PASS_THE_LIMIT
      ) {
        setLimitPopup(true);
      }
      close();
      reset();
      setLoading(false);
    },
  });

  // submit function
  function submit() {
    setLoading(true);
    const body = {
      body: {
        ...form.values,
      },
    };
    if (reCaptchaRef.current) {
      execute((token) => mutate({
        ...body,
        chaKey: token,
      }));
    } else {
      mutate(body);
    }
  }

  // return selected payment method custom fields
  const returnPaymentMethodCustomFields = depositCustomFields?.map(
    (i, index) => ({
      ...i,
      value: form.values?.fields ? form.values?.fields[index]?.value : '',
    }),
  );

  // onSubmit function
  // validate custom fields errors
  // open confirm popup
  const onSubmit = () => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    customFieldsErrorValidate(form as any, depositCustomFields, open);
  };

  return (
    <>
      {!justSuccessPopupDisplaying && (
        <>
          <form>
            <Paper w="100%" h="100%" p="md" withBorder radius="lg">
              <Text weight={700}>{t('common:attachDocuments')}</Text>
              <Stack mt="md" mx="auto" h="100%">
                <CustomFieldsForm
                  form={form}
                  fields={returnPaymentMethodCustomFields}
                />
                <Textarea
                  size="sm"
                  radius="lg"
                  placeholder={t('common:note') ?? ''}
                  label={<Text tt="capitalize">{t('common:note')}</Text>}
                  {...form.getInputProps('note')}
                />

                <SubmitButton fullWidth onClick={onSubmit}>
                  {t('common:submit')}
                </SubmitButton>
              </Stack>

              <TransactionConfirmModal
                close={close}
                openedDefault={opened}
                isLoading={isLoading}
                onClick={() => submit()}
                operationType="deposit"
              >
                <WithdrawDepositDetails
                  adminMessage={null}
                  actualAmount={toBeDeposit}
                  amount={agentPayment.actualAmount}
                  // eslint-disable-next-line @typescript-eslint/no-explicit-any
                  currencyFrom={agentPayment.currency as any}
                  fees={fees}
                  operationType="deposit"
                  paymentMethod={undefined}
                  originPaymentAmount={undefined}
                />
                <RenderCustomFields fields={form.values.fields} />
              </TransactionConfirmModal>

              <ErrorPopup
                open={openLimitPopup}
                setOpen={setLimitPopup}
                message={t('errors:limit-error')}
                actionButton={(
                  <Button
                    fullWidth
                    radius="lg"
                    component={Link}
                    href={`${ROUTES.myAccount.path}?page=limits`}
                  >
                    {t('common:goToLimits')}
                  </Button>
                )}
              />
            </Paper>
          </form>
          <Captcha reCaptchaRef={reCaptchaRef} />
        </>
      )}
      {responseData && (
        <PopUpSuccess
          currencySymbol={responseData?.currency?.symbol}
          currencyToSymbol=""
          operationType="deposit"
          setResponseData={setResponseData}
          operationId={responseData?.transactionId}
          amount={responseData?.amount}
          actualAmount={responseData?.actualAmount}
          fees={responseData?.totalFees}
          status={responseData?.status}
          note={responseData?.note}
          date={responseData?.createdAt}
          sender={undefined}
          receiver={undefined}
          currencyCode={responseData?.currency?.code}
          currencyToCode={undefined}
          paymentMethod={responseData?.paymentMethod}
          fields={responseData?.fields}
          rate={0}
          precision={responseData?.currency?.precision}
          currencyToPrecision={2}
          gif={null}
          cryptoGetaway={responseData?.cryptoGetaway}
          originPaymentAmount={responseData?.originPaymentAmount}
          isLoading={false}
          adminMessage={responseData?.adminMessage}
          onClose={onSuccess}
        />
      )}
    </>
  );
}

export default AgentPaymentForm;
