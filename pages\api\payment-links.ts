/**
 * There are two handlers get payment link data and create payment link.
 */
import { apiEndpoints, httpCode } from '@/data';
import { BackendClient } from '@/lib';
import { createPaymentLinkBackendRequestSchema } from '@/store/payment-links';
import { returnPaymentLinksParams } from '@/store/payment-links/params';
import { paymentLinksApiResponseSchema } from '@/store/payment-links/responses-transformers';

import createApiError from '@/utils/api-utils/create-api-error';
import createApiResponse from '@/utils/api-utils/create-api-response';
import { getJwt } from '@/utils/api-utils/jwt';
import { NextApiRequest, NextApiResponse } from 'next';

async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { token } = await getJwt(req);
  // This api to get payment link data
  if (req.method === 'GET') {
    try {
      const { data } = await BackendClient(req).get(
        apiEndpoints.paymentLinks(),
        {
          params: { ...returnPaymentLinksParams(req) },
        },
      );

      return createApiResponse(res, paymentLinksApiResponseSchema, data);
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
    // This api to create payment link
    // return a payment link url
  } else if (req.method === 'POST') {
    try {
      const { data } = await BackendClient(req).post(
        apiEndpoints.paymentLinks(),
        createPaymentLinkBackendRequestSchema.parse(req.body),
        {
          headers: {
            authorization: token,
          },
        },
      );

      return res
        .status(httpCode.SUCCESS)
        .json({ uid: data?.data?.attributes?.uid });
    } catch (e) {
      const error = createApiError({ error: e });
      return res.status(error.code).json(error);
    }
  }
  const error = createApiError({ error: '' });
  return res.status(httpCode.INTERNAL_SERVER_ERROR).json(error);
}

export default handler;
