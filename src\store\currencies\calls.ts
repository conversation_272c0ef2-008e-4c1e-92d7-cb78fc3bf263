import { apiEndpoints } from '@/data';
import { ApiClient } from '@/lib';
import { handleApiError } from '@/utils/api-utils/handle-backend-error';
import { getCurrenciesQueryProps } from './types';
import { QueryFunctionContext } from '@tanstack/react-query';

enum queryKeys {
  currencies = 'currencies',
}
/**
 * @description function calls handler in "/currencies" api route to return currencies
 * @param props
 * @returns array of currencies with pagination
 */

const getCurrenciesRequest = (props: getCurrenciesQueryProps) => {
  const { pagination, params } = props;
  return ApiClient.get(apiEndpoints.currencies(), {
    params: {
      page: params ? params.pageParam : pagination?.page,
      pageSize: pagination?.pageSize,
      start: pagination?.start,
      limit: pagination?.limit,
    },
  })
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e, true);
      throw e.response?.data;
    });
};
export const getCurrenciesQuery = (props: getCurrenciesQueryProps) => ({
  queryKey: [queryKeys.currencies],
  queryFn: (params: QueryFunctionContext) => getCurrenciesRequest({ ...props, params }),
  refetchOnWindowFocus: false,
  // if the last loaded page from pagination pages is less than the pagination page count set next page is last page +1
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  getNextPageParam: (lastPage: any) => lastPage?.pagination?.page < lastPage?.pagination?.pageCount
    && lastPage.pagination.page + 1,
  enabled: props?.enabled,
});
