import { ApiClient } from '@/lib';
import { deleteFileProps } from './type';
import { apiEndpoints } from '@/data';
import { handleApiError } from '@/utils/api-utils/handle-backend-error';
import { UseMutationOptions } from '@tanstack/react-query';
import { filePostResponseData } from './response-transformer';
/**
 * @description function calls handler in "/upload" api route with "post" method to upload files.
 * @param files
 * @returns uploaded file data or error if failed
 */
const postFile = (files: FormData) => ApiClient.post(apiEndpoints.uploadFile(), files, {
  maxBodyLength: Infinity,
  maxContentLength: Infinity,
})
  .then((res) => res.data)
  .catch((error) => {
    handleApiError(error);
    throw error.response.data;
  });

export const addFileMutation = (): UseMutationOptions<
  filePostResponseData,
  unknown,
  FormData
> => ({
  mutationFn: postFile,
});
//* ***************************************************************************************** */
/**
 * this function didn't call in any file
 */
const deleteFile = (props: deleteFileProps) => {
  const { id } = props;
  return ApiClient.delete(apiEndpoints.uploadFile(), {
    params: {
      id,
    },
  })
    .then((res) => res.data)
    .catch((error) => {
      handleApiError(error);
      throw error.response.data;
    });
};
export const removeFileMutation = () => ({
  mutationFn: deleteFile,
});
