import {
  UpdateUserApiRequest,
  UserApiResponse,
  updateUserApiRequestSchema,
  updateUserMutation,
} from '@/store/user';
import {
  Button,
  Group,
  Loader,
  Paper,
  Stack,
  TextInput,
  Text,
  Box,
} from '@mantine/core';
import { IconMail, IconUser } from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';
import React, { useState } from 'react';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { notifications } from '@mantine/notifications';
import SelectWithFlags from '../select-with-flags/select-with-flags';
import { countries } from 'countries-list';

import { CustomCopyButton } from '../../common/custom-copy-button';
import { useForm, zodResolver } from '@mantine/form';
import { AvatarUploadSection } from '../avatar-upload-section';
import { Badges } from '../badges';
import { preserveUserData } from '@/utils/profile-utils';

interface Props {
  data: UserApiResponse | undefined;
}
function ProfileTab({ data }: Props) {
  const { t } = useTranslation();
  const [updateProfile, setUpdateProfile] = useState(false);
  const queryClient = useQueryClient();
  const form = useForm<UpdateUserApiRequest>({
    initialValues: {
      firstName: data?.firstName,
      lastName: data?.lastName,
      country: data?.country,
    },
    validate: zodResolver(updateUserApiRequestSchema),
  });

  const { mutate, isLoading } = useMutation(updateUserMutation().mutationFn, {
    onSuccess() {
      queryClient.invalidateQueries(['user']);
      notifications.show({
        message: t(
          `common:${
            updateProfile ? 'profileUpdateSuccess' : 'avatarUpdateSuccess'
          }`,
        ),
        color: 'blue',
      });
      form.resetDirty();
      setUpdateProfile(false);
    },
  });

  const submit = (values: UpdateUserApiRequest) => {
    setUpdateProfile(true);
    mutate(preserveUserData(values, data));
  };
  return (
    <>
      <Stack spacing={0} align="stretch" m="auto" w="100%" maw={650}>
        <AvatarUploadSection
          data={data}
          isLoading={isLoading}
          mutate={mutate}
        />

        <Paper p="md" mt="lg" withBorder radius="lg">
          <Text color="dimmed" size="xs">
            {t('common:profileNote')}
          </Text>
          <TextInput
            mt={5}
            mb={20}
            radius="lg"
            label={`${t('common:email')}`}
            placeholder={`${t('common:email')}`}
            type="email"
            icon={<IconMail />}
            readOnly
            value={data?.email}
          />
          <form onSubmit={form.onSubmit(submit)}>
            <Group mb={20} position="apart">
              <TextInput
                readOnly={!!data?.firstName}
                disabled={isLoading}
                miw={250}
                w={{ base: '100%', sm: '48%' }}
                radius="lg"
                label={<Text tt="capitalize">{t('common:firstName')}</Text>}
                placeholder={`${t('common:firstName')}`}
                {...form.getInputProps('firstName')}
              />
              <TextInput
                readOnly={!!data?.lastName}
                disabled={isLoading}
                miw={250}
                w={{ base: '100%', sm: '48%' }}
                radius="lg"
                label={<Text tt="capitalize">{t('common:lastName')}</Text>}
                placeholder={`${t('common:lastName')}`}
                {...form.getInputProps('lastName')}
              />
            </Group>
            <Group mb={5} align="end" position="apart">
              <TextInput
                radius="lg"
                miw={250}
                w={{ base: '100%', sm: '48%' }}
                label={<Text tt="capitalize">{t('common:accountNo')}</Text>}
                placeholder={`${t('common:accountNo')}`}
                readOnly
                icon={<IconUser />}
                value={data?.accountId}
                rightSection={
                  <CustomCopyButton value={data?.accountId ?? ''} />
                }
              />
              <SelectWithFlags
                readOnly={!!data?.country}
                clearable
                miw={250}
                w={{ base: '100%', sm: '48%' }}
                radius="lg"
                data={countries}
                label={<Text tt="capitalize">{t('common:country')}</Text>}
                placeholder={t('common:pickOne') as string}
                priorityList={['US', 'CA', 'GB', 'DE', 'RU']}
                rightSection={!countries && <Loader size="xs" />}
                disabled={isLoading}
                {...form.getInputProps('country')}
              />
            </Group>

            {(!data?.firstName || !data?.lastName || !data?.country) && (
              <Button
                loading={isLoading}
                disabled={!form.isDirty()}
                mt={20}
                type="submit"
                fullWidth
                radius="lg"
              >
                {t('common:save')}
              </Button>
            )}
          </form>
        </Paper>
      </Stack>

      {data?.badges && data?.badges?.length > 0 && (
        <Box maw={650} mx="auto" mt="lg">
          <Badges badges={data?.badges} />
        </Box>
      )}
    </>
  );
}

export default ProfileTab;
