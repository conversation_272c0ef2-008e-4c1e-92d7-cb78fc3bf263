import { Group, Modal, Text } from '@mantine/core';
import { IconAlertTriangle } from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';

import React, { ReactNode } from 'react';

interface ErrorPopupProps {
  open: boolean;
  setOpen: (value: boolean) => void;
  message: string;
  actionButton: ReactNode;
}
function ErrorPopup(props: ErrorPopupProps) {
  const {
    open, setOpen, actionButton, message,
  } = props;
  const { t } = useTranslation();

  const onClose = () => {
    setOpen(false);
  };
  return (
    <Modal radius="lg" centered opened={open} onClose={onClose}>
      <Group position="center">
        <IconAlertTriangle size="80px" color="red" stroke={1.5} />
      </Group>
      <Text ta="center" mt="md" fw={500} size="lg">{t('common:operationFailed')}</Text>
      <Text mt="md">{message}</Text>
      <Group position="right" mt="xl">
        {actionButton}
      </Group>
    </Modal>
  );
}

export default ErrorPopup;
