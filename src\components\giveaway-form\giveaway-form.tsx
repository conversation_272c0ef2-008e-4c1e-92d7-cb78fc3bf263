import {
  CreateGiveawayApiRequest,
  createGiveawayApiRequestSchema,
  createGiveawayMutation,
} from '@/store/giveaway';
import { getUserQuery } from '@/store/user';
import {
  Box, Group, Text, TextInput,
} from '@mantine/core';
import { useForm, zodResolver } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';

import React, { useEffect, useState } from 'react';
import SubmitButton from '../common/submit-button';
import { Captcha } from '../common/captcha';
import { useCaptcha } from '@/hooks';

function GiveawayForm() {
  const { status } = useSession();
  const [isLoading, setLoading] = useState(false);
  const { execute, reCaptchaRef, reset } = useCaptcha();

  const { data } = useQuery({
    ...getUserQuery({}),
    enabled: false,
  });
  const form = useForm<CreateGiveawayApiRequest>({
    initialValues: {
      email: data?.email ?? '',
      link: '',
    },

    validate: zodResolver(createGiveawayApiRequestSchema),
  });

  useEffect(() => {
    form.setFieldValue('email', data?.email ?? '');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  const { mutate } = useMutation({
    ...createGiveawayMutation(),
    onSuccess() {
      form.reset();
      reset();
      setLoading(false);
      notifications.show({
        message: 'تم الإشتراك بنجاح',
        color: 'blue',
      });
    },
    onError() {
      reset();
      setLoading(false);
      notifications.show({
        message: 'فشل الإشتراك',
        color: 'red',
      });
    },
  });
  const submit = (values: CreateGiveawayApiRequest) => {
    setLoading(true);
    const body = values?.email
      ? { ...values, email: values?.email }
      : { ...values, email: data?.email ?? '' };
    if (reCaptchaRef.current) {
      execute((token) => mutate({
        body: {
          ...values,
          email: values?.email ?? data?.email,
        },
        chaKey: token,
      }));
    } else {
      mutate({ body });
    }
  };
  return (
    <div>
      <form onSubmit={form.onSubmit(submit)}>
        <Group h="100%" align="end" position="apart" noWrap>
          <TextInput
            type="url"
            dir="rtl"
            w="70%"
            required
            radius="lg"
            label="رابط المشاركة"
            {...form.getInputProps('link')}
          />

          <SubmitButton
            disabled={status !== 'authenticated'}
            w="28%"
            radius="lg"
            loading={isLoading}
            type="submit"
          >
            إرسال
          </SubmitButton>
        </Group>
        <Group mt="xs" noWrap>
          <Box>
            {status !== 'authenticated' && (
              <Text color="red" weight={500}>
                ملاحظة: قم بتسجيل الدخول لإتمام العملية
              </Text>
            )}
          </Box>
        </Group>
      </form>
      <Captcha reCaptchaRef={reCaptchaRef} />
    </div>
  );
}

export default GiveawayForm;
