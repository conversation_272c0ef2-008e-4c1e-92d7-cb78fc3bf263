import { CreateFeedbackApiRequest } from '@/store/feedback';
import { Rating, useMantineTheme, rem } from '@mantine/core';
import { UseFormReturnType } from '@mantine/form';
import {
  IconMoodEmpty,
  IconMoodCry,
  IconMoodSad,
  IconMoodSmile,
  IconMoodCrazyHappy,
} from '@tabler/icons-react';

interface Props {
  form: UseFormReturnType<CreateFeedbackApiRequest>;
}
function RatingSmiles({ form }: Props) {
  const getEmptyIcon = (value: number) => {
    const defaultProps = { size: rem(24), color: 'gray' };
    switch (value) {
      case 1:
        return <IconMoodCry {...defaultProps} />;
      case 2:
        return <IconMoodSad {...defaultProps} />;
      case 3:
        return <IconMoodSmile {...defaultProps} />;
      case 4:
        return <IconMoodCrazyHappy {...defaultProps} />;
      default:
        return <IconMoodEmpty {...defaultProps} />;
    }
  };

  const getFullIcon = (value: number) => {
    const defaultProps = { size: rem(24) };
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const theme = useMantineTheme();

    switch (value) {
      case 1:
        return <IconMoodCry {...defaultProps} color={theme.colors.red[7]} />;
      case 2:
        return <IconMoodSad {...defaultProps} color={theme.colors.orange[7]} />;
      case 3:
        return (
          <IconMoodSmile {...defaultProps} color={theme.colors.yellow[7]} />
        );
      case 4:
        return (
          <IconMoodCrazyHappy {...defaultProps} color={theme.colors.green[7]} />
        );
      default:
        return <IconMoodEmpty {...defaultProps} />;
    }
  };

  return (
    <Rating
      count={4}
      emptySymbol={getEmptyIcon}
      fullSymbol={getFullIcon}
      highlightSelectedOnly
      {...form.getInputProps('rate')}
    />
  );
}

export default RatingSmiles;
