/* eslint-disable @typescript-eslint/no-explicit-any */
import { <PERSON>, <PERSON><PERSON>, Card } from '@mantine/core';
import { HTMLAttributes } from 'react';

export type InferComponentProps<T extends (...args: any) => any> =
  T extends typeof Text
    ? HTMLAttributes<any> & T['defaultProps']
    : T extends typeof Button
    ? HTMLAttributes<any> & T['defaultProps']
    : T extends typeof Card
    ? HTMLAttributes<any> & T['defaultProps']
    : Parameters<T>[number];
