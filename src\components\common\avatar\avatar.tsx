import { Avatar, MantineNumberSize } from '@mantine/core';

interface AvatarProps {
  src: string | undefined;
  name: string;
  size: MantineNumberSize | undefined;
}

export default function AvatarCustom({ size, name, src }: AvatarProps) {
  const fullName = name && name?.split(' ');
  const avName = fullName
    ? fullName?.length > 0
      && `${fullName[0] ? fullName[0][0].toUpperCase() : ''} ${
        fullName[1] ? fullName[1][0].toUpperCase() : ''
      }`
    : '';
  return (
    <Avatar
      radius={60}
      size={size ?? 'sm'}
      src={src ?? ''}
      alt=""
    >
      {avName}
    </Avatar>
  );
}
