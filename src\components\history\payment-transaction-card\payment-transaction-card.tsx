import {
  Badge, Group, Image, Stack, Text, Tooltip,
} from '@mantine/core';

import currency from 'currency.js';
import useTranslation from 'next-translate/useTranslation';
import { PaymentsApiResponse } from '@/store/payment';
import dayjs from 'dayjs';
import { TransfersApiResponse } from '@/store/transfer/types';
import { statusCases } from '@/utils/operations-utils/operation-status';
import { operationIcon } from '@/utils/operations-utils/operation-icons';
import PaymentDetails from './payment-transaction-detailes/payment-transaction-detailes';
import { useRouter } from 'next/router';
import { GlobalCard } from '@/components/common/global-card';
import { Icon } from '@/components/common/icon';
import { DATE_FORMAT } from '@/data';
import { getTranslatedTextValue } from '@/utils';

interface MainStatProps {
  id: string | null;
  amount: number;
  actualAmount: PaymentsApiResponse['data'][0]['actualAmount'];
  date: string;
  status: PaymentsApiResponse['data'][0]['status'] | undefined;
  gif: string | null;
  operationType:
    | 'deposit'
    | 'transfer'
    | 'withdraw'
    | 'exchange'
    | 'received'
    | string;
  actualType:
    | 'deposit'
    | 'transfer'
    | 'withdraw'
    | 'exchange'
    | 'received'
    | 'payment_link'
    | 'gift_card'
    | string;
  note: string | undefined;
  userFrom:
    | PaymentsApiResponse['data'][0]['user']
    | TransfersApiResponse['data'][0]['userFrom']
    | undefined;
  userTo: TransfersApiResponse['data'][0]['userTo'] | undefined;
  currencyFrom:
    | PaymentsApiResponse['data'][0]['currency']
    | TransfersApiResponse['data'][0]['currencyFrom']
    | undefined;
  currencyTo: TransfersApiResponse['data'][0]['currencyTo'] | undefined;
  paymentMethod: PaymentsApiResponse['data'][0]['paymentMethod'] | undefined;
  fees: number | undefined;
  fields: PaymentsApiResponse['data'][0]['fields'] | undefined;
  rate: number;
  adminMessage: string | null;
  cryptoGetaway:PaymentsApiResponse['data'][0]['cryptoGetaway'] | undefined;
  originPaymentAmount: PaymentsApiResponse['data'][0]['originPaymentAmount'] | undefined;
}

export function PaymentTransactionCard({
  id,
  amount,
  date,
  status,
  operationType,
  note,
  userFrom,
  userTo,
  currencyFrom,
  currencyTo,
  actualAmount,
  paymentMethod,
  fees,
  fields,
  rate,
  adminMessage,
  actualType,
  gif,
  cryptoGetaway,
  originPaymentAmount,
}: MainStatProps) {
  const { t } = useTranslation();
  const { query, locale } = useRouter();
  const currencyUid = query?.slug;
  // return the color by operation type if the operation increase the balance or decrease it
  const amountColorByOperationType = () => {
    let color = '';
    if (operationType === 'exchange') {
      if (currencyUid && currencyTo?.uid === currencyUid) {
        color = 'green';
      } else color = 'red';
    } else if (operationType === 'deposit') color = 'green';
    else if (operationType === 'withdraw') color = 'red';
    else if (operationType === 'received') color = 'green';
    else color = 'red';
    return color;
  };
  // return image, symbol and and value by type of operation type
  const getAmountValue = () => {
    let value = '';
    let symbol = currencyFrom?.symbol;
    let image = currencyFrom?.image;
    if (operationType === 'received') {
      value = currency(`${actualAmount}`, {
        symbol: '',
        precision: currencyFrom?.precision ?? 2,
      }).format();
    } else if (
      operationType === 'exchange'
      && currencyUid
      && currencyTo?.uid === currencyUid
    ) {
      value = currency(`${actualAmount}`, {
        symbol: '',
        precision: currencyTo?.precision ?? 2,
      }).format();
      symbol = currencyTo?.symbol;
      image = currencyTo.image;
    } else {
      value = currency(amount, {
        symbol: '',
        precision: currencyFrom?.precision ?? 2,
      }).format();
    }
    return { value, symbol, image };
  };
  return (
    <PaymentDetails
      paymentMethod={paymentMethod}
      currencyFrom={currencyFrom}
      currencyTo={currencyTo}
      userFrom={userFrom}
      userTo={userTo}
      note={note}
      status={status}
      id={id}
      amount={amount}
      actualAmount={actualAmount}
      date={date}
      operationType={operationType}
      actualType={actualType}
      operationIcon={operationIcon({ operationType })}
      statusIcon={statusCases({ status })}
      fees={fees}
      fields={fields}
      rate={rate}
      adminMessage={adminMessage}
      gif={gif}
      cryptoGetaway={cryptoGetaway}
      originPaymentAmount={originPaymentAmount}
    >
      <GlobalCard props={{}} onClick={() => {}} key={id}>
        <div style={{ display: 'flex', gap: 8, alignItems: 'center' }}>
          <Image
            alt="currency"
            src={getAmountValue().image}
            height={40}
            width={40}
            radius={40}
          />
          <Stack spacing={5} w="90%">
            <Group position="apart">
              <div>
                <Text
                  color={amountColorByOperationType()}
                  span
                  weight={700}
                  size="md"
                >
                  {getAmountValue().value}
                </Text>
                <Text
                  mx={4}
                  span
                  weight={500}
                  color={amountColorByOperationType()}
                >
                  {getAmountValue().symbol}
                </Text>
              </div>
              <Group align="center">
                <Tooltip
                  position="bottom-start"
                  label={t(
                    `common:${
                      operationType === 'payment_link'
                        ? 'transfer'
                        : operationType
                    }`,
                  )}
                >
                  <Group>
                    <Icon
                      icon={operationIcon({
                        operationType:
                          operationType === 'received'
                            ? 'received'
                            : operationType,
                      })}
                      size={29}
                      color="gray"
                    />
                  </Group>
                </Tooltip>
                {paymentMethod && (
                  <Tooltip
                    position="bottom-start"
                    label={getTranslatedTextValue(locale, paymentMethod?.label, paymentMethod?.labelAr)}
                  >
                    <Image
                      alt="currency"
                      src={paymentMethod?.image}
                      height={25}
                      width={25}
                      radius={50}
                    />
                  </Tooltip>
                )}
                {status && (
                  <Tooltip label={t(`common:${status}`)}>
                    <Badge
                      p={0}
                      w={25}
                      h={25}
                      sx={{ borderWidth: '0.15rem' }}
                      variant="outline"
                      radius={50}
                      color={statusCases({ status }).color}
                    >
                      <Group>
                        <Icon
                          icon={statusCases({ status }).icon}
                          size={16}
                          color="dark"
                        />
                      </Group>
                    </Badge>
                  </Tooltip>
                )}
              </Group>
            </Group>
            <Group spacing={5}>
              <div />
              <Text weight={500} size="sm" color="dimmed" tt="uppercase">
                ID
                {' '}
                {id}
              </Text>
              {(operationType === 'transfer'
                || operationType === 'received'
                || operationType === 'payment_link') && (
                <div>
                  <Text weight={500} size="sm" color="dimmed" span>
                    {t(
                      `common:${operationType === 'received' ? 'from' : 'to'}`,
                    )}
                    :
                  </Text>
                  <Text span weight={500} size="sm" color="dimmed">
                    {operationType === 'received'
                      ? ` ${userFrom?.email}`
                      : ` ${userTo?.email}`}
                  </Text>
                </div>
              )}
              <Text weight={500} size="sm" color="dimmed" tt="uppercase">
                {date && dayjs(date).format(DATE_FORMAT)}
              </Text>
            </Group>
          </Stack>
        </div>
      </GlobalCard>
    </PaymentDetails>
  );
}
