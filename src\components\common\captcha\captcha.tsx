import { ENV_MODE } from '@/types';
import React from 'react';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from 'react-google-recaptcha';

interface CaptchaProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  reCaptchaRef: React.MutableRefObject<any>;
  // eslint-disable-next-line react/require-default-props
  active?: boolean;
}
function Captcha({ reCaptchaRef, active = true }: CaptchaProps) {
  if (process.env.NEXT_PUBLIC_ENV === ENV_MODE.Production && active) {
    return (
      <ReCAPTCHA
        sitekey={process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY as string}
        size="invisible"
        ref={reCaptchaRef}
      />
    );
  }

  return <div />;
}

export default Captcha;
