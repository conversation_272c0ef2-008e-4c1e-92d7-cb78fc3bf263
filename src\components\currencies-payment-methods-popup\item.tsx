import { useStyles } from './style';
import {
  Group,
  Image,
  Text,
  UnstyledButton,

} from '@mantine/core';
import { CurrenciesApiResponse } from '@/store/currencies';
import useTranslation from 'next-translate/useTranslation';

interface FeesProps {
  symbol: CurrenciesApiResponse['data'][0]['symbol'] | undefined;
  fees: {
    percent: number;
    fixed: number;
  };
}

export function Fees({ fees, symbol }: FeesProps) {
  const { t } = useTranslation();

  const { fixed, percent } = fees;
  let fee = '';
  const renderFees = () => {
    if (!percent && !fixed) {
      fee = t('common:noFee');
    }
    if (percent) {
      fee = `${percent}%`;
    }
    if (fixed) {
      fee += ` ${percent ? '+' : ''} ${fixed}${symbol}`;
    }
    return fee;
  };
  return (
    <Group spacing={4}>
      <Text c="dimmed" size="xs" lh={1} mb={5}>
        {t('common:fees')}
        :
      </Text>
      <Text c="dimmed" size="xs" lh={1} mb={5}>
        {renderFees()}
      </Text>
    </Group>
  );
}
interface ItemProps {
  title: string;
  description: string;
  image: string;
  currencyId: string;
  onChange: (currencyId: string) => void;
  selected: boolean;
  symbol: CurrenciesApiResponse['data'][0]['symbol'] | undefined;
  fees:
    | {
        percent: number;
        fixed: number;
      }
    | undefined;
}

export function Item({
  title,
  description,
  image,
  onChange,
  currencyId,
  selected,
  fees,
  symbol,
}: ItemProps &
  Omit<React.ComponentPropsWithoutRef<'button'>, keyof ItemProps>) {
  const { classes } = useStyles();

  const handleItemClick = () => {
    onChange(currencyId);
  };

  return (
    <UnstyledButton
      miw={310}
      onClick={handleItemClick}
      className={`${classes.button} ${
        selected ? classes.checkedButton : ''
      }   `}
    >
      <Image src={image} alt={title} width={40} height={40} />

      <div className={classes.body}>
        <Text c="dimmed" size="xs" lh={1} mb={5}>
          {description}
        </Text>
        {fees && <Fees fees={fees} symbol={symbol} />}
        <Text fw={500} size="sm" lh={1}>
          {title}
        </Text>
      </div>
    </UnstyledButton>
  );
}
