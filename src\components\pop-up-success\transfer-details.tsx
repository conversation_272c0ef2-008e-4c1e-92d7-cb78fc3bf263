import React from 'react';

import currency from 'currency.js';
import { Text } from '@mantine/core';
import { Item } from './pop-up-item';

interface Props {
  currencyCode: string | undefined;
  currencySymbol: string | null | undefined;
  amount: number;
  actualAmount: number;
  precision: number;
  fees: number;
  sender: string | undefined;
  receiver: string | undefined;
}

function TransferDetails({
  currencyCode,
  currencySymbol,
  amount,
  actualAmount,
  precision,
  fees,
  receiver,
  sender,
}: Props) {
  return (
    <>
      <Item
        align="center"
        name="sentAmount"
        content={(
          <div>
            <Text weight={500} color="red" span>
              {currency(amount, {
                symbol: '',
                precision,
              }).format()}
            </Text>
            <Text weight={500} color="red" mr={4} span>
              {currencySymbol}
            </Text>
          </div>
        )}
      />
      <Item
        align="center"
        name="fees"
        content={(
          <Text>
            {currency(fees, {
              symbol: '',
              precision,
            }).format()}
          </Text>
        )}
      />
      <Item
        align="center"
        name="toBeReceived"
        content={(
          <div>
            <Text weight={500} color="red" span>
              {currency(actualAmount, {
                symbol: '',
                precision,
              }).format()}
            </Text>
            <Text weight={500} color="red" mr={4} span>
              {currencySymbol}
            </Text>
          </div>
        )}
      />
      <Item
        align="center"
        name="currency"
        content={<Text>{currencyCode}</Text>}
      />
      {receiver && sender !== receiver && (
        <Item
          align="center"
          name="receiver"
          content={<Text>{receiver}</Text>}
        />
      )}
    </>
  );
}

export default TransferDetails;
