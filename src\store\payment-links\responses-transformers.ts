/**
 * payment link response schema
 */
import { z } from 'zod';
import {
  currencyApiResponseSchema,
  currencyBackendSchema,
} from '../currencies';
import { imageBackedSchema } from '../currencies/responses-transformers';

export const paymentLinkBackendResponseSchema = z.object({
  id: z.number(),
  attributes: z.object({
    expiry_date: z.string(),
    amount: z.string(),
    actual_amount: z.string(),
    fees: z.string().nullable(),
    uid: z.string(),
    status: z.enum(['pending', 'fulfilled', 'timed_out']).nullable(),
    redirect_url: z.string().nullable(),
    createdAt: z.string(),
    currency: z.object({
      data: z.object({
        id: z.number(),
        attributes: currencyBackendSchema,
      }),
    }),

    user_to: z.object({
      data: z.object({
        id: z.number(),
        attributes: z.object({
          account_id: z.string(),
          email: z.string().nullable(),
          first_name: z.string().nullable(),
          last_name: z.string().nullable(),
          avatar: z
            .object({
              data: z
                .object({
                  attributes: imageBackedSchema,
                })
                .nullable(),
            })
            .nullable()
            .optional(),
        }),
      }),
    }),
  }),
});

export const paymentLinksBackendResponseSchema = z.array(
  paymentLinkBackendResponseSchema,
);

export const paymentLinkApiResponseSchema = (
  item: z.infer<typeof paymentLinkBackendResponseSchema>,
) => ({
  id: item.id,
  amount: +item.attributes.amount,
  status: item.attributes?.status,
  uid: item.attributes.uid,
  expiryDate: item.attributes.expiry_date,
  redirectUrl: item.attributes?.redirect_url,
  createdAt: item.attributes.createdAt,
  fees: item.attributes.fees ? +item.attributes.fees : null,
  actualAmount: +item.attributes.actual_amount,
  currency: {
    id: item.attributes.currency.data.id,
    ...currencyApiResponseSchema(item.attributes.currency.data.attributes),
  },
  userTo: {
    id: item.attributes.user_to.data.id,
    accountId: item.attributes.user_to.data.attributes.account_id,
    email: item.attributes.user_to.data.attributes?.email,
    firstName: item.attributes.user_to.data.attributes?.first_name,
    lastName: item.attributes.user_to.data.attributes?.last_name,
    url:
      item.attributes.user_to.data.attributes?.avatar?.data?.attributes?.formats
        ?.thumbnail.url
      ?? item.attributes.user_to.data.attributes?.avatar?.data?.attributes.url,
  },
});

export const paymentLinksApiResponseSchema = z
  .object({
    data: paymentLinksBackendResponseSchema,
  })
  .transform(({ data }) => ({
    data: data.map(paymentLinkApiResponseSchema),
  }));
