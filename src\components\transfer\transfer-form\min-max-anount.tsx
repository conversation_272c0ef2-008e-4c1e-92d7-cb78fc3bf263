import { UserApiResponse } from '@/store/user';
import { Group, Text } from '@mantine/core';
import currency from 'currency.js';
import useTranslation from 'next-translate/useTranslation';
import React from 'react';

interface Props {
  currencyItem: UserApiResponse['currencies'][0] | undefined;
}

function MinMaxAmountTransfer({ currencyItem }: Props) {
  const { t } = useTranslation();
  return (
    <Group spacing="xs">
      <div>
        <Text span weight={500} color="dimmed">
          {t('common:min')}
          {' '}
          {currencyItem?.transferMin
            ? currency(currencyItem?.transferMin, {
              symbol: '',
              precision: currencyItem?.precision ?? 2,
            }).format()
            : '0.00 '}
        </Text>
        <Text mx={4} span weight={500} color="dimmed">
          {currencyItem?.symbol}
        </Text>
      </div>
      -
      <div>
        <Text span weight={500} color="dimmed">
          {t('common:max')}
          {' '}
          {currencyItem?.transferMax
            ? currency(
              currencyItem?.transferMax > currencyItem?.amount ? currencyItem?.amount : currencyItem?.transferMax,
              { symbol: '', precision: currencyItem?.precision ?? 2 },
            ).format()
            : currency(currencyItem?.amount ?? 0, {
              symbol: '',
              precision: currencyItem?.precision ?? 2,
            }).format()}
        </Text>
        <Text mx={4} span weight={500} color="dimmed">
          {currencyItem?.symbol}
        </Text>
      </div>
      {currencyItem?.amount === 0 && (
        <Text mx={2} weight={500} color="red">
          {t('common:youDoNotHaveBalanceToDoOperation')}
        </Text>
      )}
    </Group>
  );
}

export default MinMaxAmountTransfer;
