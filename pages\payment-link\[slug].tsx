/**
 * This component renders a Payment Link page.
 *
 * @description
 * This page used to create transfer type "payment link" by payment link.
 * Payment link should be created from another user, when crate payment link will display a link to the user and he will share it with another user.
 * In this page we call get payment link api and filter by "uid" query get from page route url.
 * Will get all transfer form data from payment link data except "userFrom" this set when another user pay the payment link.
 * When user pay the payment link will create transfer and update payment link status and navigate to
 *   the "redirect url" returns from payment link or to wallets page if "redirect url" not found.
 * The user can pay the payment link if it has not expired.
 */
import MetaTags from '@/components/common/meta-tags';
import { ResponsiveFactory } from '@/components/common/responsive-component';
import { Layout } from '@/components/layout/layout';
import { PaymentLinkDetails } from '@/components/payment-link/payment-link-details';
import { ROUTES } from '@/data';

import { getPaymentLinksQuery } from '@/store/payment-links/calls';

import {
  ActionIcon, Card, Stack, Title, Text,
} from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';

import { IconCircleCheck } from '@tabler/icons-react';
import { useQuery } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';

import useTranslation from 'next-translate/useTranslation';
import { useRouter } from 'next/router';
import React, { useState } from 'react';
import Countdown, { CountdownApi } from 'react-countdown';
//* *****************Responsive Components*************************** */

const RCard = ResponsiveFactory({
  component: Card,
  sharedProps: { pr: 'xl', w: 500, mx: 'auto' },
});

function PaymentLink() {
  const match425 = useMediaQuery('(max-width: 425px)');
  const [success, setSuccess] = useState(false);
  const [countDownApis, setCountDownApis] = useState<null | CountdownApi>(null);
  const { push, query } = useRouter();
  const { t } = useTranslation('common');
  const { status } = useSession();
  const authenticated = status === 'authenticated';
  //* ********************Queries***************************************** */
  const { data, isLoading } = useQuery({
    ...getPaymentLinksQuery({ filters: { uid: query.slug as string } }),
  });
  const hasNotPaymentLinkData = data?.data.length === 0;
  return (
    <>
      <MetaTags />
      <Layout>
        <RCard
          props={{
            lowerThanSm: {
              w: match425 ? '100%' : 400,
            },
          }}
        >
          {!success && !hasNotPaymentLinkData && (
            <PaymentLinkDetails
              isAuth={authenticated}
              onPaySuccess={() => {
                setSuccess(true);
                countDownApis?.start();
              }}
              data={data}
              isLoading={isLoading}
            />
          )}
          {hasNotPaymentLinkData && (
            <Stack h={600} justify="center" align="center">
              <Text size="lg" weight={700}>
                {t('common:paymentLinkInvalid')}
              </Text>
            </Stack>
          )}
          {success && (
            <Stack align="center">
              <Stack align="center" spacing={0}>
                <ActionIcon color="green" size={200}>
                  <IconCircleCheck size={200} stroke={1} />
                </ActionIcon>
                <Title color="green" order={3}>
                  {t('success')}
                </Title>
              </Stack>
              <Text mt="xl" size="lg" weight={500} color="dimmed">
                {t('youWillBeRedirected')}
              </Text>
              <Countdown
                date={Date.now() + 5000}
                ref={(apis) => {
                  setCountDownApis(apis);
                }}
                onComplete={() => {
                  if (data?.data[0].redirectUrl) {
                    push(data?.data[0].redirectUrl);
                  } else push(ROUTES.wallets.path);
                }}
                renderer={(p) => (
                  <Title weight={500} color="dimmed">
                    {p.formatted.seconds}
                  </Title>
                )}
              />
            </Stack>
          )}
        </RCard>
      </Layout>
    </>
  );
}

export default PaymentLink;
