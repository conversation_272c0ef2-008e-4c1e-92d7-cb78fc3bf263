import { UserApiResponse } from '@/store/user';
import { KYC_STATUS } from '@/store/user/response-transformer';
import { Badge } from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';

export function ReturnKYCBadge({
  kyc,
}: {
  kyc: UserApiResponse['kyc'] | undefined;
}) {
  const { t } = useTranslation();

  switch (kyc?.status) {
    case KYC_STATUS.Approved:
      return (
        <Badge variant="filled" color="primary">
          {t('common:approved')}
        </Badge>
      );
    case KYC_STATUS.Pending:
      return (
        <Badge variant="filled" color="green">
          {t('common:pending')}
        </Badge>
      );
    case KYC_STATUS.Rejected:
      return (
        <Badge variant="filled" color="red">
          {t('common:rejected')}
        </Badge>
      );
    case KYC_STATUS.Disabled:
      return <div />;
    default:
      return (
        <Badge variant="filled" color="yellow">
          {t('common:notVerified')}
        </Badge>
      );
  }
}
