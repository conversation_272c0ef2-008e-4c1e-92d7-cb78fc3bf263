import { Paper, Skeleton, Stack } from '@mantine/core';
import React from 'react';

function SecuritySkeleton() {
  return (
    <Stack spacing="lg" m="auto" maw={650}>
      <Paper p="md" withBorder radius="lg">
        <Skeleton radius="lg" height="40px" mb={20} />
        <Skeleton radius="lg" height="40px" />
      </Paper>
      <Paper p="md" mt="lg" withBorder radius="lg">
        <Skeleton radius="lg" height="20px" mb={20} maw={200} />
        <Skeleton radius="sm" height="30px" mb={20} maw={250} />
        <Skeleton radius="lg" height="40px" mb={15} />
        <Skeleton radius="lg" height="40px" mb={20} />
        <Skeleton radius="lg" height="40px" />
      </Paper>
    </Stack>
  );
}

export default SecuritySkeleton;
