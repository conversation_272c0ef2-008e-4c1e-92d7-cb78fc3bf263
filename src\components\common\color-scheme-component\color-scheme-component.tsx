/* eslint-disable @typescript-eslint/no-explicit-any */

import { Box, useMantineTheme } from '@mantine/core';
import { mergeDeepRight } from 'ramda';
import { ColorSchemeComponentProps } from './types';

function ColorSchemeComponent<T extends(...args: any) => any>(
  props: ColorSchemeComponentProps<T>) {
  const {
    props: childProps = {},
    children = null,
    sharedProps = {},
    component: Component,
  } = props;
  const theme = useMantineTheme();
  const renderProps = theme.colorScheme === 'dark' ? childProps?.dark : childProps?.light;
  const finalProps = mergeDeepRight(sharedProps || {}, renderProps || {});

  return (
    <Box>
      {/* @ts-ignore */}
      <Component {...{ ...finalProps, children }} />
    </Box>
  );
}

export default ColorSchemeComponent;
