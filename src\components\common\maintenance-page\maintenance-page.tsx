import {
  Container,
  Title,
  useMantineTheme,
  Image,
  Group,
  Button,
} from '@mantine/core';
import React from 'react';
import classes from './style.module.scss';
import MetaTags from '../meta-tags';
import Link from 'next/link';
import { assetBaseUrl } from '@/data';

function MaintenancePage() {
  const theme = useMantineTheme();
  return (
    <div>
      <MetaTags />
      <Container className={classes.root}>
        <Image mx="auto" width={350} src={`${assetBaseUrl}/assets/logo/logo-full-en-dark.png`} />

        <Title
          ta="center"
          sx={{
            fontSize: 30,
            [theme.fn.smallerThan('sm')]: {
              fontSize: 24,
            },
            fontFamily: `Greycliff CF, ${theme.fontFamily}`,
          }}
        >
          We are doing some maintenance.
          <br />
          Be right back!
        </Title>
        <Title
          mt={50}
          ta="center"
          sx={{
            fontSize: 30,
            [theme.fn.smallerThan('sm')]: {
              fontSize: 24,
            },
            fontFamily: `Greycliff CF, ${theme.fontFamily}`,
          }}
        >
          .الموقع قيد الصيانة
          <br />
          !سنعود
        </Title>
        <Group mt="xl" position="center">
          <Button
            className={classes.control}
            size="lg"
            variant="default"
            color="gray"
            component={Link}
            href="https://kazawallet.trengohelp.com"
            target="_blank"
          >
            Help Center
          </Button>
          <Button
            className={classes.control}
            size="lg"
            component={Link}
            href="https://telegram.me/Kazawallet_Bot"
            target="_blank"
          >
            Telegram
          </Button>
        </Group>
      </Container>
    </div>
  );
}

export default MaintenancePage;
