import { ConfirmModal } from '@/components/common/confirm-modal';
import { CustomCopyButton } from '@/components/common/custom-copy-button';
import { UserApiResponse, regenerateApiKeyMutation } from '@/store/user';
import {
  Button,
  Group,
  Paper,
  Stack,
  Text,
  TextInput,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import { IconKey } from '@tabler/icons-react';
import { useMutation } from '@tanstack/react-query';
import useTranslation from 'next-translate/useTranslation';
import React, { useState } from 'react';

interface Props {
  data: UserApiResponse | undefined;
}
function ApiKeySection({ data }: Props) {
  const { t } = useTranslation();
  const [opened, { open, close }] = useDisclosure(false);
  const [apiKey, setApiKey] = useState(data?.apiKey);
  const regenerateApiKey = useMutation({
    ...regenerateApiKeyMutation(),
    onSuccess: (res) => {
      setApiKey(res?.apiKey);
      close();
      notifications.show({
        message: t('common:regenerateApiKeySuccessful'),
        color: 'blue',
      });
    },
    onError: () => {
      close();
    },
  });
  return (
    <>
      <Stack align="stretch" w="100%" mt="lg" spacing={0} maw={650}>
        <Paper p="md" mb="md" withBorder radius="lg">
          <Group mb="lg" align="end" position="apart">
            <TextInput
              w={{ xs: '65%', base: '100%' }}
              radius="lg"
              label={<Text tt="capitalize">{t('common:apiKey')}</Text>}
              placeholder={`${t('common:apiKey')}`}
              readOnly
              icon={<IconKey />}
              value={apiKey ?? ''}
              rightSection={<CustomCopyButton value={apiKey ?? ''} />}
            />
            <Button radius="lg" onClick={open} w={{ xs: '31%', base: '100%' }}>
              {t('common:reGenerateApiKey')}
            </Button>
          </Group>
        </Paper>
      </Stack>

      <ConfirmModal
        close={close}
        openedDefault={opened}
        isLoading={regenerateApiKey.isLoading}
        onClick={() => regenerateApiKey.mutate()}
        message={<div>{t('common:completeOperation')}</div>}
      />
    </>
  );
}

export default ApiKeySection;
