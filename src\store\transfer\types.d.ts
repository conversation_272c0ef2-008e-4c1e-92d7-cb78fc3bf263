import { z } from 'zod';
import {
  createTransferBackendRequestSchema,
  createTransferApiRequestSchema,
} from './request-transformer';
import { transfersApiResponseSchema } from './responses-transformers';
import { Pagination } from '@/types';
import { QueryFunctionContext } from '@tanstack/react-query';

export type Filter = {
  eqType?: 'transfer' | 'exchange' | 'received' | 'payment_link' | null;
  nEqType?: 'transfer' | 'exchange' | 'received' | 'payment_link' | null;
  currencyId?: number | string | null;
  search?: number | string | null;
  received?: number | string | null;
  sent?: number | string | null;
  isPayment?: boolean | null;
  isNotPayment?: boolean | null;
  createdFrom?: string | null;
  createdTo?: string | null;
  // status and payment method filters use to filter transfers in next api server
  // this key not found in transfers table
  status?: string | null;
  paymentMethod?: number | string | null;
};
export interface getTransfersQueryProps {
  populate?: {};
  pagination?: Pagination;
  filters?: Filter;
  params?: QueryFunctionContext;
}

export type CreateTransferBackendRequest = z.infer<
  typeof createTransferBackendRequestSchema
>;
export type CreateTransferApiRequest = z.infer<
  typeof createTransferApiRequestSchema
>;
export type TransfersApiResponse = z.infer<typeof transfersApiResponseSchema>;
