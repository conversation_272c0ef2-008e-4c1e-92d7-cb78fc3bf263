import { getTranslatedTextValue } from '@/utils';
import { useRouter } from 'next/router';
import React from 'react';

interface TranslatedTextValueProps {
  keyEn: string;
  keyAr: string | undefined | null;
}
function TranslatedTextValue({ keyEn, keyAr }: TranslatedTextValueProps) {
  const { locale } = useRouter();
  const text = getTranslatedTextValue(locale, keyEn, keyAr);
  return <span>{text}</span>;
}

export default TranslatedTextValue;
