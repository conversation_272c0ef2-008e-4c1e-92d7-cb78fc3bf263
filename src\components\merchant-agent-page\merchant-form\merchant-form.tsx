import {
  Button,
  Container,
  Grid,
  Group,
  Loader,
  Stack,
  Text,
  TextInput,
  Textarea,
  useMantineTheme,
} from '@mantine/core';
import useTranslation from 'next-translate/useTranslation';
import React, { useEffect } from 'react';
import styles from '../styles.module.scss';
import { useStyles } from './style';
import { useForm, zodResolver } from '@mantine/form';
import {
  CreateMerchantRequestApiRequest,
  createMerchantRequestFormSchema,
  createMerchantRequestMutation,
} from '@/store/merchant';
import { useMutation } from '@tanstack/react-query';
import { notifications } from '@mantine/notifications';
import { Captcha } from '@/components/common/captcha';
import { useCaptcha } from '@/hooks';
import { countries } from 'countries-list';
import SelectWithFlags from '@/components/profile/select-with-flags/select-with-flags';

function MerchantForm() {
  const { t } = useTranslation();
  const theme = useMantineTheme();
  const { classes } = useStyles();
  const { execute, reCaptchaRef, reset } = useCaptcha();
  const form = useForm<CreateMerchantRequestApiRequest>({
    initialValues: {
      email: '',
      message: '',
      name: '',
      firstName: '',
      lastName: '',
      code: '',
      phone: '',
      telegramOrWhatsapp: '',
      websiteOrBot: '',
      socialMediaLink: '',
    },

    validate: zodResolver(createMerchantRequestFormSchema(t)),
  });

  const { mutate, isLoading } = useMutation({
    ...createMerchantRequestMutation(),
    onSuccess() {
      form.reset();
      reset();
      notifications.show({
        message: t('common:requestSentSuccessfully'),
        color: 'blue',
      });
    },
    onError() {
      reset();
    },
  });
  const submit = (values: CreateMerchantRequestApiRequest) => {
    if (reCaptchaRef.current) {
      execute((token) => mutate({
        ...values,
        phone: countries[form.values?.code as 'AD'].phone + values.phone,
        token,
      }));
    } else {
      mutate({
        ...values,
        phone: countries[form.values?.code as 'AD'].phone + values.phone,
      });
    }
  };
  useEffect(() => {
    if (form.values.phone) {
      form.setFieldValue('phone', '');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [form.values?.code]);
  return (
    <div style={{ paddingTop: 40 }} id="merchant-form">
      <Container
        py={40}
        px={{
          xs: 20,
          sm: 30,
          lg: 40,
          base: 20,
        }}
        size={1200}
      >
        <Grid justify="space-between">
          <Grid.Col sm={5} span={12}>
            <Text
              ff="BauhausC,__Cairo_0685b6,__Cairo_Fallback_0685b6"
              size={43}
              weight="bold"
              color="white"
            >
              {t('common:formMerchantTitle')}
            </Text>
            <Text mt="xl" color="dimmed" size="lg" weight={500} mb="lg">
              {t('common:formMerchantDescription')}
            </Text>
          </Grid.Col>
          <Grid.Col sm={6} span={12}>
            <form onSubmit={form.onSubmit(submit)}>
              <Stack>
                <TextInput
                  withAsterisk
                  radius="md"
                  className={classes.input}
                  label={t('common:firstName')}
                  placeholder={t('common:firstName')}
                  {...form.getInputProps('firstName')}
                />
                <TextInput
                  withAsterisk
                  radius="md"
                  className={classes.input}
                  label={t('common:lastName')}
                  placeholder={t('common:lastName')}
                  {...form.getInputProps('lastName')}
                />
                <TextInput
                  withAsterisk
                  radius="md"
                  className={classes.input}
                  label={t('common:companyName')}
                  placeholder={t('common:companyName')}
                  {...form.getInputProps('name')}
                />

                <TextInput
                  withAsterisk
                  radius="md"
                  className={classes.input}
                  label={`${t('common:businessEmail')} ${t(
                    'common:shouldBeRegisteredInKazawallet',
                  )}`}
                  placeholder={t('common:businessEmail')}
                  {...form.getInputProps('email')}
                />
                <Group position="apart" align="start">
                  <SelectWithFlags
                    withAsterisk
                    labelValue="code"
                    clearable
                    className={classes.input}
                    w="34%"
                    radius="md"
                    data={countries}
                    label={t('common:code')}
                    placeholder={t('common:code') as string}
                    priorityList={['US', 'CA', 'GB', 'DE', 'RU', 'SY']}
                    rightSection={!countries && <Loader size="xs" />}
                    {...form.getInputProps('code')}
                  />
                  <TextInput
                    readOnly={!form.values?.code}
                    withAsterisk
                    className={classes.input}
                    w="61%"
                    radius="md"
                    iconWidth={40}
                    label={t('common:phone')}
                    placeholder={`${t('common:phone')}`}
                    {...form.getInputProps('phone')}
                  />
                </Group>
                <TextInput
                  withAsterisk
                  radius="md"
                  className={classes.input}
                  label={t('common:telegramOrWhatsapp')}
                  placeholder={t('common:telegramOrWhatsapp')}
                  {...form.getInputProps('telegramOrWhatsapp')}
                />
                <TextInput
                  withAsterisk
                  radius="md"
                  className={classes.input}
                  label={t('common:websiteOrBot')}
                  placeholder={t('common:websiteOrBot')}
                  {...form.getInputProps('websiteOrBot')}
                />
                <TextInput
                  withAsterisk
                  radius="md"
                  className={classes.input}
                  label={t('common:preferredSocialMedia')}
                  placeholder={t('common:preferredSocialMedia')}
                  {...form.getInputProps('socialMediaLink')}
                />
                <Textarea
                  withAsterisk
                  minRows={5}
                  radius="md"
                  className={classes.input}
                  label={t('common:explainToUsHowYouWork')}
                  placeholder={t('common:explainToUsHowYouWork')}
                  {...form.getInputProps('message')}
                />
                <Button
                  loading={isLoading}
                  type="submit"
                  c={theme.colors.primary[7]}
                  className={styles.aHero}
                >
                  {t('common:send')}
                </Button>
              </Stack>
            </form>
          </Grid.Col>
        </Grid>
      </Container>
      <Captcha reCaptchaRef={reCaptchaRef} />
    </div>
  );
}

export default MerchantForm;
