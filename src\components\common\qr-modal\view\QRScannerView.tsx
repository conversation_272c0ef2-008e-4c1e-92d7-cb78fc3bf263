import {
  <PERSON>, But<PERSON>, Stack, Text, Center, useMantineTheme, ActionIcon, Tooltip,
} from '@mantine/core';
import { Scanner, outline } from '@yudiel/react-qr-scanner';
import useTranslation from 'next-translate/useTranslation';
import { useRouter } from 'next/router';
import { Icon } from '@/components/common/icon';
import { DetectedBarcode } from '../qr-helpers';
import { QR_CONFIG, QRSizeConfig } from '../qr-constants';

interface QRScannerViewProps {
  showScanner: boolean;
  scanError: Error | null;
  sizes: QRSizeConfig;
  isMobile: boolean;
  onScanComplete: (codes: DetectedBarcode[]) => void;
  onScanError: (error: unknown) => void;
  onImageUpload: (file: File | null) => void;
  // eslint-disable-next-line react/require-default-props
  onGenerateQR?: () => void;
  // eslint-disable-next-line react/require-default-props
  onClose?: () => void;
}

// eslint-disable-next-line complexity, sonarjs/cognitive-complexity
export function QRScannerView({
  showScanner,
  scanError,
  sizes,
  isMobile,
  onScanComplete,
  onScanError,
  onImageUpload,
  onGenerateQR,
  onClose,
}: QRScannerViewProps) {
  const { t } = useTranslation();
  const theme = useMantineTheme();
  const { locale } = useRouter();
  const isRTL = locale === 'ar';

  return (
    <Box
      sx={{
        position: 'relative',
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
      }}
    >

      {/* Camera Scanner - Full Background */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          borderRadius: theme.radius.md,
          overflow: 'hidden',
        }}
      >
        {showScanner && (
          <Box
            sx={{
              width: '100%',
              height: '100%',
              position: 'relative',
              '& [data-testid="finder"]': {
                border: `3px solid ${theme.colors.green[5]} !important`,
                borderRadius: '12px !important',
                width: `${sizes.scannerFinderWidth} !important`,
                height: `${sizes.scannerFinderHeight} !important`,
              },
              '& [data-testid="finder"]::before, & [data-testid="finder"]::after': {
                borderColor: `${theme.colors.green[4]} !important`,
                borderWidth: '4px !important',
              },
            }}
          >
            <Scanner
              onScan={onScanComplete}
              onError={onScanError}
              constraints={{
                aspectRatio: 1,
                facingMode: 'environment',
              }}
              scanDelay={QR_CONFIG.SCAN_DELAY}
              components={{
                finder: true,
                torch: true,
                zoom: true,
                tracker: outline,
              }}
              styles={{
                container: {
                  width: '100%',
                  height: '100%',
                },
                video: {
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                },
              }}
              sound
            />
          </Box>
        )}
      </Box>

      {/* Top Bar with Album and Close Buttons */}
      <Box
        sx={{
          position: 'relative',
          zIndex: 10,
          display: 'flex',
          justifyContent: 'space-between',
          padding: theme.spacing.xs,
          background: theme.fn.rgba(theme.colors.dark[9], 0.6),
          backdropFilter: 'blur(4px)',
        }}
      >
        {/* Close button */}
        {onClose ? (
          <Tooltip label={t('common:close')} position={isRTL ? 'right' : 'left'} withArrow>
            <ActionIcon
              onClick={onClose}
              size={isMobile ? 'md' : 'lg'}
              variant="filled"
              color="dark"
              sx={{
                backgroundColor: theme.fn.rgba(theme.colors.dark[6], 0.8),
                '&:hover': { backgroundColor: theme.fn.rgba(theme.colors.dark[5], 0.9) },
              }}
            >
              <Icon icon="x" size={isMobile ? '0.9rem' : '1rem'} color="white" />
            </ActionIcon>
          </Tooltip>
        ) : (
          <Box />
        )}
        <input
          id="qr-album-input"
          type="file"
          accept="image/*"
          hidden
          onChange={(e) => {
            const file = e.target.files?.[0] ?? null;
            onImageUpload(file);
            e.target.value = '';
          }}
        />

        <Button
          component="label"
          htmlFor="qr-album-input"
          variant="filled"
          color="dark"
          size={isMobile ? 'sm' : 'md'}
          leftIcon={<Icon icon="photo" size={isMobile ? '0.9rem' : '1rem'} color="white" />}
          sx={{
            backgroundColor: theme.fn.rgba(theme.colors.dark[6], 0.8),
            '&:hover': { backgroundColor: theme.fn.rgba(theme.colors.dark[5], 0.9) },
            fontSize: isMobile ? '0.875rem' : '1rem',
          }}
        >
          {t('common:album')}
        </Button>
      </Box>

      {/* Spacer */}
      <Box sx={{ flex: 1 }} />

      {/* Bottom Section */}
      <Box
        sx={{
          position: 'relative',
          zIndex: 10,
          paddingTop: isMobile ? theme.spacing.xs : theme.spacing.sm,
          paddingBottom: theme.spacing.xs,
          background: theme.fn.rgba(theme.colors.dark[9], 0.8),
          backdropFilter: 'blur(8px)',
        }}
      >
        <Stack spacing={isMobile ? 'sm' : 'md'} align="center">
          <Stack spacing={4} align="center">
            <Text
              size={isMobile ? 'md' : 'lg'}
              weight={600}
              color="white"
              align="center"
              sx={{ lineHeight: 1.3 }}
            >
              {t('common:aimToScanQrCode')}
            </Text>
            <Text
              size={isMobile ? 'xs' : 'sm'}
              color="dimmed"
              align="center"
              sx={{ lineHeight: 1.2 }}
            >
              {t('common:orSelectFromAlbum')}
            </Text>
          </Stack>

          {onGenerateQR && (
            <Button
              fullWidth
              onClick={onGenerateQR}
              size={isMobile ? 'sm' : 'md'}
              variant="filled"
              color="green"
              sx={{
                maxWidth: isMobile ? '70%' : '300px',
                fontSize: isMobile ? '0.875rem' : '1rem',
              }}
            >
              {t('common:receiveQrCode')}
            </Button>
          )}
        </Stack>
      </Box>

      {/* Error Message */}
      {scanError && (
        <Center
          sx={{
            position: 'absolute',
            bottom: isMobile ? 100 : 120,
            left: isMobile ? theme.spacing.sm : theme.spacing.md,
            right: isMobile ? theme.spacing.sm : theme.spacing.md,
            zIndex: 15,
            padding: isMobile ? theme.spacing.xs : theme.spacing.sm,
            background: theme.fn.rgba(theme.colors.red[6], 0.9),
            color: 'white',
            borderRadius: theme.radius.sm,
            backdropFilter: 'blur(4px)',
          }}
        >
          <Stack spacing="xs" align="center">
            <Text size={isMobile ? 'xs' : 'sm'} align="center" sx={{ lineHeight: 1.3 }}>
              {scanError.message}
            </Text>
          </Stack>
        </Center>
      )}
    </Box>
  );
}
