import {
  Mo<PERSON>, <PERSON>ack, Group, Text, Button, Textarea,
  useMantineTheme,
  rem,
} from '@mantine/core';
import { useState, useEffect } from 'react';
import useTranslation from 'next-translate/useTranslation';

interface PrivateNoteModalProps {
  opened: boolean;
  onClose: () => void;
  onSave: (note: string) => void;
  // eslint-disable-next-line react/require-default-props
  initialNote?: string;
}

export function PrivateNoteModal({
  opened,
  onClose,
  onSave,
  initialNote = '',
}: PrivateNoteModalProps) {
  const { t } = useTranslation();
  const [noteText, setNoteText] = useState(initialNote);
  const theme = useMantineTheme();

  // Update noteText when initialNote changes or modal opens
  useEffect(() => {
    if (opened) {
      setNoteText(initialNote);
    }
  }, [opened, initialNote]);

  const handleSave = () => {
    onSave(noteText);
    onClose();
  };

  const handleCancel = () => {
    setNoteText(initialNote); // Reset to initial value
    onClose();
  };

  return (
    <Modal
      opened={opened}
      onClose={handleCancel}
      title=""
      centered
      size="md"
      radius="xl"
      zIndex={1001}
      styles={{
        header: {
          padding: '20px 24px 0 24px',
          borderBottom: 'none',
        },
        body: {
          padding: '0 24px 24px 24px',
        },
        close: {
          border: 'none',
          backgroundColor: theme.colorScheme === 'dark' ? theme.colors.dark[5] : '#f8f9fa',
          borderRadius: '50%',
          width: rem(32),
          height: rem(32),
          color: theme.colorScheme === 'dark' ? theme.white : '#495057',
          '&:hover': {
            backgroundColor: theme.colorScheme === 'dark' ? theme.colors.dark[4] : '#e9ecef',
          },
        },
        content: {
          backgroundColor: theme.colorScheme === 'dark' ? theme.colors.dark[7] : theme.white,
          zIndex: 1002,
        },
        overlay: {
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          zIndex: 1001,
        },
      }}
    >
      <Stack spacing="lg">
        <Text size="lg" weight={600}>
          {t('common:makePrivateNote')}
        </Text>

        <Text size="sm" color="dimmed">
          {t('common:privateNoteDescription')}
        </Text>

        <Textarea
          placeholder={t('common:enterPrivateNote')}
          value={noteText}
          onChange={(event) => setNoteText(event.currentTarget.value)}
          minRows={4}
          maxRows={6}
          autosize
          radius="md"
          size="sm"
          styles={{
            input: {
              border: '1px solid #e9ecef',
              '&:focus': {
                borderColor: '#007bff',
              },
            },
          }}
        />

        <Group position="apart" mt="md">
          <Button
            variant="subtle"
            onClick={handleCancel}
            sx={{
              color: '#6c757d',
              '&:hover': {
                backgroundColor: '#f8f9fa',
              },
            }}
          >
            {t('common:cancel')}
          </Button>
          <Button
            onClick={handleSave}
            sx={{
              backgroundColor: '#a4d65e',
              '&:hover': {
                backgroundColor: '#95c653',
              },
            }}
          >
            {t('common:save')}
          </Button>
        </Group>
      </Stack>
    </Modal>
  );
}
