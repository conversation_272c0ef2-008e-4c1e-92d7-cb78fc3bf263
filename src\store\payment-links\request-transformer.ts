/**
 * create payment link request schema
 */
import { z } from 'zod';

export const createPaymentLinkApiRequestSchema = z.object({
  currency: z.string().or(z.number()),
  amount: z.number().or(z.string()),
  redirectUrl: z.string().nullable().optional(),
  expiryDate: z.string().nullable().optional(),
});

export const createPaymentLinkBackendRequestSchema = createPaymentLinkApiRequestSchema.transform((data) => ({
  data: {
    currency: data.currency,
    amount: `${data.amount}`,
    expiry_date: data?.expiryDate,
    redirect_url: data?.redirectUrl,
  },
}));
