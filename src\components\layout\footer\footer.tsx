import {
  Container, Group, Text, Image,
} from '@mantine/core';

import useTranslation from 'next-translate/useTranslation';
import Link from 'next/link';
import { useStyles } from './style';
import { assetBaseUrl } from '@/data';

interface FooterSimpleProps {
  links: { link: string; label: string, target?:string }[];
}

export default function Footer({ links }: FooterSimpleProps) {
  const { classes } = useStyles();
  const { t } = useTranslation();
  const items = links.map((link) => (
    <Link
      key={link.label}
      href={link.link}
      style={{ textDecoration: 'none' }}
      target={link?.target}
    >
      <Text color="dimmed" size="sm">
        {t(`common:${link.label}`)}
      </Text>
    </Link>
  ));

  return (
    <div className={classes.footer}>
      <Container className={classes.inner} size="md">
        <Image alt="kazawallet-logo" width={40} height={40} src={`${assetBaseUrl}/assets/logo/logo.png`} />
        <Group position="center" className={classes.links}>
          {items}
        </Group>
      </Container>
    </div>
  );
}
