/**
 * This component renders a Exchange page.
 *
 * @description
 * This page used to convert between two currencies and display the rate and exchange fee.
 * In this page there are to currencies selects,the first one is to select "give" currency and the second one is to select "get" currency.
 * And there is a form to create "transfer" with "exchange" type.
 * To create transfer should select "give and get" currencies and fill the amount for each one and click to "exchange" button.
 * There is a switch button to replace between the selected currencies.
 *
 */
import { Layout } from '@/components/layout/layout';
import MetaTags from '@/components/common/meta-tags';
import useTranslation from 'next-translate/useTranslation';
import { Container } from '@mantine/core';
import ExchangeForm from '@/components/exchange/exchange-form';
import { PageTitle } from '@/components/common/page-title';

import { getRatesQuery } from '@/store/rate';
import { useQuery } from '@tanstack/react-query';

export default function Exchange() {
  const { t } = useTranslation();
  const { data } = useQuery(getRatesQuery());
  return (
    <div>
      <MetaTags title={t('common:exchange')} />
      <Layout>
        <Container p={0} maw={800}>
          <PageTitle title="exchange" />
          <ExchangeForm rates={data} />
        </Container>
      </Layout>
    </div>
  );
}
export async function getServerSideProps() {
  return { props: {} };
}
