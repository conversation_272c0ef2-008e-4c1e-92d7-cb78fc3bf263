import { assetBaseUrl } from '@/data';
import {
  ActionIcon, Card, Group, Title, Avatar, Grid,
} from '@mantine/core';
import { IconEye, IconTrash } from '@tabler/icons-react';
import Link from 'next/link';
import React from 'react';

type FileType = { id: number; name: string; url: string };
interface DropzoneFileProps {
  deleteFile: (id: number) => void;
  file: FileType;
}
function DropzoneFile(props: DropzoneFileProps) {
  const {
    deleteFile,
    file: { url, id, name },
  } = props;

  //* ************************************************* */
  return (
    <Card radius="md" mt={3} withBorder>
      <Grid justify="space-between" gutter={0}>
        <Grid.Col span={8}>
          <Group position="left" spacing={4}>
            <Avatar
              radius={30}
              size={30}
              src={`${assetBaseUrl}/assets/icons/img.png`}
              maw={30}
              alt="drop"
            />
            <Title
              order={6}
              maw={200}
              lineClamp={1}
              sx={{ textAlign: 'start' }}
            >
              {name}
            </Title>
          </Group>
        </Grid.Col>
        <Grid.Col span={4}>
          <Group spacing={5} position="right">
            {/* {showEye && ( */}
            <ActionIcon
              variant="default"
              component={Link}
              href={url}
              target="_blank"
            >
              <IconEye size={18} color="gray" />
            </ActionIcon>
            {/* )} */}
            <ActionIcon variant="default">
              <IconTrash
                size={18}
                color="red"
                onClick={() => {
                  deleteFile(id);
                }}
              />
            </ActionIcon>
          </Group>
        </Grid.Col>
      </Grid>
    </Card>
  );
}

export default DropzoneFile;
