import PaymentTransactionCard from '@/components/history/payment-transaction-card';

interface Props {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  itemData: any;
}

export function HistoryItem({ itemData }: Props) {
  return (
    <PaymentTransactionCard
      paymentMethod={itemData?.paymentMethod ?? undefined}
      currencyFrom={itemData?.currencyFrom ?? itemData?.currency}
      currencyTo={itemData?.currencyTo ?? undefined}
      note={itemData?.note ?? undefined}
      userFrom={itemData?.userFrom ?? itemData?.user}
      userTo={itemData?.userTo ?? undefined}
      id={itemData?.transactionId}
      amount={itemData?.amount}
      actualAmount={itemData?.actualAmount}
      date={itemData?.createdAt}
      status={itemData?.status ?? 'approved'}
      operationType={
        (itemData?.type === 'transfer' || itemData?.type === 'payment_link')
        && itemData?.userTo
        && itemData?.userTo?.accountId === itemData?.userAccountId
          ? 'received'
          : itemData?.type
      }
      fees={itemData?.snapshot?.fees ?? itemData?.totalFees}
      fields={itemData?.fields ?? undefined}
      rate={itemData?.snapshot?.rate ?? 0}
      adminMessage={itemData?.adminMessage}
      actualType={itemData?.type}
      gif={itemData?.gif}
      cryptoGetaway={itemData?.cryptoGetaway}
      originPaymentAmount={itemData?.originPaymentAmount}
    />
  );
}
