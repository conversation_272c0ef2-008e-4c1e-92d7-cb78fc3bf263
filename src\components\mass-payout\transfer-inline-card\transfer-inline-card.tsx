/**
 * This component renders list of transfer items.
 * There to cases of list items rendering
 *  - list items render after check mass payout api call
 *  - list items render after complete mass payout api call
 */
import {
  TextInput,
  Text,
  SimpleGrid,
  Group,
  ActionIcon,
  Grid,
} from '@mantine/core';
import React from 'react';

import useTranslation from 'next-translate/useTranslation';
import { UserApiResponse } from '@/store/user';

import currency from 'currency.js';
import { GlobalCard } from '@/components/common/global-card';
import { Icon } from '@/components/common/icon';

interface Props {
  selectedCurrency: UserApiResponse['currencies'][0] | undefined;
  amount: number;
  account: string;
  note: string;
  error: string;
  success: boolean;
  id: number;
  isReady:boolean
}
export default function TransferInLineCard({
  account,
  amount,
  note,
  selectedCurrency,
  error,
  success,
  id,
  isReady,
}: Props) {
  const { t } = useTranslation();

  return (
    <GlobalCard props={{}} onClick={() => {}}>
      <Grid>
        <Grid.Col span={1}>
          <Group
            h="100%"
            sx={(theme) => ({
              [theme.fn.smallerThan('sm')]: {
                alignItems: 'start',
              },
            })}
            align="center"
          >
            <Text w={500} span>
              {id}
              -
            </Text>
          </Group>
        </Grid.Col>
        <Grid.Col span={11}>
          <SimpleGrid
            cols={success !== undefined ? 4 : 3}
            sx={{ alignItems: 'end' }}
            breakpoints={[
              { maxWidth: 'sm', cols: 2, spacing: 'sm' },
              { maxWidth: 'xs', cols: 1, spacing: 'sm' },
            ]}
          >
            <TextInput
              readOnly
              radius="lg"
              size="sm"
              label={<Text tt="capitalize">{t('common:amount')}</Text>}
              value={
                currency(amount, {
                  symbol: '',
                  precision: selectedCurrency?.precision ?? 2,
                }).format() ?? ''
              }
            />

            <TextInput
              readOnly
              radius="lg"
              placeholder="account number/email"
              size="sm"
              label={
                <Text tt="capitalize">{t('common:accountNumberEmail')}</Text>
              }
              value={account}
            />
            <TextInput
              readOnly
              radius="lg"
              placeholder="Note"
              size="sm"
              label={<Text tt="capitalize">{t('common:note')}</Text>}
              value={note}
            />

            <Group position="center" align="center">
              {success ? (
                <>
                  <ActionIcon
                    sx={{ cursor: 'default' }}
                    size="lg"
                    radius={50}
                    variant="outline"
                    color="green"
                  >
                    <Icon icon="check" size={20} color="green" />
                  </ActionIcon>
                  <Text weight={500} color="green">
                    {t(`common:${isReady ? 'ready' : 'success'}`)}
                  </Text>
                </>
              ) : (
                <>
                  <ActionIcon
                    sx={{ cursor: 'default' }}
                    size="lg"
                    radius={50}
                    variant="outline"
                    color="red"
                  >
                    <Icon icon="x" size={20} color="red" />
                  </ActionIcon>
                  <Text weight={500} color="red">
                    {t('common:failed')}
                  </Text>
                </>
              )}
            </Group>
          </SimpleGrid>
        </Grid.Col>
      </Grid>
      {error && (
        <Text mt="xs" color="red">
          <span>{`${t('common:error')}: `}</span>
          {t(`errors:${error}`)}
        </Text>
      )}
    </GlobalCard>
  );
}
