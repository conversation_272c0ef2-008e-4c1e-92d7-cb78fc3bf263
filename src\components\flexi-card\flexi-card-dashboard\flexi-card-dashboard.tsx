/* eslint-disable max-lines */
import {
  Container, Stack, Text, Group, Grid, Box, ActionIcon,
  Tooltip,
  rem,
  Flex,
  Button,
  useMantineTheme,
} from '@mantine/core';
import { Carousel } from '@mantine/carousel';
import { useState, useEffect, useRef } from 'react';
import {
  IconPlus, IconRefresh, IconSnowflake, IconCreditCard, IconFileText,
} from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';

import { CardTransactionsList } from '../card-transactions';
import { TopUpModal } from '../card-top-up';
import { FreezeCardModal } from '../freeze-card-modal/freeze-card-modal';
import { SupportedMerchantsModal } from '../supported-merchants-modal';

import { useStyles, neutralIconStyles, topUpStyles } from './style';
import { FlipPaymentCard } from './flip-card/card';
import { CardCredentialsModal } from './flip-card/card-detail-modal';
import { PinVerificationModal } from './flip-card/pin-verification-modal';
import { mockCards, mockTransactions } from '../mock-data';
import { useRouter } from 'next/router';

export function FlexiCardDashboard() {
  const { t } = useTranslation();
  const { classes } = useStyles();
  const [topUpModalOpen, setTopUpModalOpen] = useState(false);
  const [freezeModalOpen, setFreezeModalOpen] = useState(false);
  const [merchantsModalOpen, setMerchantsModalOpen] = useState(false);

  const [activeTab, setActiveTab] = useState('all');
  const [activeCardIndex, setActiveCardIndex] = useState(0);
  const [showCredentials, setShowCredentials] = useState(false);
  const [credModalOpen, setCredModalOpen] = useState(false);
  const [pinModalOpen, setPinModalOpen] = useState(false);
  const [pinVerificationAction, setPinVerificationAction] = useState<'credentials' | 'details'>('credentials');
  const [showCredentialsOnCard, setShowCredentialsOnCard] = useState(false);
  const [credentialsTimer, setCredentialsTimer] = useState(60);
  // eslint-disable-next-line no-undef
  const credentialsTimerRef = useRef<NodeJS.Timeout | null>(null);
  const activeCard = mockCards[activeCardIndex];

  const theme = useMantineTheme();
  const router = useRouter();

  const isDark = theme.colorScheme === 'dark';

  const pillStyle = {
    backgroundColor: 'transparent',
    border: `1px solid ${isDark ? theme.white : theme.black}`,
    color: isDark ? theme.white : theme.black,
    fontWeight: 700,
    height: 44,
    paddingInline: 32,
    borderRadius: 9999,
    transition: 'background-color 120ms ease',
    '&:hover': {
      backgroundColor: isDark ? theme.colors.dark[5] : theme.colors.gray[0],
    },
  };

  const handleMenuAction = (action:string) => {
    // eslint-disable-next-line no-console
    console.log(action);
    if (action === 'freeze') {
      setFreezeModalOpen(true);
    } else if (action === 'merchants') {
      setMerchantsModalOpen(true);
    }
  };

  const startCredentialsTimer = () => {
    // Clear any existing timer
    if (credentialsTimerRef.current) {
      clearInterval(credentialsTimerRef.current);
    }

    setShowCredentialsOnCard(true);
    setCredentialsTimer(60);

    credentialsTimerRef.current = setInterval(() => {
      setCredentialsTimer((prev) => {
        if (prev <= 1) {
          setShowCredentialsOnCard(false);
          if (credentialsTimerRef.current) {
            clearInterval(credentialsTimerRef.current);
            credentialsTimerRef.current = null;
          }
          return 60;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const resetCredentialsTimer = () => {
    if (credentialsTimerRef.current) {
      clearInterval(credentialsTimerRef.current);
      credentialsTimerRef.current = null;
    }
    setShowCredentialsOnCard(false);
    setCredentialsTimer(60);
  };

  // Cleanup timer on component unmount
  useEffect(() => () => {
    if (credentialsTimerRef.current) {
      clearInterval(credentialsTimerRef.current);
    }
  }, []);

  // Reset timer when active card changes
  useEffect(() => {
    resetCredentialsTimer();
  }, [activeCardIndex]);

  return (
    <Container size="md" px="md" sx={{ overflow: 'hidden' }}>
      <Stack spacing="lg">
        <Grid gutter="sm" align="stretch" sx={{ margin: 0 }}>
          <Grid.Col span={12} md={6} sx={{ borderRadius: 20 }}>
            <Stack spacing="lg" sx={{ overflow: 'hidden' }}>
              <Group position="apart" align="center">
                <Text size="xl" weight={700}>
                  {t('common:myCard')}
                </Text>
                <Flex
                  align="center"
                  gap={12}
                  p={8}
                  sx={{
                    backgroundColor: isDark ? '#25262B' : theme.colors.gray[0],
                    borderRadius: theme.radius.md,
                    cursor: 'pointer',
                    transition: 'background-color 120ms ease',
                    '&:hover': {
                      backgroundColor: isDark ? theme.colors.gray[7] : theme.colors.gray[1],
                    },
                  }}
                  onClick={() => router.push('/flexi-card/apply')}
                >
                  <ActionIcon
                    variant="filled"
                    radius="xl"
                    size="md"
                    sx={{
                      backgroundColor: isDark ? theme.colors.gray[2] : theme.colors.gray[8],
                      color: isDark ? theme.colors.gray[8] : theme.white,
                      border: `2px solid ${isDark ? theme.colors.gray[4] : theme.colors.gray[5]}`,
                      '&:hover': {
                        backgroundColor: isDark ? theme.colors.gray[1] : theme.colors.gray[7],
                      },
                    }}
                  >
                    <IconPlus size={16} stroke={2} />
                  </ActionIcon>

                  <Text size="sm" weight={700}>
                    {t('common:getNewCard')}
                  </Text>
                </Flex>
              </Group>

              <Box className={classes.carouselContainer}>
                <Carousel
                  withIndicators={mockCards?.length > 1}
                  withControls={false}
                  align="center"
                  slideGap={0}
                  slidesToScroll={1}
                  loop={false}
                  slideSize="100%"
                  initialSlide={0}
                  onSlideChange={(index) => {
                    setActiveCardIndex(index);
                    setShowCredentials(false);
                    resetCredentialsTimer();
                  }}
                  styles={{
                    viewport: {
                      overflow: 'hidden',
                    },
                    container: {
                      display: 'flex',
                      alignItems: 'center',
                    },
                  }}
                >
                  {mockCards.map((card, index) => (
                    <Carousel.Slide key={card.id}>
                      <Box className={classes.fixedCardShell}>
                        <FlipPaymentCard
                          card={{
                            cardNumber: card.cardNumber,
                            cardHolderName: card.cardHolderName,
                            expiryDate: card.expiryDate,
                            cvv: card.cvv,
                            cardType: card.cardType,
                            brand: card.brand,
                          }}
                          flipped={showCredentials && index === activeCardIndex}
                          showCredentialsOnCard={showCredentialsOnCard && index === activeCardIndex}
                          credentialsTimer={credentialsTimer}
                          onToggle={() => {
                            if (index === activeCardIndex) setShowCredentials((v) => !v);
                          }}
                          onViewCredentials={() => {
                            if (index === activeCardIndex) {
                              setPinVerificationAction('credentials');
                              setPinModalOpen(true);
                            }
                          }}
                        />
                      </Box>
                    </Carousel.Slide>
                  ))}
                </Carousel>
              </Box>
            </Stack>
          </Grid.Col>

          {/* Right Column - Card Info and Controls */}
          <Grid.Col span={12} md={6} sx={{ borderRadius: 20 }}>
            <Box className={classes.balanceContainer}>
              <Box>
                <Group position="apart" align="center" mb="xs">
                  <Text size="md" color="dimmed" weight={500}>
                    {t('common:cardBalance')}
                  </Text>
                  <Group spacing={8}>
                    <Tooltip label={t('common:refresh')}>
                      <ActionIcon size="md" variant="subtle" color="gray" radius="xl">
                        <IconRefresh size={18} />
                      </ActionIcon>
                    </Tooltip>
                  </Group>
                </Group>

                <Group align="baseline" spacing={4}>
                  <Text className={classes.balanceAmount}>
                    $
                    {activeCard.balance.toFixed(2)}
                  </Text>
                  <Text size="md" color="dimmed" weight={500}>
                    {activeCard.currency}
                  </Text>
                </Group>
              </Box>

              <Flex w="100%" gap={rem(16)} wrap="wrap" align="center">
                <Button
                  variant="filled"
                  color="dark"
                  radius="xl"
                  size="md"
                  leftIcon={<IconPlus size={16} />}
                  styles={topUpStyles}
                  onClick={() => setTopUpModalOpen(true)}
                >
                  {t('common:topUp')}
                </Button>

                {/* {t('common:freezeCard')} – icon only */}
                <Tooltip label={t('common:freezCard')} withArrow>
                  <ActionIcon
                    size={rem(48)}
                    radius="xl"
                    variant="subtle"
                    styles={neutralIconStyles}
                    onClick={() => setFreezeModalOpen(true)}
                  >
                    <IconSnowflake size={22} />
                  </ActionIcon>
                </Tooltip>

                {/* {t('common:merchantSupported')} – icon only */}
                <Tooltip label={t('common:merchantSupported')} withArrow>
                  <ActionIcon
                    size={rem(48)}
                    radius="xl"
                    variant="subtle"
                    styles={neutralIconStyles}
                    onClick={() => handleMenuAction('merchants')}
                  >
                    <IconFileText size={22} />
                  </ActionIcon>
                </Tooltip>

                {/* Details – icon only */}
                <Tooltip label={t('common:details')} withArrow>
                  <ActionIcon
                    size={rem(48)}
                    radius="xl"
                    variant="subtle"
                    styles={neutralIconStyles}
                    onClick={() => {
                      setPinVerificationAction('details');
                      setPinModalOpen(true);
                    }}
                  >
                    <IconCreditCard size={22} />
                  </ActionIcon>
                </Tooltip>
              </Flex>

            </Box>
          </Grid.Col>
        </Grid>
        <Group position="center" spacing={24}>
          <Button
            variant="subtle"
            sx={pillStyle}
          >
            {t('common:addToGooglePayAndApplePay')}
          </Button>
        </Group>

        {/* Activities Section */}
        <CardTransactionsList
          transactions={mockTransactions}
          activeTab={activeTab}
          setActiveTab={setActiveTab}
        />

        {/* Top Up Modal */}
        <TopUpModal
          opened={topUpModalOpen}
          onClose={() => setTopUpModalOpen(false)}
          card={activeCard}
        />

        <CardCredentialsModal
          opened={credModalOpen}
          onClose={() => {
            setCredModalOpen(false);
            resetCredentialsTimer();
          }}
          card={{
            cardNumber: activeCard.cardNumber,
            expiryDate: activeCard.expiryDate,
            cardHolderName: activeCard.cardHolderName,
            billingAddress: '221B Baker St, London NW1 6XE, UK',
          }}
          revealSeconds={60}
        />

        {/* Freeze Card Modal */}
        <FreezeCardModal
          opened={freezeModalOpen}
          onClose={() => setFreezeModalOpen(false)}
          onFreeze={() => {
            // Implement freeze card functionality here
            // eslint-disable-next-line no-console
            console.log('Card frozen');
            setFreezeModalOpen(false);
          }}
        />

        {/* PIN Verification Modal */}
        <PinVerificationModal
          opened={pinModalOpen}
          onClose={() => setPinModalOpen(false)}
          onVerified={() => {
            setPinModalOpen(false);
            if (pinVerificationAction === 'credentials') {
              startCredentialsTimer();
            } else if (pinVerificationAction === 'details') {
              setCredModalOpen(true);
            }
          }}
          title={t('common:securityCheck')}
          description={t('common:pciDssCompliance')}
        />

        {/* Supported Merchants Modal */}
        <SupportedMerchantsModal
          opened={merchantsModalOpen}
          onClose={() => setMerchantsModalOpen(false)}
        />
      </Stack>
    </Container>
  );
}
