import { apiEndpoints } from '@/data';
import { ApiClient } from '@/lib';
import { handleApiError } from '@/utils/api-utils/handle-backend-error';
import { getPaymentMethodsQueryProps } from './types';

enum queryKeys {
  paymentMethods = 'payment-methods',
}
/**
 * @description function calls handler in "/payment-methods" api route to return payment methods
 * @param props
 * @returns array of payment method with pagination
 */
const getPaymentMethodsRequest = (props: getPaymentMethodsQueryProps) => {
  const { populate, pagination, filters } = props;
  return ApiClient.get(apiEndpoints.paymentMethods(), {
    params: {
      showInRatesBar: filters?.showInRatesBar,
      depositCurrencyId: filters?.depositCurrencyId,
      withdrawCurrencyId: filters?.withdrawCurrencyId,
      depositCurrencies: populate?.depositCurrencies,
      withdrawCurrencies: populate?.withdrawCurrencies,
      depositCustomFields: populate?.depositCustomFields,
      withdrawcustomFields: populate?.withdrawcustomFields,
      icon: populate?.icon,
      page: pagination?.page,
      pageSize: pagination?.pageSize,
      start: pagination?.start,
      limit: pagination?.limit,
    },
  })
    .then((res) => res?.data)
    .catch((e) => {
      handleApiError(e, true);
      throw e.response?.data;
    });
};
export const getPaymentMethodsQuery = (props: getPaymentMethodsQueryProps) => ({
  queryKey: [queryKeys.paymentMethods, props?.filters],
  queryFn: () => getPaymentMethodsRequest(props),
  refetchOnWindowFocus: false,
});
