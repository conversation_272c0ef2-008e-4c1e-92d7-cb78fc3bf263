import { useUncontrolled } from '@mantine/hooks';
import { useStyles } from './style';
import {
  Checkbox, Image, Text, UnstyledButton,
} from '@mantine/core';
import { TranslatedTextValue } from '@/components/common/translated-text-value';

interface WalletItemProps {
  defaultChecked: boolean;
  onChange(checked: boolean): void;
  title: string;
  titleAr: string|null;
  description: string;
  image: string;
}

export function WalletItem({
  defaultChecked,
  onChange,
  title,
  titleAr,
  description,
  image,
  disabled,
}: WalletItemProps &
  Omit<React.ComponentPropsWithoutRef<'button'>, keyof WalletItemProps>) {
  const { classes } = useStyles();

  const [value, handleChange] = useUncontrolled({
    defaultValue: defaultChecked,
    finalValue: false,
    onChange,
  });

  const handleItemClick = () => {
    handleChange(!value);
  };

  return (
    <UnstyledButton
      disabled={disabled}
      miw={310}
      onClick={handleItemClick}
      className={`${classes.button} ${value ? classes.checkedButton : ''} ${
        disabled ? classes.disabledButton : ''
      }  `}
    >
      <Image src={image} alt={title} width={40} height={40} />

      <div className={classes.body}>
        <Text c="dimmed" size="xs" lh={1} mb={5}>
          {description}
        </Text>
        <Text fw={500} size="sm" lh={1}>
          <TranslatedTextValue keyEn={title} keyAr={titleAr} />
        </Text>
      </div>

      <Checkbox
        disabled={disabled}
        checked={value}
        onChange={() => {}}
        tabIndex={-1}
        styles={{ input: { cursor: 'pointer' } }}
      />
    </UnstyledButton>
  );
}
