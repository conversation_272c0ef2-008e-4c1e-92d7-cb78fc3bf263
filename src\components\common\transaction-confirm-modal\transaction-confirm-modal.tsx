import {
  Box, Button, Group, Modal, Stack, Text,
} from '@mantine/core';
import { IconExclamationCircle } from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';
import React, { ReactNode } from 'react';
import { Icon } from '../icon';
import { operationIcon } from '@/utils/operations-utils/operation-icons';

interface TransactionConfirmModalProps {
  isLoading: boolean;
  onClick: () => void;
  openedDefault: boolean;
  close: () => void;
  children: ReactNode;
  operationType: 'transfer' | 'exchange' | 'deposit' | 'withdraw';
}

function TransactionConfirmModal({
  isLoading,
  onClick,
  openedDefault,
  close,
  children,
  operationType,
}: TransactionConfirmModalProps) {
  const { t } = useTranslation();
  return (
    <Modal radius="lg" centered opened={openedDefault} onClose={close}>
      <Box mb="md">
        <Group position="center">
          <IconExclamationCircle size={48} color="#1C6092" />
        </Group>
        <Text fw={700} size="lg" ta="center">
          {t('common:attention')}
        </Text>
      </Box>
      <Stack spacing="sm">
        <Group>
          <Text tt="capitalize" color="dimmed" weight={500} miw={147}>
            {t('common:operationType')}
          </Text>
          <>
            <Icon
              icon={operationIcon({ operationType })}
              size={24}
              color="gray"
            />
            <Text weight={500} tt="uppercase">
              {t(`common:${operationType}`)}
            </Text>
          </>
        </Group>
        {children}
      </Stack>

      <Group mt="lg" position="center">
        <Button
          fullWidth
          radius="lg"
          variant="filled"
          loading={isLoading}
          onClick={onClick}
        >
          {t('common:confirm')}
        </Button>

      </Group>
    </Modal>
  );
}

export default TransactionConfirmModal;
