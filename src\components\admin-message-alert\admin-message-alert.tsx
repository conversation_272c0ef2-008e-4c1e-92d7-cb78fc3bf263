import { Alert, useMantineTheme } from '@mantine/core';
import { useMediaQuery } from '@mantine/hooks';
import { IconAlertCircle } from '@tabler/icons-react';
import useTranslation from 'next-translate/useTranslation';
import React from 'react';
import Showdown from 'showdown';

function MarkdownRenderer({ markdown }: { markdown: string }) {
  const theme = useMantineTheme();
  const isMobileScreen = useMediaQuery(
    `(max-width: ${theme.breakpoints.xs})`,
    true,
    {
      getInitialValueInEffect: false,
    },
  );
  const converter = new Showdown.Converter({
    simplifiedAutoLink: true,
    tables: true,
    strikethrough: true,
    tasklists: true,
  });

  const html = converter.makeHtml(markdown);

  return (
    <div
      // eslint-disable-next-line react/no-danger
      dangerouslySetInnerHTML={{
        __html: html.replace(
          /<img([^>]+)src="([^"]+)"([^>]*)\/?>/g,
          (match, p1, p2, p3) => `<a href="${p2}" target="_blank" rel="noopener noreferrer"><img ${p1} src="${p2}" ${p3} style="width:${
            isMobileScreen ? 270 : 320
          }px;height:100%;margin:5px auto" /></a>`,
        ),
      }}
    />
  );
}
interface AdminMessageAlertProps {
  adminMessage: string;
}
function AdminMessageAlert({ adminMessage }: AdminMessageAlertProps) {
  const { t } = useTranslation();

  return (
    <Alert
      sx={{ whiteSpace: 'pre-line' }}
      icon={<IconAlertCircle size="1rem" />}
      title={t('common:adminMessage')}
      color="yellow"
    >
      <MarkdownRenderer markdown={adminMessage} />
    </Alert>
  );
}

export default AdminMessageAlert;
